pipeline {
    agent none
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 1, unit: 'HOURS')
    }
    
    environment {
        VERSION = sh(script: 'echo $(date +%Y.%m.%d).${BUILD_NUMBER}', returnStdout: true).trim()
    }
    
    stages {
        stage('Test') {
            parallel {
                stage('Test Encoder') {
                    agent {
                        docker {
                            image 'python:3.9'
                        }
                    }
                    steps {
                        dir('megacode-encoder') {
                            sh 'pip install -r requirements.txt'
                            sh 'pip install pytest pytest-cov'
                            sh 'python -m pytest --cov=megacode tests/'
                        }
                    }
                    post {
                        always {
                            junit 'megacode-encoder/test-results/*.xml'
                        }
                    }
                }
                
                stage('Test Scanner') {
                    agent {
                        docker {
                            image 'python:3.9'
                        }
                    }
                    steps {
                        dir('megacode-scanner') {
                            sh 'pip install -r requirements.txt'
                            sh 'pip install pytest pytest-cov'
                            sh 'python -m pytest --cov=megacode tests/'
                        }
                    }
                    post {
                        always {
                            junit 'megacode-scanner/test-results/*.xml'
                        }
                    }
                }
                
                stage('Test Backend') {
                    agent {
                        docker {
                            image 'node:14'
                        }
                    }
                    steps {
                        dir('megacode-backend') {
                            sh 'npm install'
                            sh 'npm test'
                        }
                    }
                    post {
                        always {
                            junit 'megacode-backend/test-results/*.xml'
                        }
                    }
                }
                
                stage('Test Desktop') {
                    agent {
                        docker {
                            image 'python:3.9'
                        }
                    }
                    steps {
                        dir('megacode-desktop') {
                            sh 'apt-get update && apt-get install -y libgl1-mesa-glx libxkbcommon-x11-0 libdbus-1-3 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-randr0 libxcb-render-util0 libxcb-xinerama0 libxcb-xkb1 libxkbcommon-x11-0'
                            sh 'pip install -r requirements.txt'
                            sh 'pip install pytest pytest-cov'
                            sh 'python -m pytest tests/'
                        }
                    }
                }
                
                stage('Test Mobile') {
                    agent {
                        docker {
                            image 'cirrusci/flutter:stable'
                        }
                    }
                    steps {
                        dir('megacode-mobile') {
                            sh 'flutter pub get'
                            sh 'flutter test'
                        }
                    }
                }
            }
        }
        
        stage('Build') {
            parallel {
                stage('Build Desktop - Windows') {
                    agent {
                        label 'windows'
                    }
                    steps {
                        dir('megacode-desktop') {
                            bat 'scripts\\build_windows.bat'
                            archiveArtifacts artifacts: 'dist/MegaCodeDesktop-Setup.exe', fingerprint: true
                        }
                    }
                }
                
                stage('Build Desktop - macOS') {
                    agent {
                        label 'macos'
                    }
                    steps {
                        dir('megacode-desktop') {
                            sh 'chmod +x scripts/build_macos.sh'
                            sh './scripts/build_macos.sh'
                            archiveArtifacts artifacts: 'dist/MegaCode Desktop.dmg', fingerprint: true
                        }
                    }
                }
                
                stage('Build Desktop - Linux') {
                    agent {
                        label 'linux'
                    }
                    steps {
                        dir('megacode-desktop') {
                            sh 'chmod +x scripts/build_linux.sh'
                            sh './scripts/build_linux.sh'
                            archiveArtifacts artifacts: 'dist/MegaCodeDesktop.AppImage', fingerprint: true
                        }
                    }
                }
                
                stage('Build Mobile - Android') {
                    agent {
                        docker {
                            image 'cirrusci/flutter:stable'
                        }
                    }
                    steps {
                        dir('megacode-mobile') {
                            sh 'flutter pub get'
                            sh 'flutter build appbundle --release'
                            archiveArtifacts artifacts: 'build/app/outputs/bundle/release/app-release.aab', fingerprint: true
                        }
                    }
                }
                
                stage('Build Backend') {
                    agent {
                        docker {
                            image 'node:14'
                        }
                    }
                    steps {
                        dir('megacode-backend') {
                            sh 'npm install'
                            sh 'npm run build'
                            sh 'docker build -t megacode-backend:${VERSION} .'
                        }
                    }
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            agent {
                label 'deploy'
            }
            steps {
                echo 'Deploying to staging environment...'
                
                // Deploy backend to staging
                dir('megacode-backend') {
                    sh 'docker tag megacode-backend:${VERSION} megacode-registry.example.com/megacode-backend:staging'
                    sh 'docker push megacode-registry.example.com/megacode-backend:staging'
                    sh 'kubectl apply -f kubernetes/staging/'
                }
                
                // Upload desktop builds to staging server
                sh 'scp megacode-desktop/dist/MegaCodeDesktop-Setup.exe <EMAIL>:/var/www/downloads/staging/'
                sh 'scp "megacode-desktop/dist/MegaCode Desktop.dmg" <EMAIL>:/var/www/downloads/staging/'
                sh 'scp megacode-desktop/dist/MegaCodeDesktop.AppImage <EMAIL>:/var/www/downloads/staging/'
                
                // Upload mobile builds to app distribution platforms
                sh 'fastlane android deploy_staging'
                sh 'fastlane ios deploy_staging'
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            agent {
                label 'deploy'
            }
            steps {
                // Manual approval step
                input message: 'Deploy to production?', ok: 'Deploy'
                
                echo 'Deploying to production environment...'
                
                // Deploy backend to production
                dir('megacode-backend') {
                    sh 'docker tag megacode-backend:${VERSION} megacode-registry.example.com/megacode-backend:production'
                    sh 'docker push megacode-registry.example.com/megacode-backend:production'
                    sh 'kubectl apply -f kubernetes/production/'
                }
                
                // Upload desktop builds to production server
                sh 'scp megacode-desktop/dist/MegaCodeDesktop-Setup.exe <EMAIL>:/var/www/downloads/'
                sh 'scp "megacode-desktop/dist/MegaCode Desktop.dmg" <EMAIL>:/var/www/downloads/'
                sh 'scp megacode-desktop/dist/MegaCodeDesktop.AppImage <EMAIL>:/var/www/downloads/'
                
                // Upload mobile builds to app stores
                sh 'fastlane android deploy_production'
                sh 'fastlane ios deploy_production'
                
                // Create GitHub release
                sh 'gh release create v${VERSION} --title "MegaCode v${VERSION}" --notes "Release notes for version ${VERSION}"'
            }
        }
    }
    
    post {
        success {
            echo 'Build and deployment successful!'
            slackSend(color: 'good', message: "Build successful: ${env.JOB_NAME} #${env.BUILD_NUMBER} (<${env.BUILD_URL}|Open>)")
        }
        failure {
            echo 'Build or deployment failed!'
            slackSend(color: 'danger', message: "Build failed: ${env.JOB_NAME} #${env.BUILD_NUMBER} (<${env.BUILD_URL}|Open>)")
        }
    }
}
