#!/usr/bin/env python3
"""
Simple test script for MegaCode encoder basic structure.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def main():
    """Test the MegaCode encoder basic structure."""
    logger.info("Testing MegaCode encoder basic structure")
    
    # Print directory structure
    logger.info("Directory structure:")
    for root, dirs, files in os.walk("megacode"):
        level = root.replace("megacode", "").count(os.sep)
        indent = " " * 4 * level
        logger.info(f"{indent}{os.path.basename(root)}/")
        sub_indent = " " * 4 * (level + 1)
        for file in files:
            logger.info(f"{sub_indent}{file}")
    
    logger.info("Test completed successfully")


if __name__ == "__main__":
    main()
