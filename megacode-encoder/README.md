# MegaCode Encoder

The MegaCode Encoder is a Python library and command-line tool for generating high-capacity 2D and 3D codes capable of storing up to 4MB of data (approximately 1 million AI prompt tokens).

## Features

- Compress data using gzip/Brotli
- Apply <PERSON>-Solomon error correction
- Implement Luby Transform fountain codes for resilience
- Generate color-enhanced 2D codes with configurable palettes
- Create mosaic layouts of standard codes (QR, DataMatrix)
- Export to SVG/PDF for printing and STL for 3D printing
- Command-line interface for easy integration

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Command Line

```bash
python -m megacode.cli.encode --input data.txt --output code.svg --mode color --colors 8 --ecc-level H
```

### As a Library

```python
from megacode.compression import compress_data
from megacode.ecc import apply_reed_solomon
from megacode.fountain import generate_fountain_packets
from megacode.symbol import map_bits_to_colors
from megacode.layout import generate_grid
from megacode.renderer import render_svg

# Compress the data
compressed_data = compress_data(input_data, method='brotli')

# Apply Reed-Solomon error correction
ecc_data = apply_reed_solomon(compressed_data, level='H')

# Generate fountain packets
packets = generate_fountain_packets(ecc_data, overhead=0.1)

# Map bits to symbols
symbols = map_bits_to_colors(packets, palette='8-color')

# Generate grid layout
grid = generate_grid(symbols, finder_patterns=True, calibration=True)

# Render to SVG
svg = render_svg(grid, module_size=2)
with open('output.svg', 'w') as f:
    f.write(svg)
```

## Architecture

The encoder consists of several modules:

- `compression`: Data compression using gzip/Brotli
- `ecc`: Reed-Solomon error correction
- `fountain`: Luby Transform fountain codes
- `symbol`: Bit-to-symbol mapping (colors, shapes)
- `layout`: Grid layout and finder patterns
- `renderer`: Output generation (SVG, PDF, STL)
- `cli`: Command-line interface

## License

MIT
