#!/usr/bin/env python3
"""
Test script for MegaCode large file ECC handler.
"""

import os
import time
import logging
import tempfile
import random
from typing import Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Import the module to test
try:
    from megacode.ecc.large_file_ecc import (
        apply_ecc_to_large_file,
        decode_ecc_large_file,
        ECCLevel,
    )
except ImportError:
    # Fall back to direct import if module is not installed
    import sys
    import os.path
    sys.path.append(os.path.abspath(os.path.dirname(__file__)))
    from megacode.ecc.large_file_ecc import (
        apply_ecc_to_large_file,
        decode_ecc_large_file,
        ECCLevel,
    )


def generate_test_file(size_mb: int, pattern: str = "random") -> str:
    """
    Generate a test file of the specified size.
    
    Args:
        size_mb: Size of the file in MB
        pattern: Data pattern ('random', 'sequential', 'repeating')
        
    Returns:
        Path to the generated file
    """
    size_bytes = size_mb * 1024 * 1024
    
    # Create a temporary file
    fd, path = tempfile.mkstemp(suffix=".bin")
    
    try:
        with os.fdopen(fd, "wb") as f:
            if pattern == "random":
                # Generate random data in chunks to avoid memory issues
                chunk_size = 1024 * 1024  # 1MB chunks
                for _ in range(0, size_bytes, chunk_size):
                    chunk = bytes(random.randint(0, 255) for _ in range(min(chunk_size, size_bytes - f.tell())))
                    f.write(chunk)
            elif pattern == "sequential":
                # Generate sequential data
                chunk_size = 1024 * 1024  # 1MB chunks
                for i in range(0, size_bytes, chunk_size):
                    chunk = bytes(j % 256 for j in range(i, min(i + chunk_size, size_bytes)))
                    f.write(chunk)
            elif pattern == "repeating":
                # Generate repeating pattern
                pattern_bytes = b"MegaCode" * 128  # 1KB pattern
                for _ in range(0, size_bytes, len(pattern_bytes)):
                    remaining = size_bytes - f.tell()
                    f.write(pattern_bytes[:min(len(pattern_bytes), remaining)])
            else:
                raise ValueError(f"Unknown pattern: {pattern}")
    except Exception as e:
        os.unlink(path)
        raise RuntimeError(f"Failed to generate test file: {e}") from e
    
    logger.info(f"Generated {size_mb}MB test file at {path}")
    return path


def introduce_errors(file_path: str, error_rate: float = 0.01) -> str:
    """
    Introduce random errors into a file.
    
    Args:
        file_path: Path to the file
        error_rate: Percentage of bytes to corrupt (0.0 to 1.0)
        
    Returns:
        Path to the corrupted file
    """
    # Create output file path
    corrupted_file = file_path + ".corrupted"
    
    # Get file size
    file_size = os.path.getsize(file_path)
    
    # Calculate number of bytes to corrupt
    num_errors = int(file_size * error_rate)
    
    # Generate random positions to corrupt
    positions = random.sample(range(file_size), num_errors)
    
    # Read original file and create corrupted copy
    with open(file_path, "rb") as f_in, open(corrupted_file, "wb") as f_out:
        # Read and write in chunks
        chunk_size = 1024 * 1024  # 1MB chunks
        position = 0
        
        while True:
            chunk = f_in.read(chunk_size)
            if not chunk:
                break
            
            # Convert to bytearray for modification
            chunk_array = bytearray(chunk)
            
            # Corrupt bytes in this chunk
            for i in range(len(chunk_array)):
                if position + i in positions:
                    # Corrupt byte (XOR with random value)
                    chunk_array[i] ^= random.randint(1, 255)
            
            # Write corrupted chunk
            f_out.write(chunk_array)
            position += len(chunk)
    
    logger.info(f"Introduced {num_errors} errors ({error_rate:.2%}) into {corrupted_file}")
    return corrupted_file


def benchmark_ecc(
    input_file: str,
    level: Union[str, int] = ECCLevel.M,
) -> Tuple[str, float, float]:
    """
    Benchmark ECC application and decoding of a file.
    
    Args:
        input_file: Path to input file
        level: Error correction level
        
    Returns:
        Tuple of (output_file, size_ratio, time_taken)
    """
    # Create output file path
    output_file = input_file + ".ecc"
    
    # Measure original file size
    original_size = os.path.getsize(input_file)
    
    # Apply ECC and measure time
    start_time = time.time()
    apply_ecc_to_large_file(input_file, output_file, level=level)
    ecc_time = time.time() - start_time
    
    # Measure ECC file size
    ecc_size = os.path.getsize(output_file)
    size_ratio = ecc_size / original_size
    
    logger.info(
        f"Applied ECC to {original_size / (1024*1024):.2f}MB, resulting in {ecc_size / (1024*1024):.2f}MB "
        f"(ratio: {size_ratio:.2f}) in {ecc_time:.2f}s"
    )
    
    # Create decoded file path
    decoded_file = input_file + ".decoded"
    
    # Decode file and measure time
    start_time = time.time()
    decode_ecc_large_file(output_file, decoded_file)
    decode_time = time.time() - start_time
    
    # Verify decoded file size
    decoded_size = os.path.getsize(decoded_file)
    
    logger.info(
        f"Decoded {ecc_size / (1024*1024):.2f}MB to {decoded_size / (1024*1024):.2f}MB "
        f"in {decode_time:.2f}s"
    )
    
    # Verify data integrity
    if decoded_size != original_size:
        logger.warning(
            f"Size mismatch: original={original_size}, decoded={decoded_size}"
        )
    
    # Verify file contents (for small files only)
    if original_size < 100 * 1024 * 1024:  # 100MB
        with open(input_file, "rb") as f1, open(decoded_file, "rb") as f2:
            if f1.read() == f2.read():
                logger.info("Data integrity check passed")
            else:
                logger.error("Data integrity check failed")
    
    return output_file, size_ratio, ecc_time + decode_time


def test_error_correction(
    input_file: str,
    level: Union[str, int] = ECCLevel.H,
    error_rate: float = 0.01,
) -> bool:
    """
    Test error correction by introducing errors and then decoding.
    
    Args:
        input_file: Path to input file
        level: Error correction level
        error_rate: Percentage of bytes to corrupt
        
    Returns:
        True if error correction was successful, False otherwise
    """
    # Create output file path
    output_file = input_file + ".ecc"
    
    # Apply ECC
    apply_ecc_to_large_file(input_file, output_file, level=level)
    
    # Introduce errors
    corrupted_file = introduce_errors(output_file, error_rate=error_rate)
    
    # Create decoded file path
    decoded_file = input_file + ".decoded"
    
    # Decode file
    try:
        decode_ecc_large_file(corrupted_file, decoded_file)
    except Exception as e:
        logger.error(f"Decoding failed: {e}")
        return False
    
    # Verify data integrity
    with open(input_file, "rb") as f1, open(decoded_file, "rb") as f2:
        original_data = f1.read()
        decoded_data = f2.read()
        
        if len(original_data) != len(decoded_data):
            logger.error(
                f"Size mismatch: original={len(original_data)}, decoded={len(decoded_data)}"
            )
            return False
        
        # Count differences
        diff_count = sum(1 for a, b in zip(original_data, decoded_data) if a != b)
        
        if diff_count == 0:
            logger.info("Error correction successful: all errors corrected")
            return True
        else:
            logger.error(f"Error correction failed: {diff_count} uncorrected errors")
            return False


def test_large_file_ecc():
    """Test the large file ECC handler with various file sizes and error rates."""
    test_files = []
    
    try:
        # Test with small file (1MB)
        small_file = generate_test_file(1, pattern="random")
        test_files.append(small_file)
        
        # Test with medium file (5MB)
        medium_file = generate_test_file(5, pattern="sequential")
        test_files.append(medium_file)
        
        # Benchmark ECC levels
        for file_path in test_files:
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            logger.info(f"Testing {file_size:.2f}MB file")
            
            # Test with different ECC levels
            for level in [ECCLevel.L, ECCLevel.M, ECCLevel.Q, ECCLevel.H]:
                logger.info(f"Testing ECC level {level}")
                output_file, ratio, time_taken = benchmark_ecc(
                    file_path, level=level
                )
                test_files.append(output_file)
                test_files.append(file_path + ".decoded")
                
                logger.info(
                    f"Results for level {level}: ratio={ratio:.2f}, time={time_taken:.2f}s"
                )
        
        # Test error correction
        logger.info("Testing error correction")
        small_test_file = generate_test_file(0.1, pattern="random")  # 100KB
        test_files.append(small_test_file)
        
        for level, error_rate in [
            (ECCLevel.L, 0.001),  # 0.1% errors
            (ECCLevel.M, 0.01),   # 1% errors
            (ECCLevel.Q, 0.05),   # 5% errors
            (ECCLevel.H, 0.1),    # 10% errors
        ]:
            logger.info(f"Testing level {level} with {error_rate:.1%} errors")
            success = test_error_correction(
                small_test_file, level=level, error_rate=error_rate
            )
            test_files.append(small_test_file + ".ecc")
            test_files.append(small_test_file + ".ecc.corrupted")
            test_files.append(small_test_file + ".decoded")
            
            logger.info(f"Error correction {'successful' if success else 'failed'}")
    
    finally:
        # Clean up test files
        for file_path in test_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.debug(f"Removed test file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to remove test file {file_path}: {e}")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode large file ECC handler")
    
    # Set random seed for reproducibility
    random.seed(42)
    
    test_large_file_ecc()
    
    logger.info("All tests completed")


if __name__ == "__main__":
    main()
