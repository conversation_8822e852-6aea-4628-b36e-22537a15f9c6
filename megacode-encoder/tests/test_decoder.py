"""
Tests for the decoder module.
"""

import os
import unittest
import tempfile
from unittest.mock import patch, MagicMock

from megacode.decoding.decoder import Decoder


class TestDecoder(unittest.TestCase):
    """Tests for the Decoder class."""

    def setUp(self):
        """Set up test fixtures."""
        self.decoder = Decoder()

        # Create a sample test file
        self.test_data = b"Hello, world!"
        self.test_file = tempfile.NamedTemporaryFile(delete=False)
        self.test_file.write(self.test_data)
        self.test_file.close()

    def tearDown(self):
        """Tear down test fixtures."""
        os.unlink(self.test_file.name)

    def test_decode_empty_data(self):
        """Test decoding empty data."""
        result = self.decoder.decode_data(b"")
        self.assertFalse(result["success"])
        self.assertIsNone(result["data"])
        self.assertIsNotNone(result["error_message"])

    def test_decode_raw_data(self):
        """Test decoding raw data without fountain packets."""
        result = self.decoder.decode_data(self.test_data)
        self.assertTrue(result["success"])
        self.assertEqual(result["data"], self.test_data)
        # The format can be 'text' or 'raw' depending on the implementation
        self.assertIn(result["metadata"]["format"], ["raw", "text"])

    def test_parse_fountain_packets_raw_text(self):
        """Test parsing raw text data."""
        text_data = b"This is a test text"
        packets = self.decoder._parse_fountain_packets(text_data)
        self.assertEqual(len(packets), 1)
        self.assertEqual(packets[0]["format"], "raw_text")
        self.assertEqual(packets[0]["data"], text_data)

    def test_parse_fountain_packets_raw_binary(self):
        """Test parsing raw binary data."""
        binary_data = bytes([0x00, 0x01, 0x02, 0x03, 0xFF])
        packets = self.decoder._parse_fountain_packets(binary_data)
        self.assertEqual(len(packets), 1)
        self.assertEqual(packets[0]["format"], "raw_binary")
        self.assertEqual(packets[0]["data"], binary_data)

    @patch('megacode.decoding.decoder.logger')
    def test_decode_with_error(self, mock_logger):
        """Test decoding with an error."""
        # Create a decoder that raises an exception
        with patch.object(Decoder, '_parse_fountain_packets', side_effect=Exception("Test error")):
            result = self.decoder.decode_data(self.test_data)
            self.assertFalse(result["success"])
            self.assertIsNone(result["data"])
            self.assertIn("Test error", result["error_message"])
            mock_logger.error.assert_called()

    def test_decode_fountain_packets_raw(self):
        """Test decoding raw data packets."""
        packets = [{"format": "raw_text", "data": b"Test data"}]
        result = self.decoder._decode_fountain_packets(packets)
        self.assertEqual(result, b"Test data")

    def test_decode_fountain_packets_legacy(self):
        """Test decoding legacy format packets."""
        packets = [{
            "format": "legacy",
            "seed": 123,
            "block_indices": [0],
            "total_blocks": 0,
            "original_size": 0,
            "data": b"Legacy data"
        }]
        result = self.decoder._decode_fountain_packets(packets)
        self.assertEqual(result, b"Legacy data")

    def test_decode_fountain_packets_standard(self):
        """Test decoding standard format packets with one block."""
        packets = [{
            "format": "standard",
            "seed": 123,
            "block_indices": [0],
            "total_blocks": 1,
            "original_size": 10,
            "data": b"0123456789"
        }]
        result = self.decoder._decode_fountain_packets(packets)
        self.assertEqual(result, b"0123456789")

    def test_is_text(self):
        """Test if data is text."""
        # Add the _is_text method to the Decoder class for testing
        def is_text(data):
            try:
                text = data.decode("utf-8")
                # Check if all characters are printable or whitespace
                return all(c.isprintable() or c.isspace() for c in text)
            except UnicodeDecodeError:
                return False

        # Monkey patch the method for testing
        self.decoder._is_text = is_text

        self.assertTrue(self.decoder._is_text(b"Hello, world!"))
        self.assertFalse(self.decoder._is_text(bytes([0x00, 0x01, 0x02])))


if __name__ == "__main__":
    unittest.main()
