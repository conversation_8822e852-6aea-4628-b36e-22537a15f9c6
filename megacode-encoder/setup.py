from setuptools import setup, find_packages

setup(
    name="megacode-encoder",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "numpy>=1.20.0",
        "pillow>=8.0.0",
        "opencv-python>=4.5.0",
        "scikit-image>=0.18.0",
        "reedsolo>=1.5.0",
        "svgwrite>=1.4.0",
        "numpy-stl>=2.16.0",
        "brotli>=1.0.9",
        "click>=8.0.0",
        "tqdm>=4.60.0",
        "matplotlib>=3.4.0",
    ],
    entry_points={
        "console_scripts": [
            "megacode-encode=megacode.cli.encode:main",
        ],
    },
    author="MegaCode Team",
    author_email="<EMAIL>",
    description="High-capacity 2D and 3D code generator",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/megacode/megacode-encoder",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
)
