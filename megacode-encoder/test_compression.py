#!/usr/bin/env python3
"""
Test script for MegaCode compression module.
"""

import os
import sys
import logging
import gzip
import struct
from megacode.compression.compressor import (
    compress_data,
    decompress_data,
    segment_data,
    reassemble_segments,
    COMPRESSION_GZIP,
    COMPRESSION_NONE,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def test_compression():
    """Test compression and decompression."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the compression module."
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Test compression with gzip
    compressed = compress_data(test_data, method=COMPRESSION_GZIP)
    logger.info(f"Compressed data (gzip): {len(compressed)} bytes")
    
    # Test decompression
    decompressed = decompress_data(compressed)
    logger.info(f"Decompressed data: {len(decompressed)} bytes")
    
    # Verify data integrity
    assert decompressed == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")
    
    # Test no compression
    uncompressed = compress_data(test_data, method=COMPRESSION_NONE)
    logger.info(f"Uncompressed data: {len(uncompressed)} bytes")
    
    # Test decompression of uncompressed data
    decompressed_uncompressed = decompress_data(uncompressed)
    logger.info(f"Decompressed uncompressed data: {len(decompressed_uncompressed)} bytes")
    
    # Verify data integrity
    assert decompressed_uncompressed == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")


def test_segmentation():
    """Test data segmentation and reassembly."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the segmentation module."
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Test segmentation with small segment size
    segment_size = 10
    segments = segment_data(test_data, segment_size=segment_size)
    logger.info(f"Segmented into {len(segments)} segments of size {segment_size}")
    
    # Verify segment sizes
    for i, segment in enumerate(segments):
        logger.info(f"Segment {i}: {len(segment['data'])} bytes")
        if i < len(segments) - 1:
            assert len(segment["data"]) == segment_size, f"Segment {i} size mismatch"
        else:
            # Last segment may be smaller
            assert len(segment["data"]) <= segment_size, f"Last segment size mismatch"
    
    # Test reassembly
    reassembled = reassemble_segments(segments)
    logger.info(f"Reassembled data: {len(reassembled)} bytes")
    
    # Verify data integrity
    assert reassembled == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode compression module")
    
    test_compression()
    test_segmentation()
    
    logger.info("All tests completed successfully")


if __name__ == "__main__":
    main()
