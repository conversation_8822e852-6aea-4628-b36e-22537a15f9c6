"""
Large file error correction module for MegaCode.

This module provides optimized functions for applying Reed-Solomon error correction
to large files (4MB+) with efficient memory usage and parallel processing.
"""

import os
import io
import struct
import logging
import multiprocessing
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from typing import List, Dict, Union, Tuple, Optional, BinaryIO, Callable, Any

import reedsolo

from .fec import (
    apply_reed_solomon,
    decode_reed_solomon,
    ECCLevel,
    RSBlock,
)

# Configure logging
logger = logging.getLogger(__name__)

# Constants
CHUNK_SIZE = 1 * 1024 * 1024  # 1MB chunks for parallel processing
MAX_WORKERS = max(1, multiprocessing.cpu_count() - 1)  # Leave one CPU free


def apply_ecc_to_large_file(
    input_file: Union[str, BinaryIO],
    output_file: Union[str, BinaryIO, None] = None,
    level: Union[str, int] = ECCLevel.M,
    chunk_size: int = CHUNK_SIZE,
    max_workers: int = MAX_WORKERS,
) -> Optional[bytes]:
    """
    Apply Reed-Solomon error correction to a large file using parallel processing.
    
    Args:
        input_file: Path to input file or file-like object
        output_file: Path to output file or file-like object (if None, returns ECC data)
        level: Error correction level (L, M, Q, H) or ECCLevel enum
        chunk_size: Size of chunks for parallel processing
        max_workers: Maximum number of worker processes
        
    Returns:
        ECC data as bytes if output_file is None, otherwise None
        
    Raises:
        ValueError: If the input or output file is invalid
        IOError: If there's an error reading or writing files
        RuntimeError: If ECC application fails
    """
    # Validate input
    if input_file is None:
        raise ValueError("Input file cannot be None")
    
    # Convert string level to enum if needed
    if isinstance(level, str):
        try:
            level = getattr(ECCLevel, level)
        except AttributeError:
            raise ValueError(f"Invalid ECC level: {level}. Use L, M, Q, or H.")
    
    # Open input file if string path provided
    input_file_obj = None
    if isinstance(input_file, str):
        try:
            input_file_obj = open(input_file, 'rb')
        except IOError as e:
            logger.error(f"Failed to open input file {input_file}: {e}")
            raise IOError(f"Failed to open input file {input_file}: {e}") from e
    else:
        input_file_obj = input_file
    
    # Open output file if string path provided
    output_file_obj = None
    if isinstance(output_file, str):
        try:
            output_file_obj = open(output_file, 'wb')
        except IOError as e:
            if input_file_obj and isinstance(input_file, str):
                input_file_obj.close()
            logger.error(f"Failed to open output file {output_file}: {e}")
            raise IOError(f"Failed to open output file {output_file}: {e}") from e
    else:
        output_file_obj = output_file
    
    # Create in-memory buffer if no output file provided
    if output_file_obj is None:
        output_buffer = io.BytesIO()
        output_file_obj = output_buffer
    
    try:
        # Get file size
        if isinstance(input_file, str):
            file_size = os.path.getsize(input_file)
        else:
            # Try to get file size from file object
            current_pos = input_file_obj.tell()
            input_file_obj.seek(0, os.SEEK_END)
            file_size = input_file_obj.tell()
            input_file_obj.seek(current_pos)
        
        logger.info(f"Applying ECC to file of size {file_size} bytes")
        
        # Determine number of chunks
        num_chunks = (file_size + chunk_size - 1) // chunk_size
        
        # Adjust max_workers based on number of chunks
        max_workers = min(max_workers, num_chunks)
        
        # Write header with original size, ECC level, and chunk count
        header = struct.pack(
            "!QBI",  # 8-byte size, 1-byte ECC level, 4-byte chunk count
            file_size,
            level,
            num_chunks,
        )
        output_file_obj.write(header)
        
        # Process file in chunks using parallel processing
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit ECC tasks
            futures = []
            for chunk_id in range(num_chunks):
                # Read chunk
                chunk_data = input_file_obj.read(chunk_size)
                if not chunk_data:
                    break
                
                # Submit ECC task
                future = executor.submit(
                    _apply_ecc_to_chunk,
                    chunk_data,
                    level,
                    chunk_id,
                    num_chunks,
                )
                futures.append(future)
            
            # Process results as they complete
            for future in as_completed(futures):
                try:
                    ecc_blocks_bytes, chunk_id = future.result()
                    
                    # Write chunk size and chunk data
                    chunk_header = struct.pack("!II", chunk_id, len(ecc_blocks_bytes))
                    output_file_obj.write(chunk_header)
                    output_file_obj.write(ecc_blocks_bytes)
                    
                    logger.debug(f"Wrote ECC chunk {chunk_id} ({len(ecc_blocks_bytes)} bytes)")
                except Exception as e:
                    logger.error(f"Chunk ECC application failed: {e}")
                    raise RuntimeError(f"Chunk ECC application failed: {e}") from e
        
        # Return ECC data if no output file was provided
        if isinstance(output_file, type(None)):
            return output_buffer.getvalue()
        
        return None
    
    finally:
        # Close files if we opened them
        if isinstance(input_file, str) and input_file_obj:
            input_file_obj.close()
        
        if isinstance(output_file, str) and output_file_obj:
            output_file_obj.close()


def decode_ecc_large_file(
    input_file: Union[str, BinaryIO],
    output_file: Union[str, BinaryIO, None] = None,
    max_workers: int = MAX_WORKERS,
) -> Optional[bytes]:
    """
    Decode Reed-Solomon error correction from a large file using parallel processing.
    
    Args:
        input_file: Path to input file or file-like object
        output_file: Path to output file or file-like object (if None, returns decoded data)
        max_workers: Maximum number of worker processes
        
    Returns:
        Decoded data as bytes if output_file is None, otherwise None
        
    Raises:
        ValueError: If the input or output file is invalid
        IOError: If there's an error reading or writing files
        RuntimeError: If ECC decoding fails
    """
    # Validate input
    if input_file is None:
        raise ValueError("Input file cannot be None")
    
    # Open input file if string path provided
    input_file_obj = None
    if isinstance(input_file, str):
        try:
            input_file_obj = open(input_file, 'rb')
        except IOError as e:
            logger.error(f"Failed to open input file {input_file}: {e}")
            raise IOError(f"Failed to open input file {input_file}: {e}") from e
    else:
        input_file_obj = input_file
    
    # Open output file if string path provided
    output_file_obj = None
    if isinstance(output_file, str):
        try:
            output_file_obj = open(output_file, 'wb')
        except IOError as e:
            if input_file_obj and isinstance(input_file, str):
                input_file_obj.close()
            logger.error(f"Failed to open output file {output_file}: {e}")
            raise IOError(f"Failed to open output file {output_file}: {e}") from e
    else:
        output_file_obj = output_file
    
    # Create in-memory buffer if no output file provided
    if output_file_obj is None:
        output_buffer = io.BytesIO()
        output_file_obj = output_buffer
    
    try:
        # Read header
        header_size = struct.calcsize("!QBI")
        header_data = input_file_obj.read(header_size)
        
        if len(header_data) < header_size:
            raise ValueError("Invalid file format: header too small")
        
        original_size, ecc_level, num_chunks = struct.unpack("!QBI", header_data)
        
        logger.info(f"Decoding ECC file to {original_size} bytes ({num_chunks} chunks)")
        
        # Read chunk headers and data
        chunks = []
        for _ in range(num_chunks):
            # Read chunk header
            chunk_header_size = struct.calcsize("!II")
            chunk_header = input_file_obj.read(chunk_header_size)
            
            if len(chunk_header) < chunk_header_size:
                raise ValueError("Invalid file format: chunk header too small")
            
            chunk_id, chunk_size = struct.unpack("!II", chunk_header)
            
            # Read chunk data
            chunk_data = input_file_obj.read(chunk_size)
            
            if len(chunk_data) < chunk_size:
                raise ValueError(f"Invalid file format: chunk {chunk_id} data too small")
            
            chunks.append((chunk_id, chunk_data))
        
        # Process chunks in parallel
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit decoding tasks
            futures = []
            for chunk_id, chunk_data in chunks:
                future = executor.submit(
                    _decode_ecc_chunk,
                    chunk_data,
                    chunk_id,
                )
                futures.append(future)
            
            # Sort chunks by ID
            decoded_chunks = []
            for future in as_completed(futures):
                try:
                    decoded_chunk, chunk_id = future.result()
                    decoded_chunks.append((chunk_id, decoded_chunk))
                except Exception as e:
                    logger.error(f"Chunk ECC decoding failed: {e}")
                    raise RuntimeError(f"Chunk ECC decoding failed: {e}") from e
            
            # Sort chunks by ID and write to output
            for chunk_id, chunk_data in sorted(decoded_chunks, key=lambda x: x[0]):
                output_file_obj.write(chunk_data)
                logger.debug(f"Wrote decoded chunk {chunk_id} ({len(chunk_data)} bytes)")
        
        # Truncate to original size if needed
        if hasattr(output_file_obj, 'tell') and hasattr(output_file_obj, 'seek') and hasattr(output_file_obj, 'truncate'):
            current_size = output_file_obj.tell()
            if current_size > original_size:
                output_file_obj.seek(original_size)
                output_file_obj.truncate()
                logger.info(f"Truncated output to original size: {original_size} bytes")
        
        # Return decoded data if no output file was provided
        if isinstance(output_file, type(None)):
            return output_buffer.getvalue()
        
        return None
    
    finally:
        # Close files if we opened them
        if isinstance(input_file, str) and input_file_obj:
            input_file_obj.close()
        
        if isinstance(output_file, str) and output_file_obj:
            output_file_obj.close()


def _apply_ecc_to_chunk(
    chunk_data: bytes,
    level: int,
    chunk_id: int,
    total_chunks: int,
) -> Tuple[bytes, int]:
    """
    Apply Reed-Solomon error correction to a single chunk of data.
    
    Args:
        chunk_data: Chunk data to apply ECC to
        level: Error correction level
        chunk_id: Chunk ID
        total_chunks: Total number of chunks
        
    Returns:
        Tuple of (ecc_blocks_bytes, chunk_id)
    """
    try:
        # Apply Reed-Solomon error correction
        rs_blocks = apply_reed_solomon(chunk_data, level=level)
        
        # Convert blocks to bytes
        blocks_bytes = b"".join(block.to_bytes() for block in rs_blocks)
        
        return blocks_bytes, chunk_id
    except Exception as e:
        logger.error(f"Failed to apply ECC to chunk {chunk_id}: {e}")
        raise


def _decode_ecc_chunk(
    chunk_data: bytes,
    chunk_id: int,
) -> Tuple[bytes, int]:
    """
    Decode Reed-Solomon error correction from a single chunk of data.
    
    Args:
        chunk_data: Chunk data with ECC
        chunk_id: Chunk ID
        
    Returns:
        Tuple of (decoded_chunk, chunk_id)
    """
    try:
        # Parse RS blocks
        rs_blocks = []
        offset = 0
        
        while offset < len(chunk_data):
            try:
                block = RSBlock.from_bytes(chunk_data[offset:])
                rs_blocks.append(block)
                # Move to next block
                offset += len(block.to_bytes())
            except Exception as e:
                logger.error(f"Error parsing RS block at offset {offset}: {e}")
                # Skip to next potential block
                offset += 1
        
        # Decode RS blocks
        decoded_data = decode_reed_solomon(rs_blocks)
        
        return decoded_data, chunk_id
    except Exception as e:
        logger.error(f"Failed to decode ECC from chunk {chunk_id}: {e}")
        raise
