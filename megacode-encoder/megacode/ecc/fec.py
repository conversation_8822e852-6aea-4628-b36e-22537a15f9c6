"""
Forward Error Correction module for MegaCode.

This module provides Reed-Solomon error correction coding similar to QR codes,
with configurable error correction levels.
"""

import logging
import enum
from typing import List, Dict, Union, Tuple, Optional
import reedsolo
import struct

# Configure logging
logger = logging.getLogger(__name__)

# Maximum number of errors that can be corrected
MAX_ERROR_CORRECTION = 30  # percent


class ECCLevel(enum.Enum):
    """Error correction levels, similar to QR code."""
    
    L = 7  # ~7% error correction
    M = 15  # ~15% error correction
    Q = 25  # ~25% error correction
    H = 30  # ~30% error correction


class RSBlock:
    """Reed-Solomon encoded block with metadata."""
    
    def __init__(
        self,
        data: bytes,
        ecc: bytes,
        ecc_level: ECCLevel,
        block_id: int,
        total_blocks: int,
    ):
        self.data = data
        self.ecc = ecc
        self.ecc_level = ecc_level
        self.block_id = block_id
        self.total_blocks = total_blocks
    
    def to_bytes(self) -> bytes:
        """Convert the block to bytes for storage/transmission."""
        # Header: ECC level (1 byte), block ID (2 bytes), total blocks (2 bytes)
        header = struct.pack(
            "!BHH",
            self.ecc_level.value,
            self.block_id,
            self.total_blocks,
        )
        
        # Data length (2 bytes) and ECC length (1 byte)
        lengths = struct.pack("!HB", len(self.data), len(self.ecc))
        
        # Combine all parts
        return header + lengths + self.data + self.ecc
    
    @classmethod
    def from_bytes(cls, data: bytes) -> "RSBlock":
        """Create a block from its byte representation."""
        # Parse header
        header_size = struct.calcsize("!BHHB")
        ecc_level_value, block_id, total_blocks, ecc_length = struct.unpack(
            "!BHHB", data[:header_size]
        )
        
        # Convert ECC level value to enum
        ecc_level = next(level for level in ECCLevel if level.value == ecc_level_value)
        
        # Extract data and ECC
        data_start = header_size
        data_end = len(data) - ecc_length
        block_data = data[data_start:data_end]
        ecc_data = data[data_end:]
        
        return cls(
            data=block_data,
            ecc=ecc_data,
            ecc_level=ecc_level,
            block_id=block_id,
            total_blocks=total_blocks,
        )


def apply_reed_solomon(
    data: bytes,
    level: Union[str, ECCLevel] = ECCLevel.M,
    block_size: int = 255,
    nsym: Optional[int] = None,
) -> List[RSBlock]:
    """
    Apply Reed-Solomon error correction to data.
    
    Args:
        data: Input data bytes
        level: Error correction level (L, M, Q, H) or ECCLevel enum
        block_size: Maximum size of each RS block (typically 255 for RS(255,k))
        nsym: Number of error correction symbols (calculated from level if None)
    
    Returns:
        List of RSBlock objects containing data and ECC
    """
    # Convert string level to enum if needed
    if isinstance(level, str):
        try:
            level = ECCLevel[level]
        except KeyError:
            raise ValueError(f"Invalid ECC level: {level}. Use L, M, Q, or H.")
    
    # Calculate number of ECC symbols if not specified
    if nsym is None:
        # Maximum data symbols in a block
        max_data_symbols = block_size - 1  # Reserve 1 byte for metadata
        
        # Calculate ECC symbols based on level percentage
        nsym = int(max_data_symbols * (level.value / 100))
        
        # Ensure minimum number of ECC symbols
        nsym = max(nsym, 2)
        
        logger.info(f"Using {nsym} ECC symbols for level {level.name}")
    
    # Maximum data symbols per block
    max_data_symbols = block_size - nsym
    
    # Initialize Reed-Solomon codec
    rs = reedsolo.RSCodec(nsym)
    
    # Split data into blocks
    blocks = []
    total_blocks = (len(data) + max_data_symbols - 1) // max_data_symbols
    
    logger.info(f"Encoding {len(data)} bytes into {total_blocks} RS blocks")
    
    for i in range(total_blocks):
        # Extract block data
        start = i * max_data_symbols
        end = min(start + max_data_symbols, len(data))
        block_data = data[start:end]
        
        # Pad if necessary
        if len(block_data) < max_data_symbols:
            block_data = block_data + bytes([0] * (max_data_symbols - len(block_data)))
        
        # Encode with Reed-Solomon
        encoded = rs.encode(block_data)
        
        # Extract ECC portion (last nsym bytes)
        ecc = encoded[-nsym:]
        
        # Create block
        block = RSBlock(
            data=block_data,
            ecc=ecc,
            ecc_level=level,
            block_id=i,
            total_blocks=total_blocks,
        )
        
        blocks.append(block)
    
    return blocks


def decode_reed_solomon(blocks: List[RSBlock]) -> bytes:
    """
    Decode Reed-Solomon encoded blocks, correcting errors if possible.
    
    Args:
        blocks: List of RSBlock objects
    
    Returns:
        Decoded and corrected data
    """
    # Sort blocks by ID
    sorted_blocks = sorted(blocks, key=lambda b: b.block_id)
    
    # Check if we have all blocks
    block_ids = [b.block_id for b in sorted_blocks]
    total_blocks = sorted_blocks[0].total_blocks
    expected_ids = set(range(total_blocks))
    missing_ids = expected_ids - set(block_ids)
    
    if missing_ids:
        logger.warning(f"Missing blocks: {missing_ids}")
        raise ValueError(f"Cannot decode: missing {len(missing_ids)} blocks")
    
    # Decode each block
    decoded_data = bytearray()
    
    for block in sorted_blocks:
        # Initialize RS codec based on block's ECC
        nsym = len(block.ecc)
        rs = reedsolo.RSCodec(nsym)
        
        # Combine data and ECC for decoding
        encoded = block.data + block.ecc
        
        try:
            # Attempt to decode and correct errors
            decoded, _, errata_pos = rs.decode(encoded, return_errata_positions=True)
            
            if errata_pos:
                logger.info(f"Corrected {len(errata_pos)} errors in block {block.block_id}")
            
            # Remove padding from the last block
            if block.block_id == total_blocks - 1:
                # Find the first zero byte from the end
                i = len(decoded) - 1
                while i >= 0 and decoded[i] == 0:
                    i -= 1
                
                # Keep data up to that point (inclusive)
                decoded_data.extend(decoded[:i+1])
            else:
                decoded_data.extend(decoded)
                
        except reedsolo.ReedSolomonError as e:
            logger.error(f"Failed to decode block {block.block_id}: {e}")
            raise
    
    return bytes(decoded_data)
