"""
Command-line interface for MegaCode encoder.
"""

import os
import sys
import logging
import click
from typing import Optional, List, Dict, Union, Tuple

from ..compression import compress_data, segment_data
from ..ecc import apply_reed_solomon, ECCLevel
from ..fountain import generate_fountain_packets, FountainPacket
from ..symbol import map_bits_to_colors, get_color_palette
from ..layout import generate_grid
from ..renderer import render_svg, render_png, render_stl


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--input",
    "-i",
    required=True,
    type=click.Path(exists=True, readable=True),
    help="Input file to encode",
)
@click.option(
    "--output",
    "-o",
    required=True,
    type=click.Path(writable=True),
    help="Output file path",
)
@click.option(
    "--format",
    "-f",
    type=click.Choice(["svg", "png", "stl"]),
    default="svg",
    help="Output format",
)
@click.option(
    "--compression",
    type=click.Choice(["gzip", "brotli", "none"]),
    default="brotli",
    help="Compression method",
)
@click.option(
    "--compression-level",
    type=click.IntRange(0, 11),
    default=9,
    help="Compression level (0-9 for gzip, 0-11 for brotli)",
)
@click.option(
    "--ecc-level",
    type=click.Choice(["L", "M", "Q", "H"]),
    default="M",
    help="Error correction level (L=7%, M=15%, Q=25%, H=30%)",
)
@click.option(
    "--fountain-overhead",
    type=float,
    default=0.1,
    help="Fountain code overhead factor (e.g., 0.1 = 10% more packets than blocks)",
)
@click.option(
    "--palette",
    type=click.Choice(["bw", "4-color", "8-color", "16-color"]),
    default="8-color",
    help="Color palette for 2D code",
)
@click.option(
    "--module-size",
    type=int,
    default=10,
    help="Size of each module in pixels/mm",
)
@click.option(
    "--finder-patterns/--no-finder-patterns",
    default=True,
    help="Add finder patterns to the code",
)
@click.option(
    "--calibration-patterns/--no-calibration-patterns",
    default=True,
    help="Add color calibration patterns to the code",
)
@click.option(
    "--base-height",
    type=float,
    default=1.0,
    help="Height of the base in mm (for STL output)",
)
@click.option(
    "--module-height",
    type=float,
    default=0.5,
    help="Height of raised modules in mm (for STL output)",
)
@click.option(
    "--verbose",
    "-v",
    is_flag=True,
    help="Enable verbose logging",
)
def main(
    input: str,
    output: str,
    format: str,
    compression: str,
    compression_level: int,
    ecc_level: str,
    fountain_overhead: float,
    palette: str,
    module_size: int,
    finder_patterns: bool,
    calibration_patterns: bool,
    base_height: float,
    module_height: float,
    verbose: bool,
):
    """
    Encode data into a MegaCode 2D or 3D code.
    """
    # Set logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Read input file
    try:
        with open(input, "rb") as f:
            input_data = f.read()
        logger.info(f"Read {len(input_data)} bytes from {input}")
    except Exception as e:
        logger.error(f"Failed to read input file: {e}")
        sys.exit(1)
    
    # Compress data
    try:
        compressed_data = compress_data(
            input_data,
            method=compression,
            level=compression_level,
        )
        logger.info(
            f"Compressed data: {len(input_data)} -> {len(compressed_data)} bytes "
            f"({len(compressed_data) / len(input_data):.2f}x)"
        )
    except Exception as e:
        logger.error(f"Failed to compress data: {e}")
        sys.exit(1)
    
    # Segment data
    try:
        segments = segment_data(compressed_data)
        logger.info(f"Segmented data into {len(segments)} chunks")
    except Exception as e:
        logger.error(f"Failed to segment data: {e}")
        sys.exit(1)
    
    # Apply Reed-Solomon error correction
    try:
        rs_blocks = []
        for segment in segments:
            segment_blocks = apply_reed_solomon(
                segment["data"],
                level=ecc_level,
            )
            rs_blocks.extend(segment_blocks)
        logger.info(f"Applied Reed-Solomon ECC to {len(rs_blocks)} blocks")
    except Exception as e:
        logger.error(f"Failed to apply Reed-Solomon ECC: {e}")
        sys.exit(1)
    
    # Generate fountain packets
    try:
        # Convert RS blocks to bytes
        rs_data = [block.to_bytes() for block in rs_blocks]
        
        # Generate fountain packets
        packets = generate_fountain_packets(
            rs_data,
            overhead=fountain_overhead,
        )
        logger.info(f"Generated {len(packets)} fountain packets")
    except Exception as e:
        logger.error(f"Failed to generate fountain packets: {e}")
        sys.exit(1)
    
    # Convert packets to bytes
    try:
        packet_data = [packet.to_bytes() for packet in packets]
        
        # Combine all packet data
        combined_data = b"".join(packet_data)
        logger.info(f"Combined packet data: {len(combined_data)} bytes")
    except Exception as e:
        logger.error(f"Failed to convert packets to bytes: {e}")
        sys.exit(1)
    
    # Map bits to colors
    try:
        colors = map_bits_to_colors(combined_data, palette=palette)
        logger.info(f"Mapped data to {len(colors)} colors using {palette} palette")
    except Exception as e:
        logger.error(f"Failed to map bits to colors: {e}")
        sys.exit(1)
    
    # Generate grid layout
    try:
        layout = generate_grid(
            colors,
            finder_patterns=finder_patterns,
            calibration_patterns=calibration_patterns,
        )
        logger.info(f"Generated grid layout of size {layout.width}x{layout.height}")
    except Exception as e:
        logger.error(f"Failed to generate grid layout: {e}")
        sys.exit(1)
    
    # Render output
    try:
        if format == "svg":
            render_svg(layout, module_size=module_size, output_file=output)
        elif format == "png":
            render_png(layout, module_size=module_size, output_file=output)
        elif format == "stl":
            render_stl(
                layout,
                module_size=module_size,
                base_height=base_height,
                module_height=module_height,
                output_file=output,
            )
        logger.info(f"Rendered output to {output}")
    except Exception as e:
        logger.error(f"Failed to render output: {e}")
        sys.exit(1)
    
    logger.info("Encoding completed successfully")


if __name__ == "__main__":
    main()
