"""
Compression module for MegaCode.

This module provides functions for compressing and decompressing data
using various algorithms (gzip, brotli) and segmenting data into chunks.
"""

import gzip
import brotli
import struct
import logging
from typing import List, Dict, Union, Tuple, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Compression methods
COMPRESSION_GZIP = "gzip"
COMPRESSION_BROTLI = "brotli"
COMPRESSION_NONE = "none"

# Default segment size (4KB)
DEFAULT_SEGMENT_SIZE = 4 * 1024


def compress_data(
    data: Union[str, bytes],
    method: str = COMPRESSION_BROTLI,
    level: int = 9,
) -> bytes:
    """
    Compress data using the specified method.

    Args:
        data: The data to compress (string or bytes)
        method: Compression method ('gzip', 'brotli', or 'none')
        level: Compression level (0-9 for gzip, 0-11 for brotli)

    Returns:
        Compressed data as bytes

    Raises:
        ValueError: If the compression method is unsupported or parameters are invalid
        TypeError: If the data type is unsupported
        RuntimeError: If compression fails
    """
    # Validate input
    if data is None:
        raise ValueError("Input data cannot be None")

    # Convert string to bytes if needed
    if isinstance(data, str):
        try:
            data_bytes = data.encode("utf-8")
        except UnicodeEncodeError as e:
            logger.error(f"Failed to encode string to UTF-8: {e}")
            raise ValueError(f"Failed to encode string to UTF-8: {e}") from e
    elif isinstance(data, bytes):
        data_bytes = data
    else:
        raise TypeError(f"Unsupported data type: {type(data)}. Expected str or bytes.")

    # Validate compression level
    if method == COMPRESSION_GZIP and (level < 0 or level > 9):
        logger.warning(f"Invalid gzip compression level: {level}. Using default level 6.")
        level = 6
    elif method == COMPRESSION_BROTLI and (level < 0 or level > 11):
        logger.warning(f"Invalid brotli compression level: {level}. Using default level 9.")
        level = 9

    # Apply compression
    try:
        if method == COMPRESSION_GZIP:
            logger.info(f"Compressing {len(data_bytes)} bytes with gzip (level {level})")
            compressed = gzip.compress(data_bytes, compresslevel=level)
        elif method == COMPRESSION_BROTLI:
            logger.info(f"Compressing {len(data_bytes)} bytes with brotli (level {level})")
            compressed = brotli.compress(data_bytes, quality=level)
        elif method == COMPRESSION_NONE:
            logger.info("No compression applied")
            compressed = data_bytes
        else:
            raise ValueError(f"Unsupported compression method: {method}. "
                            f"Supported methods: {COMPRESSION_GZIP}, {COMPRESSION_BROTLI}, {COMPRESSION_NONE}")
    except Exception as e:
        logger.error(f"Compression failed: {e}")
        raise RuntimeError(f"Compression failed: {e}") from e

    # Add header with original size and compression method
    try:
        header = struct.pack(
            "!IB",
            len(data_bytes),  # Original size (4 bytes)
            {
                COMPRESSION_GZIP: 1,
                COMPRESSION_BROTLI: 2,
                COMPRESSION_NONE: 0,
            }.get(method, 0),  # Compression method (1 byte)
        )
    except struct.error as e:
        logger.error(f"Failed to create header: {e}")
        raise RuntimeError(f"Failed to create header: {e}") from e

    # Calculate compression ratio
    ratio = len(compressed) / len(data_bytes) if len(data_bytes) > 0 else 1.0
    logger.info(
        f"Compression ratio: {ratio:.2f} "
        f"({len(data_bytes)} -> {len(compressed)} bytes)"
    )

    return header + compressed


def decompress_data(compressed_data: bytes) -> bytes:
    """
    Decompress data that was compressed with compress_data.

    Args:
        compressed_data: The compressed data with header

    Returns:
        Decompressed data as bytes

    Raises:
        ValueError: If the compressed data is invalid or the compression method is unsupported
        TypeError: If the input is not bytes
        RuntimeError: If decompression fails
    """
    # Validate input
    if compressed_data is None:
        raise ValueError("Compressed data cannot be None")

    if not isinstance(compressed_data, bytes):
        raise TypeError(f"Unsupported data type: {type(compressed_data)}. Expected bytes.")

    # Extract header
    header_size = struct.calcsize("!IB")
    if len(compressed_data) < header_size:
        error_msg = f"Compressed data is too small to contain a valid header (got {len(compressed_data)} bytes, need at least {header_size})"
        logger.error(error_msg)
        raise ValueError(error_msg)

    try:
        original_size, compression_method = struct.unpack(
            "!IB", compressed_data[:header_size]
        )
    except struct.error as e:
        logger.error(f"Failed to unpack header: {e}")
        raise ValueError(f"Failed to unpack header: {e}") from e

    # Validate original size
    if original_size < 0 or original_size > 100 * 1024 * 1024:  # 100 MB sanity check
        error_msg = f"Invalid original size in header: {original_size} bytes"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Get compressed data without header
    compressed = compressed_data[header_size:]

    if len(compressed) == 0:
        logger.warning("Compressed data is empty after header")
        return b""

    # Apply decompression
    try:
        if compression_method == 1:  # gzip
            logger.info(f"Decompressing {len(compressed)} bytes with gzip")
            decompressed = gzip.decompress(compressed)
        elif compression_method == 2:  # brotli
            logger.info(f"Decompressing {len(compressed)} bytes with brotli")
            decompressed = brotli.decompress(compressed)
        elif compression_method == 0:  # none
            logger.info("No decompression needed")
            decompressed = compressed
        else:
            error_msg = f"Unsupported compression method: {compression_method}"
            logger.error(error_msg)
            raise ValueError(error_msg)
    except (gzip.BadGzipFile, brotli.error, ValueError, OSError) as e:
        logger.error(f"Decompression failed: {e}")
        raise RuntimeError(f"Decompression failed: {e}") from e
    except Exception as e:
        logger.error(f"Unexpected error during decompression: {e}")
        raise RuntimeError(f"Unexpected error during decompression: {e}") from e

    # Verify size
    if len(decompressed) != original_size:
        logger.warning(
            f"Decompressed size ({len(decompressed)}) doesn't match "
            f"original size ({original_size})"
        )
        # Continue anyway, as the data might still be usable

    return decompressed


def segment_data(
    data: bytes, segment_size: int = DEFAULT_SEGMENT_SIZE
) -> List[Dict[str, Union[int, bytes]]]:
    """
    Split data into fixed-size segments with metadata.

    Args:
        data: The data to segment
        segment_size: Maximum size of each segment in bytes

    Returns:
        List of segments, each containing:
        - segment_id: Segment index
        - total_segments: Total number of segments
        - data: Segment data

    Raises:
        ValueError: If the input data is invalid or segment_size is invalid
        TypeError: If the input is not bytes
    """
    # Validate input
    if data is None:
        raise ValueError("Input data cannot be None")

    if not isinstance(data, bytes):
        raise TypeError(f"Unsupported data type: {type(data)}. Expected bytes.")

    # Validate segment size
    if segment_size <= 0:
        logger.warning(f"Invalid segment size: {segment_size}. Using default size {DEFAULT_SEGMENT_SIZE}.")
        segment_size = DEFAULT_SEGMENT_SIZE

    # Handle empty data
    if len(data) == 0:
        logger.warning("Empty data to segment, returning empty list")
        return []

    # Calculate number of segments
    total_segments = (len(data) + segment_size - 1) // segment_size

    # Check if total segments is too large
    if total_segments > 65535:  # 2^16 - 1, max value for a 2-byte unsigned int
        logger.warning(f"Too many segments: {total_segments}. Increasing segment size.")
        # Recalculate segment size to fit within limits
        segment_size = (len(data) + 65000) // 65000  # Aim for slightly fewer segments
        total_segments = (len(data) + segment_size - 1) // segment_size
        logger.info(f"Adjusted segment size to {segment_size} bytes, resulting in {total_segments} segments")

    logger.info(f"Segmenting {len(data)} bytes into {total_segments} segments of max size {segment_size}")

    segments = []
    for i in range(total_segments):
        # Extract segment data
        start = i * segment_size
        end = min(start + segment_size, len(data))
        segment_data = data[start:end]

        # Create segment with metadata
        segment = {
            "segment_id": i,
            "total_segments": total_segments,
            "data": segment_data,
        }
        segments.append(segment)

    return segments


def reassemble_segments(segments: List[Dict[str, Union[int, bytes]]]) -> bytes:
    """
    Reassemble segmented data back into a single byte array.

    Args:
        segments: List of segments as returned by segment_data

    Returns:
        Reassembled data as bytes

    Raises:
        ValueError: If segments are missing or invalid
        TypeError: If the input is not a list of dictionaries
    """
    # Validate input
    if segments is None:
        raise ValueError("Segments cannot be None")

    if not isinstance(segments, list):
        raise TypeError(f"Unsupported data type: {type(segments)}. Expected list of dictionaries.")

    # Check if we have any segments
    if not segments:
        logger.warning("No segments to reassemble, returning empty bytes")
        return b""

    # Validate segment structure
    for i, segment in enumerate(segments):
        if not isinstance(segment, dict):
            raise TypeError(f"Segment {i} is not a dictionary: {type(segment)}")

        required_keys = ["segment_id", "total_segments", "data"]
        for key in required_keys:
            if key not in segment:
                raise ValueError(f"Segment {i} is missing required key: {key}")

        if not isinstance(segment["segment_id"], int):
            raise TypeError(f"Segment {i} has invalid segment_id type: {type(segment['segment_id'])}")

        if not isinstance(segment["total_segments"], int):
            raise TypeError(f"Segment {i} has invalid total_segments type: {type(segment['total_segments'])}")

        if not isinstance(segment["data"], bytes):
            raise TypeError(f"Segment {i} has invalid data type: {type(segment['data'])}")

    # Get total segments from any segment
    total_segments = segments[0]["total_segments"]

    # Validate total_segments consistency
    for i, segment in enumerate(segments):
        if segment["total_segments"] != total_segments:
            logger.warning(f"Segment {i} has inconsistent total_segments: {segment['total_segments']} vs {total_segments}")

    # Sort segments by ID
    try:
        sorted_segments = sorted(segments, key=lambda s: s["segment_id"])
    except Exception as e:
        logger.error(f"Failed to sort segments: {e}")
        raise ValueError(f"Failed to sort segments: {e}") from e

    # Check for missing segments
    segment_ids = [s["segment_id"] for s in sorted_segments]
    expected_ids = set(range(total_segments))
    missing_ids = expected_ids - set(segment_ids)

    if missing_ids:
        # If there are just a few missing segments at the end, we might still be able to recover partial data
        if all(id >= len(sorted_segments) for id in missing_ids) and len(missing_ids) < total_segments // 10:
            logger.warning(f"Missing {len(missing_ids)} trailing segments, but proceeding with partial data")
        else:
            logger.error(f"Missing segments: {missing_ids}")
            raise ValueError(f"Cannot reassemble: missing {len(missing_ids)} segments")

    # Check for duplicate segments
    duplicate_ids = [id for id in segment_ids if segment_ids.count(id) > 1]
    if duplicate_ids:
        logger.warning(f"Duplicate segment IDs found: {set(duplicate_ids)}")
        # Keep only the first occurrence of each segment ID
        unique_segments = []
        seen_ids = set()
        for segment in sorted_segments:
            if segment["segment_id"] not in seen_ids:
                unique_segments.append(segment)
                seen_ids.add(segment["segment_id"])
        sorted_segments = unique_segments

    # Concatenate segment data
    try:
        reassembled = b"".join(segment["data"] for segment in sorted_segments)
        logger.info(f"Reassembled {len(reassembled)} bytes from {len(segments)} segments")
        return reassembled
    except Exception as e:
        logger.error(f"Failed to concatenate segment data: {e}")
        raise RuntimeError(f"Failed to concatenate segment data: {e}") from e
