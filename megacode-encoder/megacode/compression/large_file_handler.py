"""
Large file handler for MegaCode.

This module provides optimized functions for handling large files (4MB+)
with efficient memory usage and parallel processing.
"""

import os
import io
import gzip
import brotli
import struct
import logging
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Union, Tuple, Optional, BinaryIO, Callable, Any

from .compressor import (
    compress_data,
    decompress_data,
    segment_data,
    reassemble_segments,
    COMPRESSION_BROTLI,
    COMPRESSION_GZIP,
    COMPRESSION_NONE,
)

# Configure logging
logger = logging.getLogger(__name__)

# Constants
CHUNK_SIZE = 1 * 1024 * 1024  # 1MB chunks for parallel processing
MAX_WORKERS = max(1, multiprocessing.cpu_count() - 1)  # Leave one CPU free


def compress_large_file(
    input_file: Union[str, BinaryIO],
    output_file: Union[str, BinaryIO, None] = None,
    method: str = COMPRESSION_BROTLI,
    level: int = 9,
    chunk_size: int = CHUNK_SIZE,
    max_workers: int = MAX_WORKERS,
) -> Optional[bytes]:
    """
    Compress a large file using parallel processing.
    
    Args:
        input_file: Path to input file or file-like object
        output_file: Path to output file or file-like object (if None, returns compressed data)
        method: Compression method ('gzip', 'brotli', or 'none')
        level: Compression level
        chunk_size: Size of chunks for parallel processing
        max_workers: Maximum number of worker processes
        
    Returns:
        Compressed data as bytes if output_file is None, otherwise None
        
    Raises:
        ValueError: If the input or output file is invalid
        IOError: If there's an error reading or writing files
        RuntimeError: If compression fails
    """
    # Validate input
    if input_file is None:
        raise ValueError("Input file cannot be None")
    
    # Open input file if string path provided
    input_file_obj = None
    if isinstance(input_file, str):
        try:
            input_file_obj = open(input_file, 'rb')
        except IOError as e:
            logger.error(f"Failed to open input file {input_file}: {e}")
            raise IOError(f"Failed to open input file {input_file}: {e}") from e
    else:
        input_file_obj = input_file
    
    # Open output file if string path provided
    output_file_obj = None
    if isinstance(output_file, str):
        try:
            output_file_obj = open(output_file, 'wb')
        except IOError as e:
            if input_file_obj and isinstance(input_file, str):
                input_file_obj.close()
            logger.error(f"Failed to open output file {output_file}: {e}")
            raise IOError(f"Failed to open output file {output_file}: {e}") from e
    else:
        output_file_obj = output_file
    
    # Create in-memory buffer if no output file provided
    if output_file_obj is None:
        output_buffer = io.BytesIO()
        output_file_obj = output_buffer
    
    try:
        # Get file size
        if isinstance(input_file, str):
            file_size = os.path.getsize(input_file)
        else:
            # Try to get file size from file object
            current_pos = input_file_obj.tell()
            input_file_obj.seek(0, os.SEEK_END)
            file_size = input_file_obj.tell()
            input_file_obj.seek(current_pos)
        
        logger.info(f"Compressing file of size {file_size} bytes")
        
        # Determine number of chunks
        num_chunks = (file_size + chunk_size - 1) // chunk_size
        
        # Adjust max_workers based on number of chunks
        max_workers = min(max_workers, num_chunks)
        
        # Write header with original size and compression method
        header = struct.pack(
            "!QBI",  # 8-byte size, 1-byte method, 4-byte chunk count
            file_size,
            {
                COMPRESSION_GZIP: 1,
                COMPRESSION_BROTLI: 2,
                COMPRESSION_NONE: 0,
            }.get(method, 0),
            num_chunks,
        )
        output_file_obj.write(header)
        
        # Process file in chunks using parallel processing
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit compression tasks
            futures = []
            for chunk_id in range(num_chunks):
                # Read chunk
                chunk_data = input_file_obj.read(chunk_size)
                if not chunk_data:
                    break
                
                # Submit compression task
                future = executor.submit(
                    _compress_chunk,
                    chunk_data,
                    method,
                    level,
                    chunk_id,
                )
                futures.append(future)
            
            # Process results as they complete
            for future in as_completed(futures):
                try:
                    compressed_chunk, chunk_id = future.result()
                    
                    # Write chunk size and chunk data
                    chunk_header = struct.pack("!II", chunk_id, len(compressed_chunk))
                    output_file_obj.write(chunk_header)
                    output_file_obj.write(compressed_chunk)
                    
                    logger.debug(f"Wrote compressed chunk {chunk_id} ({len(compressed_chunk)} bytes)")
                except Exception as e:
                    logger.error(f"Chunk compression failed: {e}")
                    raise RuntimeError(f"Chunk compression failed: {e}") from e
        
        # Return compressed data if no output file was provided
        if isinstance(output_file, type(None)):
            return output_buffer.getvalue()
        
        return None
    
    finally:
        # Close files if we opened them
        if isinstance(input_file, str) and input_file_obj:
            input_file_obj.close()
        
        if isinstance(output_file, str) and output_file_obj:
            output_file_obj.close()


def decompress_large_file(
    input_file: Union[str, BinaryIO],
    output_file: Union[str, BinaryIO, None] = None,
    max_workers: int = MAX_WORKERS,
) -> Optional[bytes]:
    """
    Decompress a large file using parallel processing.
    
    Args:
        input_file: Path to input file or file-like object
        output_file: Path to output file or file-like object (if None, returns decompressed data)
        max_workers: Maximum number of worker processes
        
    Returns:
        Decompressed data as bytes if output_file is None, otherwise None
        
    Raises:
        ValueError: If the input or output file is invalid
        IOError: If there's an error reading or writing files
        RuntimeError: If decompression fails
    """
    # Validate input
    if input_file is None:
        raise ValueError("Input file cannot be None")
    
    # Open input file if string path provided
    input_file_obj = None
    if isinstance(input_file, str):
        try:
            input_file_obj = open(input_file, 'rb')
        except IOError as e:
            logger.error(f"Failed to open input file {input_file}: {e}")
            raise IOError(f"Failed to open input file {input_file}: {e}") from e
    else:
        input_file_obj = input_file
    
    # Open output file if string path provided
    output_file_obj = None
    if isinstance(output_file, str):
        try:
            output_file_obj = open(output_file, 'wb')
        except IOError as e:
            if input_file_obj and isinstance(input_file, str):
                input_file_obj.close()
            logger.error(f"Failed to open output file {output_file}: {e}")
            raise IOError(f"Failed to open output file {output_file}: {e}") from e
    else:
        output_file_obj = output_file
    
    # Create in-memory buffer if no output file provided
    if output_file_obj is None:
        output_buffer = io.BytesIO()
        output_file_obj = output_buffer
    
    try:
        # Read header
        header_size = struct.calcsize("!QBI")
        header_data = input_file_obj.read(header_size)
        
        if len(header_data) < header_size:
            raise ValueError("Invalid file format: header too small")
        
        original_size, compression_method, num_chunks = struct.unpack("!QBI", header_data)
        
        logger.info(f"Decompressing file to {original_size} bytes ({num_chunks} chunks)")
        
        # Read chunk headers and data
        chunks = []
        for _ in range(num_chunks):
            # Read chunk header
            chunk_header_size = struct.calcsize("!II")
            chunk_header = input_file_obj.read(chunk_header_size)
            
            if len(chunk_header) < chunk_header_size:
                raise ValueError("Invalid file format: chunk header too small")
            
            chunk_id, chunk_size = struct.unpack("!II", chunk_header)
            
            # Read chunk data
            chunk_data = input_file_obj.read(chunk_size)
            
            if len(chunk_data) < chunk_size:
                raise ValueError(f"Invalid file format: chunk {chunk_id} data too small")
            
            chunks.append((chunk_id, chunk_data, compression_method))
        
        # Process chunks in parallel
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit decompression tasks
            futures = []
            for chunk_id, chunk_data, compression_method in chunks:
                future = executor.submit(
                    _decompress_chunk,
                    chunk_data,
                    compression_method,
                    chunk_id,
                )
                futures.append(future)
            
            # Sort chunks by ID
            decompressed_chunks = []
            for future in as_completed(futures):
                try:
                    decompressed_chunk, chunk_id = future.result()
                    decompressed_chunks.append((chunk_id, decompressed_chunk))
                except Exception as e:
                    logger.error(f"Chunk decompression failed: {e}")
                    raise RuntimeError(f"Chunk decompression failed: {e}") from e
            
            # Sort chunks by ID and write to output
            for chunk_id, chunk_data in sorted(decompressed_chunks, key=lambda x: x[0]):
                output_file_obj.write(chunk_data)
                logger.debug(f"Wrote decompressed chunk {chunk_id} ({len(chunk_data)} bytes)")
        
        # Return decompressed data if no output file was provided
        if isinstance(output_file, type(None)):
            return output_buffer.getvalue()
        
        return None
    
    finally:
        # Close files if we opened them
        if isinstance(input_file, str) and input_file_obj:
            input_file_obj.close()
        
        if isinstance(output_file, str) and output_file_obj:
            output_file_obj.close()


def _compress_chunk(
    chunk_data: bytes,
    method: str,
    level: int,
    chunk_id: int,
) -> Tuple[bytes, int]:
    """
    Compress a single chunk of data.
    
    Args:
        chunk_data: Chunk data to compress
        method: Compression method
        level: Compression level
        chunk_id: Chunk ID
        
    Returns:
        Tuple of (compressed_chunk, chunk_id)
    """
    try:
        # Apply compression (without the header that compress_data would add)
        if method == COMPRESSION_GZIP:
            compressed = gzip.compress(chunk_data, compresslevel=level)
        elif method == COMPRESSION_BROTLI:
            compressed = brotli.compress(chunk_data, quality=level)
        elif method == COMPRESSION_NONE:
            compressed = chunk_data
        else:
            raise ValueError(f"Unsupported compression method: {method}")
        
        return compressed, chunk_id
    except Exception as e:
        logger.error(f"Failed to compress chunk {chunk_id}: {e}")
        raise


def _decompress_chunk(
    chunk_data: bytes,
    compression_method: int,
    chunk_id: int,
) -> Tuple[bytes, int]:
    """
    Decompress a single chunk of data.
    
    Args:
        chunk_data: Compressed chunk data
        compression_method: Compression method code
        chunk_id: Chunk ID
        
    Returns:
        Tuple of (decompressed_chunk, chunk_id)
    """
    try:
        # Apply decompression
        if compression_method == 1:  # gzip
            decompressed = gzip.decompress(chunk_data)
        elif compression_method == 2:  # brotli
            decompressed = brotli.decompress(chunk_data)
        elif compression_method == 0:  # none
            decompressed = chunk_data
        else:
            raise ValueError(f"Unsupported compression method: {compression_method}")
        
        return decompressed, chunk_id
    except Exception as e:
        logger.error(f"Failed to decompress chunk {chunk_id}: {e}")
        raise
