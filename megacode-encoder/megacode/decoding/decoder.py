"""
Decoding module for MegaCode scanner.

This module provides functions for decoding data from classified symbols.
"""

import logging
import struct
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)

# Try to import required modules
try:
    import reedsolo
    HAVE_REEDSOLO = True
except ImportError:
    logger.warning("reedsolo not available, error correction will be limited")
    HAVE_REEDSOLO = False


class Decoder:
    """Decoder for MegaCode scanner."""

    def __init__(self):
        """Initialize the decoder."""
        pass

    def decode_data(
        self,
        raw_data: bytes,
    ) -> Dict[str, Any]:
        """
        Decode data from raw bytes.

        Args:
            raw_data: Raw data bytes

        Returns:
            Dictionary with decoding results:
            - 'success': Whether decoding was successful
            - 'data': Decoded data
            - 'metadata': Metadata from the code
            - 'errors_corrected': Number of errors corrected
            - 'error_message': User-friendly error message if decoding failed

        Raises:
            ValueError: If the data is invalid
        """
        # Check if data is valid
        if not raw_data:
            return {
                "success": False,
                "data": None,
                "metadata": None,
                "errors_corrected": 0,
                "error_message": "No data provided for decoding. Please provide valid data."
            }

        try:
            # Parse fountain packets
            packets = self._parse_fountain_packets(raw_data)

            if not packets:
                logger.warning("No valid fountain packets found")
                # Return raw data as a fallback
                return {
                    "success": True,
                    "data": raw_data,
                    "metadata": {"format": "raw"},
                    "errors_corrected": 0,
                    "error_message": None
                }

            # Decode fountain packets
            fountain_data = self._decode_fountain_packets(packets)

            if fountain_data is None:
                logger.warning("Failed to decode fountain packets")
                return {
                    "success": False,
                    "data": None,
                    "metadata": {"format": packets[0].get("format", "unknown")},
                    "errors_corrected": 0,
                    "error_message": "Could not decode the data structure. The code may be damaged or in an unsupported format."
                }

            # Parse RS blocks
            rs_blocks = self._parse_rs_blocks(fountain_data)

            if not rs_blocks:
                # If no RS blocks found, try to return the fountain data directly
                # Check if it looks like text
                try:
                    text = fountain_data.decode('utf-8')
                    if all(c.isprintable() or c.isspace() for c in text):
                        logger.info("No RS blocks found, but data appears to be valid text")
                        return {
                            "success": True,
                            "data": fountain_data,
                            "metadata": {"format": "text", "encoding": "utf-8"},
                            "errors_corrected": 0,
                            "error_message": None
                        }
                except UnicodeDecodeError:
                    # Not valid UTF-8 text, return as binary
                    logger.info("No RS blocks found, returning raw binary data")
                    return {
                        "success": True,
                        "data": fountain_data,
                        "metadata": {"format": "binary"},
                        "errors_corrected": 0,
                        "error_message": None
                    }

            # Decode RS blocks
            rs_data, errors_corrected = self._decode_rs_blocks(rs_blocks)

            if rs_data is None:
                logger.warning("Failed to decode RS blocks")
                return {
                    "success": False,
                    "data": None,
                    "metadata": {"format": "rs_encoded", "blocks": len(rs_blocks)},
                    "errors_corrected": errors_corrected,
                    "error_message": "Error correction failed. The code may be too damaged to recover."
                }

            # Decompress data
            decompressed_data, metadata = self._decompress_data(rs_data)

            if decompressed_data is None:
                logger.warning("Failed to decompress data")
                # Try to return the RS data directly
                try:
                    text = rs_data.decode('utf-8')
                    if all(c.isprintable() or c.isspace() for c in text):
                        logger.info("Decompression failed, but data appears to be valid text")
                        return {
                            "success": True,
                            "data": rs_data,
                            "metadata": {"format": "text", "encoding": "utf-8", **metadata},
                            "errors_corrected": errors_corrected,
                            "error_message": "Decompression failed, but the raw data appears to be valid."
                        }
                except UnicodeDecodeError:
                    # Not valid UTF-8 text, return as binary
                    return {
                        "success": True,
                        "data": rs_data,
                        "metadata": {"format": "binary", **metadata},
                        "errors_corrected": errors_corrected,
                        "error_message": "Decompression failed, returning uncompressed data."
                    }

            # Determine format based on content
            format_info = "binary"
            try:
                text = decompressed_data.decode('utf-8')
                if all(c.isprintable() or c.isspace() for c in text):
                    format_info = "text"
            except UnicodeDecodeError:
                pass

            # Add format info to metadata
            if metadata is None:
                metadata = {}
            metadata["format"] = format_info

            return {
                "success": True,
                "data": decompressed_data,
                "metadata": metadata,
                "errors_corrected": errors_corrected,
                "error_message": None
            }

        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            error_message = "An unexpected error occurred during decoding."

            # Provide more specific error messages for common exceptions
            if isinstance(e, struct.error):
                error_message = "The data structure is invalid or corrupted."
            elif isinstance(e, MemoryError):
                error_message = "Not enough memory to decode the data. The file may be too large."
            elif isinstance(e, ImportError):
                error_message = "Missing required library for decoding. Please install the required dependencies."

            return {
                "success": False,
                "data": None,
                "metadata": None,
                "errors_corrected": 0,
                "error_message": f"{error_message} Technical details: {str(e)}"
            }

    def _parse_fountain_packets(self, data: bytes) -> List[Dict[str, Any]]:
        """
        Parse fountain packets from raw data.

        Args:
            data: Raw data bytes

        Returns:
            List of fountain packets
        """
        packets = []
        offset = 0

        # Define known packet formats
        FORMATS = [
            {
                "name": "standard",
                "header_format": "!HIHHI",  # magic, seed, num_indices, total_blocks, original_size
                "header_size": 13,
                "magic": 0x4D46,  # 'MF'
                "index_size": 2,  # 2 bytes per index
            },
            {
                "name": "compact",
                "header_format": "!HIHB",  # magic, seed, num_indices, total_blocks
                "header_size": 9,
                "magic": 0x4D43,  # 'MC'
                "index_size": 1,  # 1 byte per index
            },
            {
                "name": "legacy",
                "header_format": "!HIH",  # magic, seed, num_indices
                "header_size": 8,
                "magic": 0x4D4C,  # 'ML'
                "index_size": 2,  # 2 bytes per index
            }
        ]

        while offset < len(data):
            packet_found = False

            # Try each format
            for fmt in FORMATS:
                try:
                    # Check if we have enough data for the header
                    if offset + fmt["header_size"] > len(data):
                        continue

                    # Parse header
                    header_values = struct.unpack(
                        fmt["header_format"],
                        data[offset:offset+fmt["header_size"]]
                    )

                    # Extract values based on format
                    if fmt["name"] == "standard":
                        magic, seed, num_indices, total_blocks, original_size = header_values
                    elif fmt["name"] == "compact":
                        magic, seed, num_indices, total_blocks = header_values
                        original_size = 0  # Will be calculated from data
                    elif fmt["name"] == "legacy":
                        magic, seed, num_indices = header_values
                        total_blocks = 0
                        original_size = 0

                    # Check magic bytes
                    if magic != fmt["magic"]:
                        continue

                    # Check if indices are valid
                    if num_indices <= 0 or num_indices > 1000:  # Sanity check
                        continue

                    # Check if we have enough data for indices
                    indices_size = num_indices * fmt["index_size"]
                    if offset + fmt["header_size"] + indices_size > len(data):
                        continue

                    # Parse indices based on format
                    if fmt["index_size"] == 2:
                        indices_format = f"!{num_indices}H"
                    else:
                        indices_format = f"!{num_indices}B"

                    indices = struct.unpack(
                        indices_format,
                        data[offset+fmt["header_size"]:offset+fmt["header_size"]+indices_size]
                    )

                    # Calculate packet size and data
                    if fmt["name"] == "standard":
                        # Standard format has fixed block size
                        block_size = total_blocks > 0 and original_size // total_blocks or 0
                        packet_size = fmt["header_size"] + indices_size + block_size
                    else:
                        # For other formats, use remaining data or try to detect packet boundary
                        # Look for next potential packet start
                        next_offset = offset + fmt["header_size"] + indices_size
                        while next_offset < len(data) - 2:
                            if (data[next_offset] == 0x4D and
                                (data[next_offset+1] == 0x46 or
                                 data[next_offset+1] == 0x43 or
                                 data[next_offset+1] == 0x4C)):
                                break
                            next_offset += 1

                        if next_offset > offset + fmt["header_size"] + indices_size:
                            packet_size = next_offset - offset
                        else:
                            # Use at least 1 byte of data
                            packet_size = fmt["header_size"] + indices_size + 1

                    # Check if we have enough data for the packet
                    if offset + packet_size > len(data):
                        # Use remaining data
                        packet_data = data[offset+fmt["header_size"]+indices_size:]
                    else:
                        packet_data = data[offset+fmt["header_size"]+indices_size:offset+packet_size]

                    # For compact format, calculate original_size from data
                    if fmt["name"] == "compact" and total_blocks > 0:
                        original_size = len(packet_data) * total_blocks

                    # Create packet
                    packet = {
                        "format": fmt["name"],
                        "seed": seed,
                        "block_indices": list(indices),
                        "total_blocks": total_blocks,
                        "original_size": original_size,
                        "data": packet_data,
                    }

                    packets.append(packet)

                    # Move to next packet
                    offset += packet_size
                    packet_found = True
                    break  # Break out of format loop

                except Exception as e:
                    logger.debug(f"Failed to parse {fmt['name']} packet at offset {offset}: {e}")
                    continue

            # If no format matched, move forward
            if not packet_found:
                offset += 1

        # Try to detect raw data format if no packets found
        if not packets and len(data) > 0:
            try:
                # Check if it's a simple binary format
                if all(b in range(32, 127) or b in (9, 10, 13) for b in data):
                    # Likely text data
                    logger.info("No fountain packets found, but data appears to be text")
                    packet = {
                        "format": "raw_text",
                        "seed": 0,
                        "block_indices": [0],
                        "total_blocks": 1,
                        "original_size": len(data),
                        "data": data,
                    }
                    packets.append(packet)
                else:
                    # Binary data
                    logger.info("No fountain packets found, treating as raw binary data")
                    packet = {
                        "format": "raw_binary",
                        "seed": 0,
                        "block_indices": [0],
                        "total_blocks": 1,
                        "original_size": len(data),
                        "data": data,
                    }
                    packets.append(packet)
            except Exception as e:
                logger.warning(f"Failed to parse raw data: {e}")

        logger.info(f"Parsed {len(packets)} fountain packets")
        return packets

    def _decode_fountain_packets(self, packets: List[Dict[str, Any]]) -> Optional[bytes]:
        """
        Decode fountain packets.

        Args:
            packets: List of fountain packets

        Returns:
            Decoded data or None if decoding fails
        """
        if not packets:
            return None

        # Check if we have raw data packets
        if len(packets) == 1 and packets[0].get("format", "") in ["raw_text", "raw_binary"]:
            logger.info(f"Using raw data packet ({len(packets[0]['data'])} bytes)")
            return packets[0]["data"]

        # Extract parameters from first packet
        packet_format = packets[0].get("format", "standard")
        total_blocks = packets[0]["total_blocks"]
        original_size = packets[0]["original_size"]

        # Handle special formats
        if packet_format == "legacy":
            # Legacy format doesn't have block information, just return the data
            logger.info(f"Using legacy format data ({len(packets[0]['data'])} bytes)")
            return packets[0]["data"]

        # For standard and compact formats, use fountain decoding
        if total_blocks <= 0:
            # If total_blocks is not specified, estimate it from the data
            block_sizes = [len(p["data"]) for p in packets]
            avg_block_size = sum(block_sizes) / len(block_sizes)
            total_blocks = max(1, int(original_size / avg_block_size) if original_size > 0 else len(packets))
            logger.info(f"Estimated total blocks: {total_blocks}")

        logger.info(
            f"Decoding {len(packets)} packets to recover {total_blocks} blocks "
            f"({original_size} bytes)"
        )

        # Initialize decoded blocks
        decoded_blocks = [None] * total_blocks
        decoded_count = 0

        # Initialize ripple (packets that can decode exactly one block)
        ripple = []

        # Process all packets
        remaining_packets = list(packets)

        # Continue until all blocks are decoded or no more progress
        max_iterations = total_blocks * 2  # Prevent infinite loops
        iteration = 0

        while decoded_count < total_blocks and remaining_packets and iteration < max_iterations:
            iteration += 1
            ripple_found = False

            # Find packets that can decode exactly one block
            for packet in list(remaining_packets):
                # Skip if already in ripple
                if packet in ripple:
                    continue

                # Check if this packet can decode exactly one block
                unknown_blocks = [
                    idx for idx in packet["block_indices"] if idx < len(decoded_blocks) and decoded_blocks[idx] is None
                ]

                if len(unknown_blocks) == 1:
                    ripple.append(packet)
                    ripple_found = True

            # If ripple is empty, try to use packets with multiple unknowns
            if not ripple:
                # Sort packets by number of unknowns
                packets_by_unknowns = []
                for packet in remaining_packets:
                    unknown_blocks = [
                        idx for idx in packet["block_indices"] if idx < len(decoded_blocks) and decoded_blocks[idx] is None
                    ]
                    if unknown_blocks:  # Only consider packets with at least one unknown
                        packets_by_unknowns.append((len(unknown_blocks), packet))

                if packets_by_unknowns:
                    # Use the packet with the fewest unknowns
                    packets_by_unknowns.sort()
                    ripple.append(packets_by_unknowns[0][1])
                    ripple_found = True

            # If still no progress, try to recover partial data
            if not ripple_found:
                logger.warning(
                    f"Decoding stalled: {decoded_count}/{total_blocks} blocks decoded"
                )

                # If we have at least some blocks, return partial data
                if decoded_count > 0:
                    logger.info(f"Returning partial data ({decoded_count}/{total_blocks} blocks)")
                    # Fill missing blocks with zeros
                    for i in range(len(decoded_blocks)):
                        if decoded_blocks[i] is None:
                            # Use the first packet's data length as a guide
                            decoded_blocks[i] = b"\x00" * len(packets[0]["data"])

                    # Combine blocks and truncate
                    partial_data = b"".join(decoded_blocks)
                    return partial_data[:original_size] if original_size > 0 else partial_data

                return None

            # Process a packet from the ripple
            packet = ripple.pop(0)
            if packet in remaining_packets:
                remaining_packets.remove(packet)

            # Find the unknown blocks
            unknown_blocks = [
                idx for idx in packet["block_indices"] if idx < len(decoded_blocks) and decoded_blocks[idx] is None
            ]

            if not unknown_blocks:
                # All blocks in this packet are already decoded
                continue

            # For multiple unknowns, we can only guess
            if len(unknown_blocks) > 1:
                logger.debug(f"Guessing values for {len(unknown_blocks)} unknown blocks")
                # Just use the packet data for the first unknown
                unknown_idx = unknown_blocks[0]
                decoded_blocks[unknown_idx] = packet["data"]
                decoded_count += 1
                continue

            # Normal case - exactly one unknown
            unknown_idx = unknown_blocks[0]

            # XOR with all known blocks to recover the unknown block
            block_data = bytearray(packet["data"])

            for idx in packet["block_indices"]:
                if idx < len(decoded_blocks) and idx != unknown_idx and decoded_blocks[idx] is not None:
                    for j in range(min(len(block_data), len(decoded_blocks[idx]))):
                        block_data[j] ^= decoded_blocks[idx][j]

            # Store the decoded block
            decoded_blocks[unknown_idx] = bytes(block_data)
            decoded_count += 1

            logger.debug(f"Decoded block {unknown_idx} ({decoded_count}/{total_blocks})")

        # Check if all blocks were decoded
        if decoded_count < total_blocks:
            logger.warning(
                f"Failed to decode all blocks: {decoded_count}/{total_blocks}"
            )

            # If we have at least some blocks, return partial data
            if decoded_count > 0:
                logger.info(f"Returning partial data ({decoded_count}/{total_blocks} blocks)")
                # Fill missing blocks with zeros
                for i in range(len(decoded_blocks)):
                    if decoded_blocks[i] is None:
                        # Use the first packet's data length as a guide
                        decoded_blocks[i] = b"\x00" * len(packets[0]["data"])

                # Combine blocks and truncate
                partial_data = b"".join(decoded_blocks)
                return partial_data[:original_size] if original_size > 0 else partial_data

            return None

        # Combine blocks to form the original data
        decoded_data = b"".join(decoded_blocks)

        # Truncate to original size if specified
        if original_size > 0:
            decoded_data = decoded_data[:original_size]

        logger.info(f"Successfully decoded {len(decoded_data)} bytes")

        return decoded_data

    def _parse_rs_blocks(self, data: bytes) -> List[Dict[str, Any]]:
        """
        Parse Reed-Solomon blocks from data.

        Args:
            data: Data bytes

        Returns:
            List of RS blocks
        """
        blocks = []
        offset = 0

        while offset < len(data):
            try:
                # Check if we have enough data for a header
                if offset + 7 > len(data):
                    break

                # Parse header
                magic1, magic2, ecc_level, block_id, total_blocks = struct.unpack(
                    "!BBBBB", data[offset:offset+5]
                )

                # Check magic bytes
                if magic1 != 0x4D or magic2 != 0x43:  # 'MC'
                    offset += 1
                    continue

                # Parse lengths
                data_length, ecc_length = struct.unpack(
                    "!BB", data[offset+5:offset+7]
                )

                # Check if we have enough data for the block
                if offset + 7 + data_length + ecc_length > len(data):
                    offset += 1
                    continue

                # Extract data and ECC
                block_data = data[offset+7:offset+7+data_length]
                ecc_data = data[offset+7+data_length:offset+7+data_length+ecc_length]

                # Create block
                block = {
                    "ecc_level": ecc_level,
                    "block_id": block_id,
                    "total_blocks": total_blocks,
                    "data": block_data,
                    "ecc": ecc_data,
                }

                blocks.append(block)

                # Move to next block
                offset += 7 + data_length + ecc_length

            except Exception as e:
                logger.warning(f"Failed to parse RS block at offset {offset}: {e}")
                offset += 1

        logger.info(f"Parsed {len(blocks)} RS blocks")
        return blocks

    def _decode_rs_blocks(self, blocks: List[Dict[str, Any]]) -> Tuple[Optional[bytes], int]:
        """
        Decode Reed-Solomon blocks.

        Args:
            blocks: List of RS blocks

        Returns:
            Tuple of (decoded_data, errors_corrected)
        """
        if not HAVE_REEDSOLO:
            logger.warning("reedsolo not available, skipping error correction")
            # Concatenate block data without error correction
            return b"".join(block["data"] for block in blocks), 0

        # Sort blocks by ID
        sorted_blocks = sorted(blocks, key=lambda b: b["block_id"])

        # Check if we have all blocks
        block_ids = [b["block_id"] for b in sorted_blocks]
        total_blocks = sorted_blocks[0]["total_blocks"]
        expected_ids = set(range(total_blocks))
        missing_ids = expected_ids - set(block_ids)

        if missing_ids:
            logger.warning(f"Missing blocks: {missing_ids}")
            return None, 0

        # Decode each block
        decoded_data = bytearray()
        total_errors = 0

        for block in sorted_blocks:
            # Initialize RS codec based on block's ECC
            nsym = len(block["ecc"])
            rs = reedsolo.RSCodec(nsym)

            # Combine data and ECC for decoding
            encoded = block["data"] + block["ecc"]

            try:
                # Attempt to decode and correct errors
                decoded, _, errata_pos = rs.decode(encoded, return_errata_positions=True)

                if errata_pos:
                    logger.info(f"Corrected {len(errata_pos)} errors in block {block['block_id']}")
                    total_errors += len(errata_pos)

                # Remove padding from the last block
                if block["block_id"] == total_blocks - 1:
                    # Find the first zero byte from the end
                    i = len(decoded) - 1
                    while i >= 0 and decoded[i] == 0:
                        i -= 1

                    # Keep data up to that point (inclusive)
                    decoded_data.extend(decoded[:i+1])
                else:
                    decoded_data.extend(decoded)

            except reedsolo.ReedSolomonError as e:
                logger.error(f"Failed to decode block {block['block_id']}: {e}")
                return None, total_errors

        return bytes(decoded_data), total_errors

    def _decompress_data(self, data: bytes) -> Tuple[Optional[bytes], Dict[str, Any]]:
        """
        Decompress data.

        Args:
            data: Compressed data

        Returns:
            Tuple of (decompressed_data, metadata)
        """
        try:
            # Extract header
            header_size = struct.calcsize("!IB")
            if len(data) < header_size:
                logger.warning("Compressed data is too small to contain a valid header")
                return None, {}

            original_size, compression_method = struct.unpack(
                "!IB", data[:header_size]
            )

            # Get compressed data without header
            compressed = data[header_size:]

            # Apply decompression
            if compression_method == 1:  # gzip
                import gzip
                logger.info(f"Decompressing {len(compressed)} bytes with gzip")
                decompressed = gzip.decompress(compressed)
            elif compression_method == 2:  # brotli
                try:
                    import brotli
                    logger.info(f"Decompressing {len(compressed)} bytes with brotli")
                    decompressed = brotli.decompress(compressed)
                except ImportError:
                    logger.warning("brotli not available, cannot decompress")
                    return None, {"compression_method": "brotli"}
            elif compression_method == 0:  # none
                logger.info("No decompression needed")
                decompressed = compressed
            else:
                logger.warning(f"Unsupported compression method: {compression_method}")
                return None, {"compression_method": compression_method}

            # Verify size
            if len(decompressed) != original_size:
                logger.warning(
                    f"Decompressed size ({len(decompressed)}) doesn't match "
                    f"original size ({original_size})"
                )

            return decompressed, {
                "original_size": original_size,
                "compression_method": compression_method,
            }

        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return None, {}
