"""
Renderer module for MegaCode.

This module handles rendering the 2D grid to various output formats:
- SVG for vector graphics
- PNG for raster images
- STL for 3D printing
"""

import logging
import io
import svgwrite
import numpy as np
from PIL import Image
from stl import mesh
from typing import Union, Optional, Tuple

from ..layout import GridLayout

# Configure logging
logger = logging.getLogger(__name__)


def render_svg(
    layout: GridLayout,
    module_size: int = 10,
    output_file: Optional[str] = None,
) -> str:
    """
    Render the grid layout as an SVG image.
    
    Args:
        layout: GridLayout object
        module_size: Size of each module in pixels
        output_file: Path to save the SVG file (if None, returns SVG as string)
    
    Returns:
        SVG content as string
    """
    # Calculate dimensions
    width = layout.width * module_size
    height = layout.height * module_size
    
    # Create SVG drawing
    dwg = svgwrite.Drawing(
        output_file or "megacode.svg",
        size=(f"{width}px", f"{height}px"),
        profile="tiny",
    )
    
    # Add white background
    dwg.add(
        dwg.rect(
            insert=(0, 0),
            size=(f"{width}px", f"{height}px"),
            fill="white",
        )
    )
    
    # Add modules
    for y in range(layout.height):
        for x in range(layout.width):
            # Get color
            color = layout.grid[y, x]
            color_hex = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
            
            # Add rectangle
            dwg.add(
                dwg.rect(
                    insert=(x * module_size, y * module_size),
                    size=(module_size, module_size),
                    fill=color_hex,
                )
            )
    
    # Save to file or return as string
    if output_file:
        dwg.save()
        logger.info(f"Saved SVG to {output_file}")
        return ""
    else:
        svg_string = dwg.tostring()
        logger.info(f"Generated SVG ({len(svg_string)} bytes)")
        return svg_string


def render_png(
    layout: GridLayout,
    module_size: int = 10,
    output_file: Optional[str] = None,
) -> Optional[bytes]:
    """
    Render the grid layout as a PNG image.
    
    Args:
        layout: GridLayout object
        module_size: Size of each module in pixels
        output_file: Path to save the PNG file (if None, returns PNG as bytes)
    
    Returns:
        PNG content as bytes if output_file is None, otherwise None
    """
    # Calculate dimensions
    width = layout.width * module_size
    height = layout.height * module_size
    
    # Create image
    img = Image.new("RGB", (width, height), "white")
    pixels = img.load()
    
    # Fill pixels
    for y in range(layout.height):
        for x in range(layout.width):
            # Get color
            color = tuple(layout.grid[y, x])
            
            # Fill module area
            for dy in range(module_size):
                for dx in range(module_size):
                    pixels[x * module_size + dx, y * module_size + dy] = color
    
    # Save to file or return as bytes
    if output_file:
        img.save(output_file, format="PNG")
        logger.info(f"Saved PNG to {output_file}")
        return None
    else:
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        png_bytes = img_bytes.getvalue()
        logger.info(f"Generated PNG ({len(png_bytes)} bytes)")
        return png_bytes


def render_stl(
    layout: GridLayout,
    module_size: float = 1.0,
    base_height: float = 1.0,
    module_height: float = 0.5,
    output_file: Optional[str] = None,
) -> Optional[bytes]:
    """
    Render the grid layout as an STL file for 3D printing.
    
    Args:
        layout: GridLayout object
        module_size: Size of each module in mm
        base_height: Height of the base in mm
        module_height: Height of raised modules in mm
        output_file: Path to save the STL file (if None, returns STL as bytes)
    
    Returns:
        STL content as bytes if output_file is None, otherwise None
    """
    # Calculate dimensions
    width = layout.width * module_size
    height = layout.height * module_size
    
    # Create base mesh
    vertices = np.array([
        [0, 0, 0],                    # v0: bottom-left-front
        [width, 0, 0],                # v1: bottom-right-front
        [width, height, 0],           # v2: bottom-right-back
        [0, height, 0],               # v3: bottom-left-back
        [0, 0, base_height],          # v4: top-left-front
        [width, 0, base_height],      # v5: top-right-front
        [width, height, base_height], # v6: top-right-back
        [0, height, base_height],     # v7: top-left-back
    ])
    
    # Define faces (triangles)
    faces = np.array([
        # Bottom face
        [0, 2, 1],
        [0, 3, 2],
        # Front face
        [0, 1, 5],
        [0, 5, 4],
        # Right face
        [1, 2, 6],
        [1, 6, 5],
        # Back face
        [2, 3, 7],
        [2, 7, 6],
        # Left face
        [3, 0, 4],
        [3, 4, 7],
        # Top face
        [4, 5, 6],
        [4, 6, 7],
    ])
    
    # Create mesh
    base_mesh = mesh.Mesh(np.zeros(faces.shape[0], dtype=mesh.Mesh.dtype))
    for i, f in enumerate(faces):
        for j in range(3):
            base_mesh.vectors[i][j] = vertices[f[j], :]
    
    # Add modules
    module_meshes = []
    
    for y in range(layout.height):
        for x in range(layout.width):
            # Get color
            color = layout.grid[y, x]
            
            # Only create raised modules for black or near-black colors
            if sum(color) < 100:  # Threshold for "dark" colors
                # Calculate module position
                mx = x * module_size
                my = y * module_size
                mz = base_height
                
                # Create module vertices
                module_vertices = np.array([
                    [mx, my, mz],                                # v0: bottom-left-front
                    [mx + module_size, my, mz],                  # v1: bottom-right-front
                    [mx + module_size, my + module_size, mz],    # v2: bottom-right-back
                    [mx, my + module_size, mz],                  # v3: bottom-left-back
                    [mx, my, mz + module_height],                # v4: top-left-front
                    [mx + module_size, my, mz + module_height],  # v5: top-right-front
                    [mx + module_size, my + module_size, mz + module_height],  # v6: top-right-back
                    [mx, my + module_size, mz + module_height],  # v7: top-left-back
                ])
                
                # Create module mesh
                module_mesh = mesh.Mesh(np.zeros(faces.shape[0], dtype=mesh.Mesh.dtype))
                for i, f in enumerate(faces):
                    for j in range(3):
                        module_mesh.vectors[i][j] = module_vertices[f[j], :]
                
                module_meshes.append(module_mesh)
    
    # Combine all meshes
    combined_mesh = mesh.Mesh(np.concatenate([base_mesh.data] + [m.data for m in module_meshes]))
    
    # Save to file or return as bytes
    if output_file:
        combined_mesh.save(output_file)
        logger.info(f"Saved STL to {output_file}")
        return None
    else:
        stl_bytes = io.BytesIO()
        combined_mesh.save(stl_bytes)
        stl_data = stl_bytes.getvalue()
        logger.info(f"Generated STL ({len(stl_data)} bytes)")
        return stl_data
