"""
MegaCode: High-capacity 2D and 3D code generator.

MegaCode is a system for generating high-capacity 2D and 3D codes
capable of storing up to 4MB of data (approximately 1 million AI prompt tokens).
"""

__version__ = "0.1.0"

# Import modules conditionally to avoid dependency issues
try:
    from . import compression
except ImportError:
    pass

try:
    from . import ecc
except ImportError:
    pass

try:
    from . import fountain
except ImportError:
    pass

try:
    from . import symbol
except ImportError:
    pass

try:
    from . import layout
except ImportError:
    pass

try:
    from . import renderer
except ImportError:
    pass

try:
    from . import cli
except ImportError:
    pass

__all__ = [
    "compression",
    "ecc",
    "fountain",
    "symbol",
    "layout",
    "renderer",
    "cli",
]
