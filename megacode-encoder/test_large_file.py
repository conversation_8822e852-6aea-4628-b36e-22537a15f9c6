#!/usr/bin/env python3
"""
Test script for MegaCode large file handler.
"""

import os
import time
import logging
import tempfile
import random
from typing import Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Import the module to test
try:
    from megacode.compression.large_file_handler import (
        compress_large_file,
        decompress_large_file,
        COMPRESSION_BROTLI,
        COMPRESSION_GZIP,
        COMPRESSION_NONE,
    )
except ImportError:
    # Fall back to direct import if module is not installed
    import sys
    import os.path
    sys.path.append(os.path.abspath(os.path.dirname(__file__)))
    from megacode.compression.large_file_handler import (
        compress_large_file,
        decompress_large_file,
        COMPRESSION_BROTLI,
        COMPRESSION_GZIP,
        COMPRESSION_NONE,
    )


def generate_test_file(size_mb: int, pattern: str = "random") -> str:
    """
    Generate a test file of the specified size.
    
    Args:
        size_mb: Size of the file in MB
        pattern: Data pattern ('random', 'sequential', 'repeating')
        
    Returns:
        Path to the generated file
    """
    size_bytes = size_mb * 1024 * 1024
    
    # Create a temporary file
    fd, path = tempfile.mkstemp(suffix=".bin")
    
    try:
        with os.fdopen(fd, "wb") as f:
            if pattern == "random":
                # Generate random data in chunks to avoid memory issues
                chunk_size = 1024 * 1024  # 1MB chunks
                for _ in range(0, size_bytes, chunk_size):
                    chunk = bytes(random.randint(0, 255) for _ in range(min(chunk_size, size_bytes - f.tell())))
                    f.write(chunk)
            elif pattern == "sequential":
                # Generate sequential data
                chunk_size = 1024 * 1024  # 1MB chunks
                for i in range(0, size_bytes, chunk_size):
                    chunk = bytes(j % 256 for j in range(i, min(i + chunk_size, size_bytes)))
                    f.write(chunk)
            elif pattern == "repeating":
                # Generate repeating pattern
                pattern_bytes = b"MegaCode" * 128  # 1KB pattern
                for _ in range(0, size_bytes, len(pattern_bytes)):
                    remaining = size_bytes - f.tell()
                    f.write(pattern_bytes[:min(len(pattern_bytes), remaining)])
            else:
                raise ValueError(f"Unknown pattern: {pattern}")
    except Exception as e:
        os.unlink(path)
        raise RuntimeError(f"Failed to generate test file: {e}") from e
    
    logger.info(f"Generated {size_mb}MB test file at {path}")
    return path


def benchmark_compression(
    input_file: str,
    method: str = COMPRESSION_BROTLI,
    level: int = 9,
) -> Tuple[str, float, float]:
    """
    Benchmark compression and decompression of a file.
    
    Args:
        input_file: Path to input file
        method: Compression method
        level: Compression level
        
    Returns:
        Tuple of (output_file, compression_ratio, time_taken)
    """
    # Create output file path
    output_file = input_file + ".compressed"
    
    # Measure original file size
    original_size = os.path.getsize(input_file)
    
    # Compress file and measure time
    start_time = time.time()
    compress_large_file(input_file, output_file, method=method, level=level)
    compression_time = time.time() - start_time
    
    # Measure compressed file size
    compressed_size = os.path.getsize(output_file)
    compression_ratio = compressed_size / original_size
    
    logger.info(
        f"Compressed {original_size / (1024*1024):.2f}MB to {compressed_size / (1024*1024):.2f}MB "
        f"(ratio: {compression_ratio:.2f}) in {compression_time:.2f}s"
    )
    
    # Create decompressed file path
    decompressed_file = input_file + ".decompressed"
    
    # Decompress file and measure time
    start_time = time.time()
    decompress_large_file(output_file, decompressed_file)
    decompression_time = time.time() - start_time
    
    # Verify decompressed file size
    decompressed_size = os.path.getsize(decompressed_file)
    
    logger.info(
        f"Decompressed {compressed_size / (1024*1024):.2f}MB to {decompressed_size / (1024*1024):.2f}MB "
        f"in {decompression_time:.2f}s"
    )
    
    # Verify data integrity
    if decompressed_size != original_size:
        logger.warning(
            f"Size mismatch: original={original_size}, decompressed={decompressed_size}"
        )
    
    # Verify file contents (for small files only)
    if original_size < 100 * 1024 * 1024:  # 100MB
        with open(input_file, "rb") as f1, open(decompressed_file, "rb") as f2:
            if f1.read() == f2.read():
                logger.info("Data integrity check passed")
            else:
                logger.error("Data integrity check failed")
    
    return output_file, compression_ratio, compression_time + decompression_time


def test_large_file_handler():
    """Test the large file handler with various file sizes and patterns."""
    test_files = []
    
    try:
        # Test with small file (1MB)
        small_file = generate_test_file(1, pattern="random")
        test_files.append(small_file)
        
        # Test with medium file (10MB)
        medium_file = generate_test_file(10, pattern="sequential")
        test_files.append(medium_file)
        
        # Test with large file (50MB) - adjust based on available memory
        large_file = generate_test_file(50, pattern="repeating")
        test_files.append(large_file)
        
        # Benchmark compression methods
        for file_path in test_files:
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            logger.info(f"Testing {file_size:.2f}MB file")
            
            # Test with different compression methods
            for method, level in [
                (COMPRESSION_BROTLI, 9),
                (COMPRESSION_GZIP, 6),
                (COMPRESSION_NONE, 0),
            ]:
                logger.info(f"Testing {method} compression (level {level})")
                output_file, ratio, time_taken = benchmark_compression(
                    file_path, method=method, level=level
                )
                test_files.append(output_file)
                test_files.append(file_path + ".decompressed")
                
                logger.info(
                    f"Results for {method}: ratio={ratio:.2f}, time={time_taken:.2f}s"
                )
    
    finally:
        # Clean up test files
        for file_path in test_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.debug(f"Removed test file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to remove test file {file_path}: {e}")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode large file handler")
    
    # Set random seed for reproducibility
    random.seed(42)
    
    test_large_file_handler()
    
    logger.info("All tests completed")


if __name__ == "__main__":
    main()
