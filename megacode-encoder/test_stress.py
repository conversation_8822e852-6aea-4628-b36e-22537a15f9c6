#!/usr/bin/env python3
"""
Stress test script for MegaCode.

This script tests the entire MegaCode pipeline with large files and various error conditions.
"""

import os
import time
import logging
import tempfile
import random
import argparse
from typing import Tuple, List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Import modules to test
try:
    from megacode.compression.large_file_handler import (
        compress_large_file,
        decompress_large_file,
        COMPRESSION_BROTLI,
    )
    from megacode.ecc.large_file_ecc import (
        apply_ecc_to_large_file,
        decode_ecc_large_file,
        ECCLevel,
    )
except ImportError:
    # Fall back to direct import if module is not installed
    import sys
    import os.path
    sys.path.append(os.path.abspath(os.path.dirname(__file__)))
    from megacode.compression.large_file_handler import (
        compress_large_file,
        decompress_large_file,
        COMPRESSION_BROTLI,
    )
    from megacode.ecc.large_file_ecc import (
        apply_ecc_to_large_file,
        decode_ecc_large_file,
        ECCLevel,
    )


def generate_test_file(size_mb: int, pattern: str = "random") -> str:
    """
    Generate a test file of the specified size.
    
    Args:
        size_mb: Size of the file in MB
        pattern: Data pattern ('random', 'sequential', 'repeating', 'zeros')
        
    Returns:
        Path to the generated file
    """
    size_bytes = size_mb * 1024 * 1024
    
    # Create a temporary file
    fd, path = tempfile.mkstemp(suffix=".bin")
    
    try:
        with os.fdopen(fd, "wb") as f:
            if pattern == "random":
                # Generate random data in chunks to avoid memory issues
                chunk_size = 1024 * 1024  # 1MB chunks
                for _ in range(0, size_bytes, chunk_size):
                    chunk = bytes(random.randint(0, 255) for _ in range(min(chunk_size, size_bytes - f.tell())))
                    f.write(chunk)
            elif pattern == "sequential":
                # Generate sequential data
                chunk_size = 1024 * 1024  # 1MB chunks
                for i in range(0, size_bytes, chunk_size):
                    chunk = bytes(j % 256 for j in range(i, min(i + chunk_size, size_bytes)))
                    f.write(chunk)
            elif pattern == "repeating":
                # Generate repeating pattern
                pattern_bytes = b"MegaCode" * 128  # 1KB pattern
                for _ in range(0, size_bytes, len(pattern_bytes)):
                    remaining = size_bytes - f.tell()
                    f.write(pattern_bytes[:min(len(pattern_bytes), remaining)])
            elif pattern == "zeros":
                # Generate all zeros
                chunk_size = 1024 * 1024  # 1MB chunks
                for _ in range(0, size_bytes, chunk_size):
                    chunk = bytes(0 for _ in range(min(chunk_size, size_bytes - f.tell())))
                    f.write(chunk)
            else:
                raise ValueError(f"Unknown pattern: {pattern}")
    except Exception as e:
        os.unlink(path)
        raise RuntimeError(f"Failed to generate test file: {e}") from e
    
    logger.info(f"Generated {size_mb}MB test file at {path}")
    return path


def introduce_errors(file_path: str, error_rate: float = 0.01, error_pattern: str = "random") -> str:
    """
    Introduce errors into a file.
    
    Args:
        file_path: Path to the file
        error_rate: Percentage of bytes to corrupt (0.0 to 1.0)
        error_pattern: Error pattern ('random', 'burst', 'edge')
        
    Returns:
        Path to the corrupted file
    """
    # Create output file path
    corrupted_file = file_path + ".corrupted"
    
    # Get file size
    file_size = os.path.getsize(file_path)
    
    # Calculate number of bytes to corrupt
    num_errors = int(file_size * error_rate)
    
    # Generate positions to corrupt based on pattern
    if error_pattern == "random":
        # Random errors throughout the file
        positions = random.sample(range(file_size), num_errors)
    elif error_pattern == "burst":
        # Burst errors (concentrated in a few areas)
        num_bursts = min(10, num_errors // 10)
        burst_size = num_errors // num_bursts
        positions = []
        for _ in range(num_bursts):
            burst_start = random.randint(0, file_size - burst_size)
            positions.extend(range(burst_start, burst_start + burst_size))
        # Ensure we have exactly num_errors positions
        if len(positions) > num_errors:
            positions = positions[:num_errors]
        elif len(positions) < num_errors:
            # Add some random positions to reach the target
            additional = random.sample(
                [p for p in range(file_size) if p not in positions],
                num_errors - len(positions)
            )
            positions.extend(additional)
    elif error_pattern == "edge":
        # Errors concentrated at the beginning and end
        edge_size = min(num_errors // 2, file_size // 10)
        positions = list(range(edge_size)) + list(range(file_size - edge_size, file_size))
        # Ensure we have exactly num_errors positions
        if len(positions) > num_errors:
            positions = random.sample(positions, num_errors)
        elif len(positions) < num_errors:
            # Add some random positions to reach the target
            additional = random.sample(
                [p for p in range(edge_size, file_size - edge_size) if p not in positions],
                num_errors - len(positions)
            )
            positions.extend(additional)
    else:
        raise ValueError(f"Unknown error pattern: {error_pattern}")
    
    # Read original file and create corrupted copy
    with open(file_path, "rb") as f_in, open(corrupted_file, "wb") as f_out:
        # Read and write in chunks
        chunk_size = 1024 * 1024  # 1MB chunks
        position = 0
        
        while True:
            chunk = f_in.read(chunk_size)
            if not chunk:
                break
            
            # Convert to bytearray for modification
            chunk_array = bytearray(chunk)
            
            # Corrupt bytes in this chunk
            for i in range(len(chunk_array)):
                if position + i in positions:
                    # Corrupt byte (XOR with random value)
                    chunk_array[i] ^= random.randint(1, 255)
            
            # Write corrupted chunk
            f_out.write(chunk_array)
            position += len(chunk)
    
    logger.info(f"Introduced {num_errors} errors ({error_rate:.2%}) into {corrupted_file}")
    return corrupted_file


def run_pipeline_test(
    input_file: str,
    compression_level: int = 9,
    ecc_level: int = ECCLevel.M,
    introduce_errors_flag: bool = False,
    error_rate: float = 0.01,
    error_pattern: str = "random",
    keep_files: bool = False,
) -> Dict[str, Any]:
    """
    Run the complete MegaCode pipeline on a file.
    
    Args:
        input_file: Path to input file
        compression_level: Compression level
        ecc_level: Error correction level
        introduce_errors_flag: Whether to introduce errors
        error_rate: Percentage of bytes to corrupt
        error_pattern: Error pattern
        keep_files: Whether to keep intermediate files
        
    Returns:
        Dictionary with test results
    """
    start_time = time.time()
    test_files = []
    results = {
        "input_file": input_file,
        "input_size": os.path.getsize(input_file),
        "compression_level": compression_level,
        "ecc_level": ecc_level,
        "success": False,
        "error": None,
    }
    
    try:
        # Step 1: Compress
        compressed_file = input_file + ".compressed"
        test_files.append(compressed_file)
        
        compression_start = time.time()
        compress_large_file(
            input_file,
            compressed_file,
            method=COMPRESSION_BROTLI,
            level=compression_level,
        )
        compression_time = time.time() - compression_start
        
        compressed_size = os.path.getsize(compressed_file)
        compression_ratio = compressed_size / results["input_size"]
        
        results["compressed_size"] = compressed_size
        results["compression_ratio"] = compression_ratio
        results["compression_time"] = compression_time
        
        logger.info(
            f"Compressed {results['input_size'] / (1024*1024):.2f}MB to "
            f"{compressed_size / (1024*1024):.2f}MB (ratio: {compression_ratio:.2f}) "
            f"in {compression_time:.2f}s"
        )
        
        # Step 2: Apply ECC
        ecc_file = compressed_file + ".ecc"
        test_files.append(ecc_file)
        
        ecc_start = time.time()
        apply_ecc_to_large_file(
            compressed_file,
            ecc_file,
            level=ecc_level,
        )
        ecc_time = time.time() - ecc_start
        
        ecc_size = os.path.getsize(ecc_file)
        ecc_ratio = ecc_size / compressed_size
        
        results["ecc_size"] = ecc_size
        results["ecc_ratio"] = ecc_ratio
        results["ecc_time"] = ecc_time
        
        logger.info(
            f"Applied ECC to {compressed_size / (1024*1024):.2f}MB, resulting in "
            f"{ecc_size / (1024*1024):.2f}MB (ratio: {ecc_ratio:.2f}) "
            f"in {ecc_time:.2f}s"
        )
        
        # Step 3: Introduce errors (optional)
        if introduce_errors_flag:
            corrupted_file = introduce_errors(
                ecc_file,
                error_rate=error_rate,
                error_pattern=error_pattern,
            )
            test_files.append(corrupted_file)
            
            results["error_rate"] = error_rate
            results["error_pattern"] = error_pattern
            
            # Use corrupted file for decoding
            decode_input = corrupted_file
        else:
            # Use original ECC file for decoding
            decode_input = ecc_file
        
        # Step 4: Decode ECC
        decoded_ecc_file = ecc_file + ".decoded"
        test_files.append(decoded_ecc_file)
        
        decode_ecc_start = time.time()
        decode_ecc_large_file(
            decode_input,
            decoded_ecc_file,
        )
        decode_ecc_time = time.time() - decode_ecc_start
        
        results["decode_ecc_time"] = decode_ecc_time
        
        logger.info(
            f"Decoded ECC in {decode_ecc_time:.2f}s"
        )
        
        # Step 5: Decompress
        decompressed_file = decoded_ecc_file + ".decompressed"
        test_files.append(decompressed_file)
        
        decompress_start = time.time()
        decompress_large_file(
            decoded_ecc_file,
            decompressed_file,
        )
        decompress_time = time.time() - decompress_start
        
        results["decompress_time"] = decompress_time
        
        logger.info(
            f"Decompressed in {decompress_time:.2f}s"
        )
        
        # Step 6: Verify
        decompressed_size = os.path.getsize(decompressed_file)
        results["decompressed_size"] = decompressed_size
        
        if decompressed_size != results["input_size"]:
            logger.warning(
                f"Size mismatch: original={results['input_size']}, "
                f"decompressed={decompressed_size}"
            )
            results["size_match"] = False
        else:
            results["size_match"] = True
        
        # For small files, verify content
        if results["input_size"] < 100 * 1024 * 1024:  # 100MB
            with open(input_file, "rb") as f1, open(decompressed_file, "rb") as f2:
                original_data = f1.read()
                decompressed_data = f2.read()
                
                if original_data == decompressed_data:
                    logger.info("Data integrity check passed")
                    results["data_match"] = True
                else:
                    # Count differences
                    diff_count = sum(1 for a, b in zip(original_data, decompressed_data) if a != b)
                    diff_percent = diff_count / len(original_data) if len(original_data) > 0 else 0
                    
                    logger.error(
                        f"Data integrity check failed: {diff_count} differences "
                        f"({diff_percent:.2%})"
                    )
                    results["data_match"] = False
                    results["diff_count"] = diff_count
                    results["diff_percent"] = diff_percent
        
        # Calculate total time
        total_time = time.time() - start_time
        results["total_time"] = total_time
        
        logger.info(
            f"Total pipeline time: {total_time:.2f}s"
        )
        
        results["success"] = True
        
    except Exception as e:
        logger.error(f"Pipeline test failed: {e}")
        results["success"] = False
        results["error"] = str(e)
    
    finally:
        # Clean up test files
        if not keep_files:
            for file_path in test_files:
                try:
                    if os.path.exists(file_path):
                        os.unlink(file_path)
                        logger.debug(f"Removed test file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove test file {file_path}: {e}")
    
    return results


def run_stress_tests(args: argparse.Namespace) -> List[Dict[str, Any]]:
    """
    Run a series of stress tests.
    
    Args:
        args: Command-line arguments
        
    Returns:
        List of test results
    """
    results = []
    test_files = []
    
    try:
        # Generate test files of different sizes and patterns
        test_configs = []
        
        # Small files (100KB - 1MB)
        for size in [0.1, 0.5, 1]:
            for pattern in ["random", "sequential", "repeating", "zeros"]:
                test_configs.append({
                    "size": size,
                    "pattern": pattern,
                    "compression_level": 9,
                    "ecc_level": ECCLevel.M,
                    "introduce_errors": False,
                })
        
        # Medium files (5MB - 10MB)
        for size in [5, 10]:
            for pattern in ["random", "sequential"]:
                test_configs.append({
                    "size": size,
                    "pattern": pattern,
                    "compression_level": 9,
                    "ecc_level": ECCLevel.M,
                    "introduce_errors": False,
                })
        
        # Error correction tests (with small files)
        for ecc_level in [ECCLevel.L, ECCLevel.M, ECCLevel.Q, ECCLevel.H]:
            for error_rate, error_pattern in [
                (0.001, "random"),  # 0.1% random errors
                (0.01, "random"),   # 1% random errors
                (0.01, "burst"),    # 1% burst errors
                (0.01, "edge"),     # 1% edge errors
            ]:
                test_configs.append({
                    "size": 0.5,
                    "pattern": "random",
                    "compression_level": 9,
                    "ecc_level": ecc_level,
                    "introduce_errors": True,
                    "error_rate": error_rate,
                    "error_pattern": error_pattern,
                })
        
        # Large file test (if enabled)
        if args.large_file:
            test_configs.append({
                "size": args.large_file_size,
                "pattern": "random",
                "compression_level": 9,
                "ecc_level": ECCLevel.M,
                "introduce_errors": False,
            })
        
        # Run tests
        for config in test_configs:
            logger.info(f"Running test with config: {config}")
            
            # Generate test file
            test_file = generate_test_file(
                config["size"],
                pattern=config["pattern"],
            )
            test_files.append(test_file)
            
            # Run pipeline test
            result = run_pipeline_test(
                test_file,
                compression_level=config["compression_level"],
                ecc_level=config["ecc_level"],
                introduce_errors_flag=config.get("introduce_errors", False),
                error_rate=config.get("error_rate", 0.01),
                error_pattern=config.get("error_pattern", "random"),
                keep_files=args.keep_files,
            )
            
            results.append(result)
            
            # Log result
            if result["success"]:
                logger.info(f"Test passed: {config}")
            else:
                logger.error(f"Test failed: {config}, error: {result['error']}")
    
    finally:
        # Clean up test files
        if not args.keep_files:
            for file_path in test_files:
                try:
                    if os.path.exists(file_path):
                        os.unlink(file_path)
                        logger.debug(f"Removed test file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove test file {file_path}: {e}")
    
    return results


def main():
    """Run stress tests."""
    parser = argparse.ArgumentParser(description="MegaCode stress test")
    parser.add_argument("--large-file", action="store_true", help="Include large file test")
    parser.add_argument("--large-file-size", type=int, default=50, help="Size of large file in MB")
    parser.add_argument("--keep-files", action="store_true", help="Keep intermediate files")
    args = parser.parse_args()
    
    logger.info("Running MegaCode stress tests")
    
    # Set random seed for reproducibility
    random.seed(42)
    
    # Run stress tests
    results = run_stress_tests(args)
    
    # Print summary
    success_count = sum(1 for r in results if r["success"])
    logger.info(f"Stress tests completed: {success_count}/{len(results)} passed")
    
    # Print detailed results for failed tests
    for i, result in enumerate(results):
        if not result["success"]:
            logger.error(f"Failed test {i+1}: {result}")


if __name__ == "__main__":
    main()
