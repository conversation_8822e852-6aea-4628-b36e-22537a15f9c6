#!/usr/bin/env python3
"""
Standalone test script for MegaCode encoder.
"""

import os
import sys
import logging
import gzip
import struct

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
COMPRESSION_GZIP = "gzip"
COMPRESSION_NONE = "none"


def compress_data(data, method=COMPRESSION_GZIP, level=9):
    """
    Compress data using the specified method.
    
    Args:
        data: The data to compress (string or bytes)
        method: Compression method ('gzip' or 'none')
        level: Compression level (0-9 for gzip)
    
    Returns:
        Compressed data as bytes
    """
    # Convert string to bytes if needed
    if isinstance(data, str):
        data_bytes = data.encode("utf-8")
    else:
        data_bytes = data
    
    # Apply compression
    if method == COMPRESSION_GZIP:
        logger.info(f"Compressing {len(data_bytes)} bytes with gzip (level {level})")
        compressed = gzip.compress(data_bytes, compresslevel=level)
    elif method == COMPRESSION_NONE:
        logger.info("No compression applied")
        compressed = data_bytes
    else:
        raise ValueError(f"Unsupported compression method: {method}")
    
    # Add header with original size and compression method
    header = struct.pack(
        "!IB",
        len(data_bytes),  # Original size (4 bytes)
        {
            COMPRESSION_GZIP: 1,
            COMPRESSION_NONE: 0,
        }.get(method, 0),  # Compression method (1 byte)
    )
    
    logger.info(
        f"Compression ratio: {len(compressed) / len(data_bytes):.2f} "
        f"({len(data_bytes)} -> {len(compressed)} bytes)"
    )
    
    return header + compressed


def decompress_data(compressed_data):
    """
    Decompress data that was compressed with compress_data.
    
    Args:
        compressed_data: The compressed data with header
    
    Returns:
        Decompressed data as bytes
    """
    # Extract header
    header_size = struct.calcsize("!IB")
    if len(compressed_data) < header_size:
        raise ValueError("Compressed data is too small to contain a valid header")
    
    original_size, compression_method = struct.unpack(
        "!IB", compressed_data[:header_size]
    )
    
    # Get compressed data without header
    compressed = compressed_data[header_size:]
    
    # Apply decompression
    if compression_method == 1:  # gzip
        logger.info(f"Decompressing {len(compressed)} bytes with gzip")
        decompressed = gzip.decompress(compressed)
    elif compression_method == 0:  # none
        logger.info("No decompression needed")
        decompressed = compressed
    else:
        raise ValueError(f"Unsupported compression method: {compression_method}")
    
    # Verify size
    if len(decompressed) != original_size:
        logger.warning(
            f"Decompressed size ({len(decompressed)}) doesn't match "
            f"original size ({original_size})"
        )
    
    return decompressed


def segment_data(data, segment_size=4 * 1024):
    """
    Split data into fixed-size segments with metadata.
    
    Args:
        data: The data to segment
        segment_size: Maximum size of each segment in bytes
    
    Returns:
        List of segments, each containing:
        - segment_id: Segment index
        - total_segments: Total number of segments
        - data: Segment data
    """
    # Calculate number of segments
    total_segments = (len(data) + segment_size - 1) // segment_size
    logger.info(f"Segmenting {len(data)} bytes into {total_segments} segments")
    
    segments = []
    for i in range(total_segments):
        # Extract segment data
        start = i * segment_size
        end = min(start + segment_size, len(data))
        segment_data = data[start:end]
        
        # Create segment with metadata
        segment = {
            "segment_id": i,
            "total_segments": total_segments,
            "data": segment_data,
        }
        segments.append(segment)
    
    return segments


def reassemble_segments(segments):
    """
    Reassemble segmented data back into a single byte array.
    
    Args:
        segments: List of segments as returned by segment_data
    
    Returns:
        Reassembled data as bytes
    """
    # Check if we have all segments
    if not segments:
        return b""
    
    # Get total segments from any segment
    total_segments = segments[0]["total_segments"]
    
    # Sort segments by ID
    sorted_segments = sorted(segments, key=lambda s: s["segment_id"])
    
    # Check for missing segments
    segment_ids = [s["segment_id"] for s in sorted_segments]
    expected_ids = set(range(total_segments))
    missing_ids = expected_ids - set(segment_ids)
    
    if missing_ids:
        logger.warning(f"Missing segments: {missing_ids}")
        raise ValueError(f"Cannot reassemble: missing {len(missing_ids)} segments")
    
    # Concatenate segment data
    reassembled = b"".join(segment["data"] for segment in sorted_segments)
    logger.info(f"Reassembled {len(reassembled)} bytes from {len(segments)} segments")
    
    return reassembled


def test_compression():
    """Test compression and decompression."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the compression module."
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Test compression with gzip
    compressed = compress_data(test_data, method=COMPRESSION_GZIP)
    logger.info(f"Compressed data (gzip): {len(compressed)} bytes")
    
    # Test decompression
    decompressed = decompress_data(compressed)
    logger.info(f"Decompressed data: {len(decompressed)} bytes")
    
    # Verify data integrity
    assert decompressed == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")
    
    # Test no compression
    uncompressed = compress_data(test_data, method=COMPRESSION_NONE)
    logger.info(f"Uncompressed data: {len(uncompressed)} bytes")
    
    # Test decompression of uncompressed data
    decompressed_uncompressed = decompress_data(uncompressed)
    logger.info(f"Decompressed uncompressed data: {len(decompressed_uncompressed)} bytes")
    
    # Verify data integrity
    assert decompressed_uncompressed == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")


def test_segmentation():
    """Test data segmentation and reassembly."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the segmentation module."
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Test segmentation with small segment size
    segment_size = 10
    segments = segment_data(test_data, segment_size=segment_size)
    logger.info(f"Segmented into {len(segments)} segments of size {segment_size}")
    
    # Verify segment sizes
    for i, segment in enumerate(segments):
        logger.info(f"Segment {i}: {len(segment['data'])} bytes")
        if i < len(segments) - 1:
            assert len(segment["data"]) == segment_size, f"Segment {i} size mismatch"
        else:
            # Last segment may be smaller
            assert len(segment["data"]) <= segment_size, f"Last segment size mismatch"
    
    # Test reassembly
    reassembled = reassemble_segments(segments)
    logger.info(f"Reassembled data: {len(reassembled)} bytes")
    
    # Verify data integrity
    assert reassembled == test_data, "Data integrity check failed"
    logger.info("Data integrity check passed")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode standalone functions")
    
    test_compression()
    test_segmentation()
    
    logger.info("All tests completed successfully")


if __name__ == "__main__":
    main()
