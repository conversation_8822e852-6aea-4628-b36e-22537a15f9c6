#!/usr/bin/env python3
"""
Simple test script for MegaCode encoder.
"""

import os
import sys
import logging
from megacode.compression import compress_data, segment_data
from megacode.ecc import apply_reed_solomon, ECCLevel
from megacode.fountain import generate_fountain_packets
from megacode.symbol import map_bits_to_colors
from megacode.layout import generate_grid
from megacode.renderer import render_svg, render_png

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def main():
    """Test the MegaCode encoder with a simple example."""
    # Create test data
    test_data = b"Hello, MegaCode! This is a test of the high-capacity encoding system."
    logger.info(f"Test data: {len(test_data)} bytes")
    
    # Compress data
    compressed_data = compress_data(test_data, method="brotli")
    logger.info(f"Compressed data: {len(compressed_data)} bytes")
    
    # Segment data
    segments = segment_data(compressed_data)
    logger.info(f"Segmented data into {len(segments)} chunks")
    
    # Apply Reed-Solomon error correction
    rs_blocks = []
    for segment in segments:
        segment_blocks = apply_reed_solomon(segment["data"], level=ECCLevel.M)
        rs_blocks.extend(segment_blocks)
    logger.info(f"Applied Reed-Solomon ECC to {len(rs_blocks)} blocks")
    
    # Generate fountain packets
    rs_data = [block.to_bytes() for block in rs_blocks]
    packets = generate_fountain_packets(rs_data, overhead=0.1)
    logger.info(f"Generated {len(packets)} fountain packets")
    
    # Convert packets to bytes
    packet_data = [packet.to_bytes() for packet in packets]
    combined_data = b"".join(packet_data)
    logger.info(f"Combined packet data: {len(combined_data)} bytes")
    
    # Map bits to colors
    colors = map_bits_to_colors(combined_data, palette="8-color")
    logger.info(f"Mapped data to {len(colors)} colors")
    
    # Generate grid layout
    layout = generate_grid(colors, finder_patterns=True, calibration_patterns=True)
    logger.info(f"Generated grid layout of size {layout.width}x{layout.height}")
    
    # Render output
    render_svg(layout, module_size=10, output_file="test_output.svg")
    render_png(layout, module_size=10, output_file="test_output.png")
    logger.info("Rendered output to test_output.svg and test_output.png")
    
    logger.info("Test completed successfully")


if __name__ == "__main__":
    main()
