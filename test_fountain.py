#!/usr/bin/env python3
"""
Test script for MegaCode fountain code module.
"""

import logging
import random
import struct
import numpy as np
from typing import List, Dict, Set, Tuple, Optional, Union, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class FountainPacket:
    """A single packet in the fountain code."""

    def __init__(
        self,
        data: bytes,
        seed: int,
        block_indices: List[int],
        total_blocks: int,
        original_size: int,
    ):
        """
        Initialize a fountain packet.

        Args:
            data: The packet data (XOR of source blocks)
            seed: Random seed used to generate this packet
            block_indices: Indices of source blocks combined in this packet
            total_blocks: Total number of source blocks
            original_size: Original size of the data in bytes
        """
        self.data = data
        self.seed = seed
        self.block_indices = block_indices
        self.total_blocks = total_blocks
        self.original_size = original_size

    def to_bytes(self) -> bytes:
        """Convert the packet to bytes for storage/transmission."""
        # Header: Magic bytes (2 bytes), seed (4 bytes), total blocks (2 bytes), original size (4 bytes)
        header = struct.pack(
            "!HIHHI",
            0x4D46,  # Magic bytes 'MF' for MegaCode Fountain
            self.seed,
            len(self.block_indices),
            self.total_blocks,
            self.original_size,
        )

        # Block indices (2 bytes each)
        indices_bytes = struct.pack(f"!{len(self.block_indices)}H", *self.block_indices)

        # Combine all parts
        return header + indices_bytes + self.data

    @classmethod
    def from_bytes(cls, data: bytes) -> "FountainPacket":
        """Create a packet from its byte representation."""
        # Parse header
        header_size = struct.calcsize("!HIHHI")
        magic, seed, num_indices, total_blocks, original_size = struct.unpack(
            "!HIHHI", data[:header_size]
        )

        # Check magic bytes
        if magic != 0x4D46:
            logger.warning(f"Invalid magic bytes: {magic:04x}")

        # Parse block indices
        indices_size = num_indices * struct.calcsize("!H")
        indices_start = header_size
        indices_end = indices_start + indices_size

        block_indices = list(
            struct.unpack(
                f"!{num_indices}H", data[indices_start:indices_end]
            )
        )

        # Extract packet data
        packet_data = data[indices_end:]

        return cls(
            data=packet_data,
            seed=seed,
            block_indices=block_indices,
            total_blocks=total_blocks,
            original_size=original_size,
        )


def _robust_soliton_distribution(
    K: int, c: float = 0.03, delta: float = 0.5
) -> np.ndarray:
    """
    Generate a robust soliton distribution for LT codes.

    Args:
        K: Number of source blocks
        c: Parameter controlling the distribution
        delta: Failure probability

    Returns:
        Array of probabilities for selecting 1, 2, ..., K blocks
    """
    # Ideal soliton distribution
    rho = np.zeros(K + 1)
    rho[1] = 1.0 / K
    for i in range(2, K + 1):
        rho[i] = 1.0 / (i * (i - 1))

    # Robust soliton parameters
    S = c * np.log(K / delta) * np.sqrt(K)
    S = int(S) if S >= 1 else 1

    # Robust soliton component
    tau = np.zeros(K + 1)
    for i in range(1, S + 1):
        tau[i] = S / (K * i)
    tau[S] = S * np.log(S / delta) / K

    # Combine distributions
    mu = rho + tau

    # Normalize
    return mu / np.sum(mu)


def _select_blocks(
    K: int, seed: int, distribution: np.ndarray
) -> List[int]:
    """
    Select source blocks to combine for a packet.

    Args:
        K: Number of source blocks
        seed: Random seed
        distribution: Probability distribution for number of blocks

    Returns:
        List of selected block indices
    """
    # Set random seed for reproducibility
    rng = random.Random(seed)

    # Sample number of blocks from distribution
    d_max = len(distribution) - 1
    d = 1  # Default to 1 block if sampling fails

    # Convert distribution to cumulative
    cum_dist = np.cumsum(distribution)

    # Sample from distribution
    r = rng.random()
    for i in range(1, d_max + 1):
        if r < cum_dist[i]:
            d = i
            break

    # Select d distinct blocks
    block_indices = []
    while len(block_indices) < d:
        idx = rng.randint(0, K - 1)
        if idx not in block_indices:
            block_indices.append(idx)

    return sorted(block_indices)


def generate_fountain_packets(
    blocks: List[bytes],
    num_packets: Optional[int] = None,
    overhead: float = 0.1,
    c: float = 0.03,
    delta: float = 0.01,
    start_seed: int = 0,
) -> List[FountainPacket]:
    """
    Generate fountain-coded packets from source blocks.

    Args:
        blocks: List of source data blocks
        num_packets: Number of packets to generate (if None, calculated from overhead)
        overhead: Overhead factor (e.g., 0.1 = 10% more packets than blocks)
        c: Parameter for robust soliton distribution
        delta: Failure probability
        start_seed: Starting seed for packet generation

    Returns:
        List of FountainPacket objects
    """
    K = len(blocks)

    if K == 0:
        logger.warning("No blocks to encode")
        return []

    # Determine number of packets to generate
    if num_packets is None:
        num_packets = int(K * (1 + overhead))

    logger.info(f"Generating {num_packets} fountain packets from {K} blocks")

    # Ensure all blocks have the same size
    block_size = len(blocks[0])
    for i, block in enumerate(blocks):
        if len(block) != block_size:
            logger.warning(
                f"Block {i} has different size: {len(block)} vs {block_size}"
            )
            # Pad shorter blocks
            if len(block) < block_size:
                blocks[i] = block + bytes([0] * (block_size - len(block)))
            # Truncate longer blocks
            else:
                blocks[i] = block[:block_size]

    # Calculate original data size
    original_size = K * block_size

    # Generate degree distribution
    distribution = _robust_soliton_distribution(K, c, delta)

    # Generate packets
    packets = []
    for i in range(num_packets):
        seed = start_seed + i

        # Select blocks to combine
        block_indices = _select_blocks(K, seed, distribution)

        # XOR selected blocks
        if not block_indices:
            logger.warning(f"No blocks selected for packet {i}")
            continue

        # Start with first block
        packet_data = bytearray(blocks[block_indices[0]])

        # XOR with remaining blocks
        for idx in block_indices[1:]:
            block = blocks[idx]
            for j in range(block_size):
                packet_data[j] ^= block[j]

        # Create packet
        packet = FountainPacket(
            data=bytes(packet_data),
            seed=seed,
            block_indices=block_indices,
            total_blocks=K,
            original_size=original_size,
        )

        packets.append(packet)

    return packets


def decode_fountain_packets(
    packets: List[FountainPacket],
) -> Optional[bytes]:
    """
    Decode fountain-coded packets to recover the original data.

    Args:
        packets: List of FountainPacket objects

    Returns:
        Decoded data if successful, None otherwise
    """
    if not packets:
        logger.warning("No packets to decode")
        return None

    # Extract parameters from first packet
    total_blocks = packets[0].total_blocks
    original_size = packets[0].original_size
    block_size = len(packets[0].data)

    logger.info(
        f"Decoding {len(packets)} packets to recover {total_blocks} blocks "
        f"({original_size} bytes)"
    )

    # Initialize decoded blocks
    decoded_blocks = [None] * total_blocks
    decoded_count = 0

    # Initialize ripple (packets that can decode exactly one block)
    ripple = []

    # Process all packets
    remaining_packets = list(packets)

    # Continue until all blocks are decoded or no more progress
    while decoded_count < total_blocks and remaining_packets:
        # Find packets that can decode exactly one block
        for i, packet in enumerate(remaining_packets):
            # Skip if already in ripple
            if packet in ripple:
                continue

            # Check if this packet can decode exactly one block
            unknown_blocks = [
                idx for idx in packet.block_indices if decoded_blocks[idx] is None
            ]

            if len(unknown_blocks) == 1:
                ripple.append(packet)

        # If ripple is empty, we can't make progress
        if not ripple:
            logger.warning(
                f"Decoding stalled: {decoded_count}/{total_blocks} blocks decoded"
            )
            return None

        # Process a packet from the ripple
        packet = ripple.pop(0)
        remaining_packets.remove(packet)

        # Find the unknown block
        unknown_blocks = [
            idx for idx in packet.block_indices if decoded_blocks[idx] is None
        ]

        if not unknown_blocks:
            # All blocks in this packet are already decoded
            continue

        unknown_idx = unknown_blocks[0]

        # XOR with all known blocks to recover the unknown block
        block_data = bytearray(packet.data)

        for idx in packet.block_indices:
            if idx != unknown_idx and decoded_blocks[idx] is not None:
                for j in range(block_size):
                    block_data[j] ^= decoded_blocks[idx][j]

        # Store the decoded block
        decoded_blocks[unknown_idx] = bytes(block_data)
        decoded_count += 1

        logger.debug(f"Decoded block {unknown_idx} ({decoded_count}/{total_blocks})")

    # Check if all blocks were decoded
    if decoded_count < total_blocks:
        logger.warning(
            f"Failed to decode all blocks: {decoded_count}/{total_blocks}"
        )
        return None

    # Combine blocks to form the original data
    decoded_data = b"".join(decoded_blocks)

    # Truncate to original size
    decoded_data = decoded_data[:original_size]

    logger.info(f"Successfully decoded {len(decoded_data)} bytes")

    return decoded_data


def test_fountain_coding():
    """Test fountain coding."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the fountain coding module."
    logger.info(f"Original data: {len(test_data)} bytes")

    # Split into blocks
    block_size = 10
    blocks = []
    for i in range(0, len(test_data), block_size):
        block = test_data[i:i+block_size]
        # Pad the last block if needed
        if len(block) < block_size:
            block = block + bytes([0] * (block_size - len(block)))
        blocks.append(block)

    # Calculate original size for proper truncation
    original_size = len(test_data)

    logger.info(f"Split into {len(blocks)} blocks of size {block_size}")

    # Generate fountain packets with high overhead for testing
    overhead = 1.0  # 100% overhead for testing
    packets = generate_fountain_packets(blocks, overhead=overhead)

    # Override original_size to match the actual data size
    for packet in packets:
        packet.original_size = original_size

    logger.info(f"Generated {len(packets)} packets")

    # Convert to bytes and back
    packet_bytes = [packet.to_bytes() for packet in packets]
    logger.info(f"Packet sizes: {[len(pb) for pb in packet_bytes]}")

    # Parse packets
    parsed_packets = [FountainPacket.from_bytes(pb) for pb in packet_bytes]

    # Randomly drop some packets (but not too many)
    drop_rate = 0.2
    num_to_drop = int(len(parsed_packets) * drop_rate)
    drop_indices = random.sample(range(len(parsed_packets)), num_to_drop)
    remaining_packets = [p for i, p in enumerate(parsed_packets) if i not in drop_indices]
    logger.info(f"Dropped {num_to_drop} packets, {len(remaining_packets)} remaining")

    # Decode
    decoded_data = decode_fountain_packets(remaining_packets)

    # Verify
    if decoded_data is not None:
        logger.info(f"Decoded data: {len(decoded_data)} bytes")
        # Compare only the actual data (without padding)
        if decoded_data == test_data:
            logger.info("Data integrity check passed")
        else:
            logger.error("Data integrity check failed")
            logger.error(f"Original: {test_data}")
            logger.error(f"Decoded: {decoded_data}")

            # Check if the issue is just padding
            if decoded_data.rstrip(b'\x00') == test_data:
                logger.info("Data matches after removing padding")
    else:
        logger.error("Decoding failed")

        # Try again with more packets
        logger.info("Retrying with more packets...")
        # Generate more packets
        more_packets = generate_fountain_packets(blocks, overhead=2.0)

        # Override original_size to match the actual data size
        for packet in more_packets:
            packet.original_size = original_size

        logger.info(f"Generated {len(more_packets)} packets")

        # Decode with all packets
        all_packets = parsed_packets + more_packets
        decoded_data = decode_fountain_packets(all_packets)

        if decoded_data is not None:
            logger.info(f"Decoded data: {len(decoded_data)} bytes")
            # Compare only the actual data (without padding)
            if decoded_data == test_data:
                logger.info("Data integrity check passed on retry")
            elif decoded_data.rstrip(b'\x00') == test_data:
                logger.info("Data matches after removing padding on retry")
            else:
                logger.error("Data integrity check failed on retry")
        else:
            logger.error("Decoding failed on retry")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode fountain code module")

    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)

    test_fountain_coding()

    logger.info("All tests completed")


if __name__ == "__main__":
    main()
