# MegaCode Core Library

The MegaCode Core Library is a high-performance Rust implementation of the core algorithms used in the MegaCode system, with bindings for Python, Node.js, and other languages.

## Features

- Optimized compression algorithms
- Fast Reed-Solomon error correction
- Efficient Luby Transform fountain codes
- Bit packing and manipulation utilities
- Cross-language bindings

## Installation

### Rust

```bash
cargo build --release
```

### Python

```bash
pip install megacode-core
```

### Node.js

```bash
npm install megacode-core
```

## Usage

### Rust

```rust
use megacode_core::{compression, ecc, fountain};

fn main() {
    let data = b"Hello, MegaCode!";
    
    // Compress data
    let compressed = compression::compress(data, compression::Method::Brotli, 9);
    
    // Apply Reed-Solomon ECC
    let ecc_data = ecc::apply_reed_solomon(&compressed, ecc::Level::M);
    
    // Generate fountain packets
    let packets = fountain::generate_packets(&ecc_data, 0.1);
    
    println!("Generated {} packets", packets.len());
}
```

### Python

```python
import megacode_core

# Compress data
compressed = megacode_core.compress(data, method="brotli", level=9)

# Apply Reed-Solomon ECC
ecc_data = megacode_core.apply_reed_solomon(compressed, level="M")

# Generate fountain packets
packets = megacode_core.generate_fountain_packets(ecc_data, overhead=0.1)
```

## Architecture

The library consists of several modules:

- `compression`: Data compression algorithms
- `ecc`: Error correction coding
- `fountain`: Fountain code implementation
- `bindings`: Language bindings (Python, Node.js)

## License

MIT
