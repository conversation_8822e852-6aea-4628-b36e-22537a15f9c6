[package]
name = "megacode-core"
version = "0.1.0"
edition = "2021"
authors = ["MegaCode Team"]
description = "Core algorithms for the MegaCode high-capacity encoding system"
license = "MIT"
repository = "https://github.com/megacode/megacode-core"

[lib]
name = "megacode_core"
crate-type = ["cdylib", "rlib"]

[dependencies]
brotli = "3.3.0"
flate2 = "1.0.22"
reed-solomon-erasure = "4.0.2"
rand = "0.8.4"
byteorder = "1.4.3"
thiserror = "1.0.30"
log = "0.4.14"
env_logger = "0.9.0"

[dev-dependencies]
criterion = "0.3.5"
tempfile = "3.2.0"

[features]
default = ["python", "node"]
python = ["pyo3"]
node = ["napi"]

[dependencies.pyo3]
version = "0.15.1"
features = ["extension-module"]
optional = true

[dependencies.napi]
version = "2.0.0"
optional = true

[dependencies.napi-derive]
version = "2.0.0"
optional = true

[[bench]]
name = "compression"
harness = false

[[bench]]
name = "ecc"
harness = false

[[bench]]
name = "fountain"
harness = false
