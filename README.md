# MegaCode: High-Capacity Physical Data Encoding System

MegaCode is a system for generating and scanning high-capacity 2D and 3D codes capable of storing up to 4MB of data (approximately 1 million AI prompt tokens).

## Overview

MegaCode addresses the limitation of traditional QR codes (~3 KB capacity) by leveraging color-enhanced 2D modules, mosaic tiling, and 3D-layer embedding to pack multi-megabyte payloads into printable or 3D-printable formats. It enables offline data transfer, artifact tagging, and interactive exhibits, connecting physical objects to rich digital content without network dependency.

## Features

- **High Capacity**: Store up to 4 MB of compressed text per code
- **Robustness**: Decode with ≥95% accuracy under ≤30% module/tile occlusion or damage
- **Usability**: End-to-end decode in ≤120 seconds on a modern smartphone
- **Portability**: Support 2D print (SVG/PNG at ≥600 dpi) and 3D print (STL with ≤0.1 mm layers)
- **Integrability**: Expose local REST APIs for encode/decode operations

## Repository Structure

The project is organized into several repositories:

- **megacode-encoder**: Python library and CLI for generating MegaCode symbols
- **megacode-scanner**: Python library and CLI for decoding MegaCode symbols
- **megacode-backend**: Node.js/Express API for web-based encoding and decoding
- **megacode-mobile**: Flutter mobile app for scanning MegaCode symbols
- **megacode-core**: Rust library with core algorithms and language bindings

## Getting Started

### Prerequisites

- Python 3.8+
- Node.js 14+
- Rust 1.55+
- Flutter 2.5+

### Installation

1. Clone the repository:

```bash
git clone https://github.com/megacode/megacode.git
cd megacode
```

2. Install dependencies for each component:

```bash
# Encoder
cd megacode-encoder
pip install -r requirements.txt

# Scanner
cd ../megacode-scanner
pip install -r requirements.txt

# Backend
cd ../megacode-backend
npm install

# Core (optional)
cd ../megacode-core
cargo build --release
```

3. Install the Flutter app:

```bash
cd ../megacode-mobile
flutter pub get
```

### Usage

#### Encoding

```bash
cd megacode-encoder
python -m megacode.cli.encode --input data.txt --output code.svg --format svg --palette 8-color --ecc-level M
```

#### Decoding

```bash
cd megacode-scanner
python -m megacode.cli.decode --input code.png --output decoded.txt
```

#### Running the API

```bash
cd megacode-backend
npm start
```

#### Running the Mobile App

```bash
cd megacode-mobile
flutter run
```

## Documentation

- [Encoder Documentation](megacode-encoder/README.md)
- [Scanner Documentation](megacode-scanner/README.md)
- [Backend API Documentation](megacode-backend/README.md)
- [Mobile App Documentation](megacode-mobile/README.md)
- [Core Library Documentation](megacode-core/README.md)

## Architecture

MegaCode uses a multi-stage pipeline for encoding and decoding:

1. **Data Preparation**: Compression, segmentation, and error correction
2. **Symbol Generation**: Mapping bits to colors or mosaic tiles
3. **Rendering**: Generating SVG/PDF/STL output
4. **Scanning**: Image acquisition, perspective correction, color calibration
5. **Decoding**: Symbol classification, error correction, data reconstruction

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
