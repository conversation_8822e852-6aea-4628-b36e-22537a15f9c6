/**
 * API Routes for MegaCode Backend
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');

// Import controllers
const encodeController = require('./controllers/encodeController');
const decodeController = require('./controllers/decodeController');
const statusController = require('./controllers/statusController');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../temp/uploads');
    fs.ensureDirSync(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, `${uniqueId}${ext}`);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept images and text files
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'application/json',
      'application/octet-stream',
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`));
    }
  },
});

/**
 * @route GET /api/status
 * @description Get API status
 * @access Public
 */
router.get('/status', statusController.getStatus);

/**
 * @route POST /api/encode
 * @description Encode data into a MegaCode
 * @access Public
 */
router.post('/encode', upload.single('file'), encodeController.encodeData);

/**
 * @route POST /api/encode/text
 * @description Encode text into a MegaCode
 * @access Public
 */
router.post('/encode/text', encodeController.encodeText);

/**
 * @route POST /api/encode/url
 * @description Encode URL into a MegaCode
 * @access Public
 */
router.post('/encode/url', encodeController.encodeUrl);

/**
 * @route POST /api/decode
 * @description Decode a MegaCode image
 * @access Public
 */
router.post('/decode', upload.single('image'), decodeController.decodeImage);

/**
 * @route GET /api/decode/:id
 * @description Get decode result by ID
 * @access Public
 */
router.get('/decode/:id', decodeController.getDecodeResult);

/**
 * @route GET /api/encode/:id
 * @description Get encode result by ID
 * @access Public
 */
router.get('/encode/:id', encodeController.getEncodeResult);

/**
 * @route GET /api/download/:id
 * @description Download encoded or decoded file
 * @access Public
 */
router.get('/download/:id', (req, res) => {
  const { id } = req.params;
  const filePath = path.join(__dirname, `../../temp/results/${id}`);
  
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      success: false,
      error: 'File not found',
    });
  }
  
  res.download(filePath);
});

module.exports = router;
