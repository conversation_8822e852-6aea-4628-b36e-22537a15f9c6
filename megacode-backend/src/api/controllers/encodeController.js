/**
 * Encode Controller for MegaCode Backend
 */

const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const axios = require('axios');

// Import services
const { saveJob, getJob, updateJob } = require('../services/jobService');

/**
 * Encode data into a MegaCode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.encodeData = async (req, res) => {
  try {
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded',
      });
    }
    
    // Get encoding options from request
    const {
      eccLevel = 'M',
      palette = '8-color',
      format = 'svg',
      size = 'medium',
    } = req.body;
    
    // Validate options
    if (!['L', 'M', 'Q', 'H'].includes(eccLevel)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid ECC level. Must be one of: L, M, Q, H',
      });
    }
    
    if (!['bw', '4-color', '8-color', '16-color'].includes(palette)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid palette. Must be one of: bw, 4-color, 8-color, 16-color',
      });
    }
    
    if (!['svg', 'png'].includes(format)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid format. Must be one of: svg, png',
      });
    }
    
    if (!['small', 'medium', 'large'].includes(size)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid size. Must be one of: small, medium, large',
      });
    }
    
    // Create job ID
    const jobId = uuidv4();
    
    // Create job record
    const job = {
      id: jobId,
      type: 'encode',
      status: 'pending',
      createdAt: new Date().toISOString(),
      options: {
        eccLevel,
        palette,
        format,
        size,
      },
      inputFile: req.file.path,
      outputFile: null,
    };
    
    // Save job
    await saveJob(job);
    
    // Start encoding process asynchronously
    processEncodeJob(job);
    
    // Return job ID
    return res.status(202).json({
      success: true,
      message: 'Encoding job started',
      jobId,
    });
  } catch (error) {
    console.error('Error in encodeData:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Encode text into a MegaCode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.encodeText = async (req, res) => {
  try {
    // Check if text was provided
    if (!req.body.text) {
      return res.status(400).json({
        success: false,
        error: 'No text provided',
      });
    }
    
    // Get text from request
    const { text } = req.body;
    
    // Get encoding options from request
    const {
      eccLevel = 'M',
      palette = '8-color',
      format = 'svg',
      size = 'medium',
    } = req.body;
    
    // Validate options
    if (!['L', 'M', 'Q', 'H'].includes(eccLevel)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid ECC level. Must be one of: L, M, Q, H',
      });
    }
    
    if (!['bw', '4-color', '8-color', '16-color'].includes(palette)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid palette. Must be one of: bw, 4-color, 8-color, 16-color',
      });
    }
    
    if (!['svg', 'png'].includes(format)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid format. Must be one of: svg, png',
      });
    }
    
    if (!['small', 'medium', 'large'].includes(size)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid size. Must be one of: small, medium, large',
      });
    }
    
    // Create job ID
    const jobId = uuidv4();
    
    // Create temporary file for text
    const tempDir = path.join(__dirname, '../../../temp/uploads');
    fs.ensureDirSync(tempDir);
    const textFilePath = path.join(tempDir, `${jobId}.txt`);
    
    // Write text to file
    await fs.writeFile(textFilePath, text, 'utf8');
    
    // Create job record
    const job = {
      id: jobId,
      type: 'encode',
      status: 'pending',
      createdAt: new Date().toISOString(),
      options: {
        eccLevel,
        palette,
        format,
        size,
        contentType: 'text',
      },
      inputFile: textFilePath,
      outputFile: null,
    };
    
    // Save job
    await saveJob(job);
    
    // Start encoding process asynchronously
    processEncodeJob(job);
    
    // Return job ID
    return res.status(202).json({
      success: true,
      message: 'Encoding job started',
      jobId,
    });
  } catch (error) {
    console.error('Error in encodeText:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Encode URL into a MegaCode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.encodeUrl = async (req, res) => {
  try {
    // Check if URL was provided
    if (!req.body.url) {
      return res.status(400).json({
        success: false,
        error: 'No URL provided',
      });
    }
    
    // Get URL from request
    const { url } = req.body;
    
    // Validate URL
    try {
      new URL(url);
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL',
      });
    }
    
    // Get encoding options from request
    const {
      eccLevel = 'M',
      palette = '8-color',
      format = 'svg',
      size = 'medium',
    } = req.body;
    
    // Validate options
    if (!['L', 'M', 'Q', 'H'].includes(eccLevel)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid ECC level. Must be one of: L, M, Q, H',
      });
    }
    
    if (!['bw', '4-color', '8-color', '16-color'].includes(palette)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid palette. Must be one of: bw, 4-color, 8-color, 16-color',
      });
    }
    
    if (!['svg', 'png'].includes(format)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid format. Must be one of: svg, png',
      });
    }
    
    if (!['small', 'medium', 'large'].includes(size)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid size. Must be one of: small, medium, large',
      });
    }
    
    // Create job ID
    const jobId = uuidv4();
    
    // Create temporary file for URL
    const tempDir = path.join(__dirname, '../../../temp/uploads');
    fs.ensureDirSync(tempDir);
    const urlFilePath = path.join(tempDir, `${jobId}.txt`);
    
    // Write URL to file
    await fs.writeFile(urlFilePath, url, 'utf8');
    
    // Create job record
    const job = {
      id: jobId,
      type: 'encode',
      status: 'pending',
      createdAt: new Date().toISOString(),
      options: {
        eccLevel,
        palette,
        format,
        size,
        contentType: 'url',
      },
      inputFile: urlFilePath,
      outputFile: null,
    };
    
    // Save job
    await saveJob(job);
    
    // Start encoding process asynchronously
    processEncodeJob(job);
    
    // Return job ID
    return res.status(202).json({
      success: true,
      message: 'Encoding job started',
      jobId,
    });
  } catch (error) {
    console.error('Error in encodeUrl:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Get encode result by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEncodeResult = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get job from database
    const job = await getJob(id);
    
    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found',
      });
    }
    
    // Return job status
    return res.status(200).json({
      success: true,
      job: {
        id: job.id,
        status: job.status,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
        options: job.options,
        result: job.result,
      },
    });
  } catch (error) {
    console.error('Error in getEncodeResult:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Process encode job
 * @param {Object} job - Job object
 */
async function processEncodeJob(job) {
  try {
    // Update job status
    job.status = 'processing';
    await updateJob(job);
    
    // Create output directory
    const outputDir = path.join(__dirname, '../../../temp/results');
    fs.ensureDirSync(outputDir);
    
    // Set output file path
    const outputFile = path.join(outputDir, `${job.id}.${job.options.format}`);
    job.outputFile = outputFile;
    
    // Map size to module size
    const moduleSizeMap = {
      small: 5,
      medium: 10,
      large: 20,
    };
    const moduleSize = moduleSizeMap[job.options.size] || 10;
    
    // Build command arguments
    const args = [
      '-i', job.inputFile,
      '-o', outputFile,
      '--ecc', job.options.eccLevel,
      '--palette', job.options.palette,
      '--module-size', moduleSize.toString(),
    ];
    
    // Spawn encoder process
    const encoder = spawn('python', ['-m', 'megacode.cli.encode', ...args]);
    
    // Collect output
    let stdout = '';
    let stderr = '';
    
    encoder.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    encoder.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    // Handle process completion
    encoder.on('close', async (code) => {
      if (code === 0) {
        // Encoding successful
        job.status = 'completed';
        job.completedAt = new Date().toISOString();
        job.result = {
          outputFile,
          downloadUrl: `/api/download/${job.id}`,
          previewUrl: `/api/preview/${job.id}`,
        };
      } else {
        // Encoding failed
        job.status = 'failed';
        job.error = stderr || 'Encoding process failed';
      }
      
      // Update job
      await updateJob(job);
    });
  } catch (error) {
    console.error('Error in processEncodeJob:', error);
    
    // Update job status
    job.status = 'failed';
    job.error = error.message || 'Internal Server Error';
    await updateJob(job);
  }
}
