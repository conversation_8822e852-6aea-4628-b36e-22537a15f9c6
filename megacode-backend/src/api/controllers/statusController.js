/**
 * Status Controller for MegaCode Backend
 */

const os = require('os');
const { version } = require('../../../package.json');

/**
 * Get API status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStatus = (req, res) => {
  try {
    // Get system information
    const uptime = process.uptime();
    const memory = process.memoryUsage();
    const cpus = os.cpus();
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    
    // Return status
    return res.status(200).json({
      success: true,
      status: 'ok',
      version,
      system: {
        hostname,
        platform,
        arch,
        uptime,
        memory: {
          rss: Math.round(memory.rss / 1024 / 1024) + ' MB',
          heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + ' MB',
          heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + ' MB',
        },
        cpus: cpus.length,
      },
    });
  } catch (error) {
    console.error('Error in getStatus:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};
