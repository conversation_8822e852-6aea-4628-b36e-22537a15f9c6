/**
 * Decode Controller for MegaCode Backend
 */

const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');

// Import services
const { saveJob, getJob, updateJob } = require('../services/jobService');

/**
 * Decode a MegaCode image
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.decodeImage = async (req, res) => {
  try {
    // Check if image was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image uploaded',
      });
    }
    
    // Get decoding options from request
    const {
      format = 'auto',
      palette = 'auto',
      debug = false,
    } = req.body;
    
    // Validate options
    if (!['auto', 'text', 'binary'].includes(format)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid format. Must be one of: auto, text, binary',
      });
    }
    
    if (!['auto', 'bw', '4-color', '8-color', '16-color'].includes(palette)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid palette. Must be one of: auto, bw, 4-color, 8-color, 16-color',
      });
    }
    
    // Create job ID
    const jobId = uuidv4();
    
    // Create job record
    const job = {
      id: jobId,
      type: 'decode',
      status: 'pending',
      createdAt: new Date().toISOString(),
      options: {
        format,
        palette,
        debug: debug === 'true' || debug === true,
      },
      inputFile: req.file.path,
      outputFile: null,
    };
    
    // Save job
    await saveJob(job);
    
    // Start decoding process asynchronously
    processDecodeJob(job);
    
    // Return job ID
    return res.status(202).json({
      success: true,
      message: 'Decoding job started',
      jobId,
    });
  } catch (error) {
    console.error('Error in decodeImage:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Get decode result by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDecodeResult = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get job from database
    const job = await getJob(id);
    
    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found',
      });
    }
    
    // Return job status
    return res.status(200).json({
      success: true,
      job: {
        id: job.id,
        status: job.status,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
        options: job.options,
        result: job.result,
      },
    });
  } catch (error) {
    console.error('Error in getDecodeResult:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal Server Error',
    });
  }
};

/**
 * Process decode job
 * @param {Object} job - Job object
 */
async function processDecodeJob(job) {
  try {
    // Update job status
    job.status = 'processing';
    await updateJob(job);
    
    // Create output directory
    const outputDir = path.join(__dirname, '../../../temp/results');
    fs.ensureDirSync(outputDir);
    
    // Set output file path
    const outputFile = path.join(outputDir, `${job.id}.bin`);
    job.outputFile = outputFile;
    
    // Build command arguments
    const args = [
      '--input', job.inputFile,
      '--output', outputFile,
      '--format', job.options.format,
    ];
    
    if (job.options.palette !== 'auto') {
      args.push('--palette', job.options.palette);
    }
    
    if (job.options.debug) {
      args.push('--debug');
    }
    
    // Spawn decoder process
    const decoder = spawn('python', ['-m', 'megacode.cli.decode', ...args]);
    
    // Collect output
    let stdout = '';
    let stderr = '';
    
    decoder.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    decoder.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    // Handle process completion
    decoder.on('close', async (code) => {
      if (code === 0) {
        // Decoding successful
        job.status = 'completed';
        job.completedAt = new Date().toISOString();
        
        // Determine content type
        let contentType = 'application/octet-stream';
        let content = null;
        
        try {
          // Try to read as text
          const fileContent = await fs.readFile(outputFile);
          
          // Check if it's valid UTF-8
          try {
            content = fileContent.toString('utf8');
            contentType = 'text/plain';
            
            // Check if it's JSON
            try {
              JSON.parse(content);
              contentType = 'application/json';
            } catch (e) {
              // Not JSON, keep as text
            }
            
            // Check if it's a URL
            try {
              const url = content.trim();
              new URL(url);
              contentType = 'text/url';
            } catch (e) {
              // Not a URL, keep as text or JSON
            }
          } catch (e) {
            // Not valid UTF-8, keep as binary
            content = null;
          }
        } catch (e) {
          console.error('Error reading output file:', e);
        }
        
        job.result = {
          outputFile,
          downloadUrl: `/api/download/${job.id}`,
          contentType,
          content: contentType.startsWith('text/') || contentType === 'application/json' ? content : null,
        };
        
        if (job.options.debug) {
          // Add debug images
          const debugDir = path.dirname(job.inputFile);
          const debugImages = {};
          
          const debugFiles = [
            'debug_input.png',
            'debug_detection.png',
            'debug_grid.png',
            'debug_symbols.png',
          ];
          
          for (const file of debugFiles) {
            const debugPath = path.join(debugDir, file);
            if (await fs.pathExists(debugPath)) {
              // Copy to results directory
              const resultPath = path.join(outputDir, `${job.id}_${file}`);
              await fs.copy(debugPath, resultPath);
              
              debugImages[file] = `/api/download/${job.id}_${file.replace('.png', '')}`;
            }
          }
          
          job.result.debugImages = debugImages;
        }
      } else {
        // Decoding failed
        job.status = 'failed';
        job.error = stderr || 'Decoding process failed';
      }
      
      // Update job
      await updateJob(job);
    });
  } catch (error) {
    console.error('Error in processDecodeJob:', error);
    
    // Update job status
    job.status = 'failed';
    job.error = error.message || 'Internal Server Error';
    await updateJob(job);
  }
}
