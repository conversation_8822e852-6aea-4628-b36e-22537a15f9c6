/**
 * Job Service for MegaCode Backend
 * 
 * This service handles job storage and retrieval.
 * In a production environment, this would use a database.
 */

const fs = require('fs-extra');
const path = require('path');

// In-memory job storage (for development)
const jobs = new Map();

// Path to job storage file
const jobStoragePath = path.join(__dirname, '../../../temp/jobs.json');

// Initialize job storage
async function initJobStorage() {
  try {
    // Ensure directory exists
    await fs.ensureDir(path.dirname(jobStoragePath));
    
    // Check if jobs file exists
    if (await fs.pathExists(jobStoragePath)) {
      // Load jobs from file
      const jobsData = await fs.readJson(jobStoragePath);
      
      // Populate jobs map
      for (const job of jobsData) {
        jobs.set(job.id, job);
      }
      
      console.log(`Loaded ${jobs.size} jobs from storage`);
    } else {
      // Create empty jobs file
      await fs.writeJson(jobStoragePath, []);
      console.log('Created empty jobs storage');
    }
  } catch (error) {
    console.error('Error initializing job storage:', error);
  }
}

// Initialize job storage on module load
initJobStorage();

/**
 * Save job to storage
 * @param {Object} job - Job object
 * @returns {Promise<void>}
 */
async function saveJob(job) {
  try {
    // Add job to in-memory storage
    jobs.set(job.id, job);
    
    // Save jobs to file
    await persistJobs();
    
    return job;
  } catch (error) {
    console.error('Error saving job:', error);
    throw error;
  }
}

/**
 * Get job from storage
 * @param {string} id - Job ID
 * @returns {Promise<Object|null>} - Job object or null if not found
 */
async function getJob(id) {
  try {
    // Get job from in-memory storage
    return jobs.get(id) || null;
  } catch (error) {
    console.error('Error getting job:', error);
    throw error;
  }
}

/**
 * Update job in storage
 * @param {Object} job - Job object
 * @returns {Promise<Object>} - Updated job object
 */
async function updateJob(job) {
  try {
    // Update job in in-memory storage
    jobs.set(job.id, job);
    
    // Save jobs to file
    await persistJobs();
    
    return job;
  } catch (error) {
    console.error('Error updating job:', error);
    throw error;
  }
}

/**
 * Delete job from storage
 * @param {string} id - Job ID
 * @returns {Promise<boolean>} - True if job was deleted, false if not found
 */
async function deleteJob(id) {
  try {
    // Delete job from in-memory storage
    const deleted = jobs.delete(id);
    
    // Save jobs to file
    if (deleted) {
      await persistJobs();
    }
    
    return deleted;
  } catch (error) {
    console.error('Error deleting job:', error);
    throw error;
  }
}

/**
 * Persist jobs to file
 * @returns {Promise<void>}
 */
async function persistJobs() {
  try {
    // Convert jobs map to array
    const jobsArray = Array.from(jobs.values());
    
    // Save jobs to file
    await fs.writeJson(jobStoragePath, jobsArray);
  } catch (error) {
    console.error('Error persisting jobs:', error);
    throw error;
  }
}

/**
 * Clean up old jobs
 * @param {number} maxAgeHours - Maximum age of jobs in hours
 * @returns {Promise<number>} - Number of jobs deleted
 */
async function cleanupJobs(maxAgeHours = 24) {
  try {
    const now = new Date();
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    let deletedCount = 0;
    
    // Find old jobs
    for (const [id, job] of jobs.entries()) {
      const createdAt = new Date(job.createdAt);
      const age = now - createdAt;
      
      if (age > maxAge) {
        // Delete job
        jobs.delete(id);
        deletedCount++;
        
        // Delete job files
        if (job.inputFile && await fs.pathExists(job.inputFile)) {
          await fs.unlink(job.inputFile);
        }
        
        if (job.outputFile && await fs.pathExists(job.outputFile)) {
          await fs.unlink(job.outputFile);
        }
      }
    }
    
    // Save jobs to file
    if (deletedCount > 0) {
      await persistJobs();
    }
    
    return deletedCount;
  } catch (error) {
    console.error('Error cleaning up jobs:', error);
    throw error;
  }
}

// Export functions
module.exports = {
  saveJob,
  getJob,
  updateJob,
  deleteJob,
  cleanupJobs,
};
