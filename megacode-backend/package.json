{"name": "megacode-backend", "version": "0.1.0", "description": "Backend API for MegaCode encoding and decoding", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.17.1", "cors": "^2.8.5", "body-parser": "^1.19.0", "multer": "^1.4.3", "swagger-ui-express": "^4.1.6", "morgan": "^1.10.0", "winston": "^3.3.3", "dotenv": "^10.0.0", "child_process": "^1.0.2", "fs-extra": "^10.0.0"}, "devDependencies": {"nodemon": "^2.0.12", "jest": "^27.0.6"}, "engines": {"node": ">=14.0.0"}, "author": "MegaCode Team", "license": "MIT"}