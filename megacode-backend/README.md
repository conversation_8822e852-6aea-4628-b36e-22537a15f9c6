# MegaCode Backend API

The MegaCode Backend API provides RESTful endpoints for encoding and decoding high-capacity 2D and 3D codes.

## Features

- `/encode` endpoint for generating MegaCode symbols
- `/decode` endpoint for processing uploaded images
- Job queue for handling long-running tasks
- Web interface for easy interaction
- Swagger documentation

## Installation

```bash
npm install
```

## Usage

### Starting the Server

```bash
npm start
```

### API Endpoints

#### Encode

```
POST /api/encode
Content-Type: application/json

{
  "data": "Base64-encoded data or text",
  "format": "svg",
  "palette": "8-color",
  "eccLevel": "M"
}
```

#### Decode

```
POST /api/decode
Content-Type: multipart/form-data

file: [Image file]
```

## Architecture

The backend consists of several components:

- `api`: Express.js routes and controllers
- `services`: Integration with encoder/scanner
- `web`: Web interface for easy interaction

## License

MIT
