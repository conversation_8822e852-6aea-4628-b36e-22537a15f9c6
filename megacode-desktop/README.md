# MegaCode Desktop

MegaCode Desktop is a PyQt6-based desktop application for encoding and decoding high-capacity 2D and 3D codes generated by the MegaCode system.

## Features

- Encode text or files into MegaCode symbols
- Decode MegaCode symbols from images
- Support for various encoding options:
  - Multiple output formats (SVG, PNG, STL)
  - Compression methods (<PERSON><PERSON>li, GZip)
  - Error correction levels
  - Color palettes
- Modern, user-friendly interface
- Cross-platform (Windows, macOS, Linux)

## Installation

### Prerequisites

- Python 3.8 or higher
- PyQt6
- MegaCode libraries (optional, falls back to CLI if not available)

### Install from Source

1. Clone the repository:

```bash
git clone https://github.com/megacode/megacode.git
cd megacode/megacode-desktop
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Run the application:

```bash
python main.py
```

## Usage

### Encoding

1. Select the "Encode" tab
2. Enter text or load a file to encode
3. Configure encoding options:
   - Output format (SVG, PNG, STL)
   - Compression method and level
   - ECC level
   - Color palette
   - Module size
   - Finder and calibration patterns
4. Click "Encode" to generate the MegaCode symbol
5. Save the output file

### Decoding

1. Select the "Decode" tab
2. Load an image containing a MegaCode symbol
3. Configure decoding options:
   - Color palette
   - Debug mode
4. Click "Decode" to extract the data
5. Save the decoded output

## Configuration

The application settings can be configured through the "Settings" dialog:

- General settings:
  - Theme (Light, Dark, Auto)
  - File handling options
- Encoding defaults:
  - Default format, compression, ECC level, etc.
- Decoding defaults:
  - Default palette, debug mode, etc.
- Advanced settings:
  - API integration

## Development

### Project Structure

- `main.py`: Application entry point
- `megacode_desktop/`: Main package
  - `ui/`: User interface components
  - `core/`: Core functionality
  - `utils/`: Utility functions

### Building

To build a standalone executable:

```bash
pip install pyinstaller
pyinstaller --name MegaCodeDesktop --windowed --icon=megacode_desktop/resources/icons/app.ico main.py
```

## License

MIT License

## Acknowledgments

- MegaCode Team
- PyQt6 and Qt
- Python community
