#!/usr/bin/env python3
"""
Simple PyQt6 application to test if PyQt6 is working correctly.
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget

class SimpleWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple PyQt6 App")
        self.setGeometry(100, 100, 400, 200)
        
        # Create a central widget and layout
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # Add a label
        label = QLabel("Hello, PyQt6!")
        label.setStyleSheet("font-size: 24px;")
        layout.addWidget(label)
        
        # Set the central widget
        self.setCentralWidget(central_widget)

def main():
    app = QApplication(sys.argv)
    window = SimpleWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
