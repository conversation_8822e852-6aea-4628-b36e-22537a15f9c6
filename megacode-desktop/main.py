#!/usr/bin/env python3
"""
MegaCode Desktop Application

A PyQt6-based desktop application for encoding and decoding MegaCode symbols.
"""

import sys
import os
import logging
from PyQt6.QtWidgets import QApplication
import qdarktheme

# Add the parent directory and megacode packages to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "megacode-encoder")))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "megacode-scanner")))

# Import the main window
from megacode_desktop.ui.main_window import MainWindow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def main():
    """Main application entry point."""
    # Create the application
    app = QApplication(sys.argv)
    app.setApplicationName("MegaCode Desktop")
    app.setOrganizationName("MegaCode")
    app.setOrganizationDomain("megacode.example.com")

    # Set the style
    qdarktheme.setup_theme("auto")

    # Create and show the main window
    window = MainWindow()
    window.show()

    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
