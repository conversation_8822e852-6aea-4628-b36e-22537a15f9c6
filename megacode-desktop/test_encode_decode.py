#!/usr/bin/env python3

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add current directory to path
sys.path.append('.')

from megacode_desktop.core.encoder import MegaCodeEncoder
from megacode_desktop.core.decoder import MegaCodeDecoder

def main():
    # Create test text
    test_text = """This is a test text file for MegaCode encoding and decoding.
It contains multiple lines of text to test the text handling capabilities.

The MegaCode system should be able to:
1. Encode this text properly
2. Decode it back to the original text
3. Display it correctly in the output section

Special characters: !@#$%^&*()_+-=[]{}|;':",./<>?
Numbers: 0123456789"""

    # Convert to bytes
    test_data = test_text.encode('utf-8')
    
    # Save the test data to a file
    with open('test_data.txt', 'wb') as f:
        f.write(test_data)
    
    logger.info(f"Created test data file: {len(test_data)} bytes")
    
    # Encode the data
    encoder = MegaCodeEncoder()
    options = {'format': 'png', 'palette': 'bw'}
    
    try:
        encoded_data = encoder.encode(test_data, options)
        
        # Save the encoded data
        with open('test_encoded.png', 'wb') as f:
            f.write(encoded_data)
        
        logger.info(f"Encoded data saved to test_encoded.png: {len(encoded_data)} bytes")
        
        # Now decode it
        decoder = MegaCodeDecoder()
        result = decoder.decode('test_encoded.png', {})
        
        if result['success']:
            logger.info("Decoding successful")
            logger.info(f"Decoded data length: {len(result['data'])} bytes")
            
            try:
                decoded_text = result['data'].decode('utf-8')
                logger.info("Decoded text:")
                print(decoded_text)
                
                # Compare with original
                if decoded_text == test_text:
                    logger.info("Decoded text matches original text!")
                else:
                    logger.warning("Decoded text does not match original text")
                    logger.info("Original text:")
                    print(test_text)
            except UnicodeDecodeError:
                logger.error("Could not decode as UTF-8 text")
                logger.info(f"First 50 bytes: {result['data'][:50]}")
        else:
            logger.error(f"Decoding failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        logger.error(f"Error during encoding/decoding: {e}")

if __name__ == "__main__":
    main()
