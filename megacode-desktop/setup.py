#!/usr/bin/env python3
"""
Setup script for MegaCode Desktop.
"""

import os
from setuptools import setup, find_packages

# Get the long description from the README file
with open(os.path.join(os.path.dirname(__file__), "README.md"), encoding="utf-8") as f:
    long_description = f.read()

# Get the version from the package
with open(os.path.join(os.path.dirname(__file__), "megacode_desktop", "__init__.py"), encoding="utf-8") as f:
    for line in f:
        if line.startswith("__version__"):
            version = line.split("=")[1].strip().strip('"\'')
            break

# Get the requirements from the requirements.txt file
with open(os.path.join(os.path.dirname(__file__), "requirements.txt"), encoding="utf-8") as f:
    requirements = [line.strip() for line in f if line.strip()]

setup(
    name="megacode-desktop",
    version=version,
    description="PyQt6-based desktop application for MegaCode encoding and decoding",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="MegaCode Team",
    author_email="<EMAIL>",
    url="https://github.com/megacode/megacode",
    packages=find_packages(),
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "megacode-desktop=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Graphics",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
)
