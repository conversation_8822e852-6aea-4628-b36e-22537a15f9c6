"""
Custom decoder implementation for MegaCode Desktop.

This module provides a custom decoder implementation that fixes issues
with the original MegaCode decoder.
"""

import logging
import struct
from typing import Dict, Any, List, Optional, Tuple, Set

# Configure logger
logger = logging.getLogger(__name__)

# Try to import required modules
try:
    from megacode.decoding.decoder import Decoder as MegaCodeDecoder
    HAVE_MEGACODE = True
except ImportError:
    HAVE_MEGACODE = False
    logger.warning("MegaCode decoder not available, using fallback")


class CustomDecoder:
    """
    Custom decoder implementation that fixes issues with the original MegaCode decoder.

    This class wraps the original MegaCode decoder and adds fixes for known issues.
    """

    def __init__(self):
        """Initialize the custom decoder."""
        if HAVE_MEGACODE:
            self.original_decoder = MegaCodeDecoder()
        else:
            self.original_decoder = None

    def decode_data(self, raw_data: bytes) -> Dict[str, Any]:
        """
        Decode data from raw bytes.

        Args:
            raw_data: Raw data bytes

        Returns:
            Dictionary with decoding results
        """
        if not HAV<PERSON>_MEGACODE:
            return {
                "success": False,
                "data": None,
                "metadata": None,
                "errors_corrected": 0,
                "error_message": "MegaCode decoder not available"
            }

        try:
            # Check for null bytes and clean them if needed
            cleaned_data = self._clean_null_bytes(raw_data)
            if cleaned_data != raw_data:
                logger.info(f"Cleaned {len(raw_data) - len(cleaned_data)} null bytes from data")
                raw_data = cleaned_data

            # First, try to decode as text
            try:
                text = raw_data.decode('utf-8')
                if all(c.isprintable() or c.isspace() for c in text):
                    # Make sure we're not returning empty data
                    if not text.strip():
                        logger.warning("Decoded text is empty or whitespace only")
                        # Try to recover some content from the original data
                        if len(raw_data) == 0:
                            logger.warning("Raw data is empty, cannot recover")
                        else:
                            logger.info(f"Original data length: {len(raw_data)} bytes")
                            # Return the raw data as-is
                            return {
                                "success": True,
                                "data": raw_data,
                                "metadata": {"format": "raw", "note": "Empty text recovered as raw data"},
                                "errors_corrected": 0,
                                "error_message": None
                            }

                    logger.info("Data appears to be valid text, returning directly")
                    return {
                        "success": True,
                        "data": raw_data,
                        "metadata": {"format": "text", "encoding": "utf-8"},
                        "errors_corrected": 0,
                        "error_message": None
                    }
            except UnicodeDecodeError:
                # Try other common encodings
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        text = raw_data.decode(encoding)
                        if all(c.isprintable() or c.isspace() for c in text) and text.strip():
                            logger.info(f"Data appears to be valid {encoding} text, returning directly")
                            return {
                                "success": True,
                                "data": raw_data,
                                "metadata": {"format": "text", "encoding": encoding},
                                "errors_corrected": 0,
                                "error_message": None
                            }
                    except (UnicodeDecodeError, LookupError):
                        pass

                # Not valid text in any common encoding, continue with normal decoding
                pass

            # Parse fountain packets with custom implementation
            packets = self._parse_fountain_packets(raw_data)

            if not packets:
                logger.warning("No valid fountain packets found, returning raw data")
                # Try to detect if this is a text file with null bytes
                if self._is_text_with_nulls(raw_data):
                    logger.info("Data appears to be text with null bytes")
                    cleaned = self._clean_null_bytes(raw_data)
                    return {
                        "success": True,
                        "data": cleaned,
                        "metadata": {"format": "text", "encoding": "utf-8", "note": "Null bytes removed"},
                        "errors_corrected": len(raw_data) - len(cleaned),
                        "error_message": None
                    }
                return {
                    "success": True,
                    "data": raw_data,
                    "metadata": {"format": "raw"},
                    "errors_corrected": 0,
                    "error_message": None
                }

            # Use our custom fountain packet parser and decoder
            try:
                # Parse fountain packets
                packets = self._parse_fountain_packets(raw_data)

                if not packets:
                    logger.warning("No valid fountain packets found, returning raw data")
                    # Try to detect if this is a text file with null bytes
                    if self._is_text_with_nulls(raw_data):
                        logger.info("Data appears to be text with null bytes")
                        cleaned = self._clean_null_bytes(raw_data)
                        return {
                            "success": True,
                            "data": cleaned,
                            "metadata": {"format": "text", "encoding": "utf-8", "note": "Null bytes removed"},
                            "errors_corrected": len(raw_data) - len(cleaned),
                            "error_message": None
                        }
                    return {
                        "success": True,
                        "data": raw_data,
                        "metadata": {"format": "raw"},
                        "errors_corrected": 0,
                        "error_message": None
                    }

                # Decode fountain packets
                decoded_data = self._decode_fountain_packets(packets)

                if decoded_data is None:
                    logger.warning("Failed to decode fountain packets, trying original decoder")
                    # Try the original decoder as fallback
                    try:
                        result = self.original_decoder.decode_data(raw_data)
                        if result.get("success", False):
                            return result
                    except Exception as e:
                        logger.warning(f"Original decoder also failed: {e}")

                    # If original decoder also fails, return raw data
                    logger.warning("Both decoders failed, returning raw data")
                    # Try to detect if this is a text file with null bytes
                    if self._is_text_with_nulls(raw_data):
                        logger.info("Data appears to be text with null bytes")
                        cleaned = self._clean_null_bytes(raw_data)
                        return {
                            "success": True,
                            "data": cleaned,
                            "metadata": {"format": "text", "encoding": "utf-8", "note": "Null bytes removed"},
                            "errors_corrected": len(raw_data) - len(cleaned),
                            "error_message": None
                        }
                    return {
                        "success": True,
                        "data": raw_data,
                        "metadata": {"format": "raw", "note": "Recovered from error"},
                        "errors_corrected": 0,
                        "error_message": None
                    }

                # Determine format based on content
                format_type = "binary"
                encoding = None

                try:
                    # Try to decode as text
                    text = decoded_data.decode('utf-8')
                    if all(c.isprintable() or c.isspace() for c in text):
                        format_type = "text"
                        encoding = "utf-8"
                except UnicodeDecodeError:
                    # Try other common encodings
                    for enc in ['latin-1', 'cp1252', 'iso-8859-1']:
                        try:
                            text = decoded_data.decode(enc)
                            if all(c.isprintable() or c.isspace() for c in text):
                                format_type = "text"
                                encoding = enc
                                break
                        except (UnicodeDecodeError, LookupError):
                            pass

                # Create metadata
                metadata = {
                    "format": format_type,
                }

                if encoding:
                    metadata["encoding"] = encoding

                # Add packet format if available
                if packets and "format" in packets[0]:
                    metadata["packet_format"] = packets[0]["format"]

                return {
                    "success": True,
                    "data": decoded_data,
                    "metadata": metadata,
                    "errors_corrected": 0,
                    "error_message": None
                }

            except IndexError as e:
                # Specifically catch "list index out of range" errors
                logger.warning(f"Custom decoder failed with IndexError: {e}")
                # Try to extract any meaningful data
                try:
                    # Try to decode as text first
                    text = raw_data.decode('utf-8', errors='replace')
                    if any(c.isprintable() for c in text):
                        logger.info("Extracted text from raw data after IndexError")
                        return {
                            "success": True,
                            "data": raw_data,
                            "metadata": {"format": "text", "encoding": "utf-8", "note": "Recovered from IndexError"},
                            "errors_corrected": 0,
                            "error_message": None
                        }
                except:
                    pass

                # Return raw data as fallback
                return {
                    "success": True,
                    "data": raw_data,
                    "metadata": {"format": "raw", "note": "Recovered from IndexError"},
                    "errors_corrected": 0,
                    "error_message": None
                }



        except IndexError as e:
            # Specifically handle "list index out of range" errors
            logger.error(f"Decoding failed with IndexError: {e}")

            # Try to extract any meaningful data
            try:
                # Try to decode as text first
                text = raw_data.decode('utf-8', errors='replace')
                if any(c.isprintable() for c in text):
                    logger.info("Extracted text from raw data after IndexError in main try block")
                    return {
                        "success": True,
                        "data": raw_data,
                        "metadata": {"format": "text", "encoding": "utf-8", "note": "Recovered from IndexError"},
                        "errors_corrected": 0,
                        "error_message": None
                    }
            except:
                pass

            # Return raw data as fallback
            return {
                "success": True,
                "data": raw_data,
                "metadata": {"format": "raw", "note": "Recovered from IndexError exception"},
                "errors_corrected": 0,
                "error_message": None
            }
        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            # Try to detect if this is a text file with null bytes
            if self._is_text_with_nulls(raw_data):
                logger.info("Data appears to be text with null bytes")
                cleaned = self._clean_null_bytes(raw_data)
                return {
                    "success": True,
                    "data": cleaned,
                    "metadata": {"format": "text", "encoding": "utf-8", "note": "Null bytes removed"},
                    "errors_corrected": len(raw_data) - len(cleaned),
                    "error_message": None
                }
            # Return raw data as fallback
            return {
                "success": True,
                "data": raw_data,
                "metadata": {"format": "raw", "note": "Recovered from exception"},
                "errors_corrected": 0,
                "error_message": None
            }

    def _is_text_with_nulls(self, data: bytes) -> bool:
        """
        Check if data appears to be text with null bytes.

        Args:
            data: Raw data bytes

        Returns:
            True if data appears to be text with null bytes
        """
        # Check if data contains null bytes
        if b'\x00' not in data:
            return False

        # Remove null bytes and try to decode as text
        cleaned = self._clean_null_bytes(data)
        if not cleaned:
            return False

        # Try with UTF-8 first
        try:
            text = cleaned.decode('utf-8')

            # Check if the text is empty or just whitespace
            if not text.strip():
                logger.warning("Cleaned text is empty or whitespace only")
                return False

            # Check if the text contains printable characters
            printable_chars = sum(1 for c in text if c.isprintable() or c.isspace())
            # If more than 80% of characters are printable, it's likely text
            is_text = printable_chars / len(text) > 0.8

            if is_text:
                # Make sure we have some actual content
                non_whitespace = sum(1 for c in text if not c.isspace())
                if non_whitespace < 1:
                    logger.warning("Text contains only whitespace characters")
                    return False

                logger.info("Data appears to be UTF-8 text with null bytes")
                return True
        except UnicodeDecodeError:
            pass

        # Try other common encodings
        for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
            try:
                text = cleaned.decode(encoding)

                # Check if the text is empty or just whitespace
                if not text.strip():
                    continue

                # Check if the text contains printable characters
                printable_chars = sum(1 for c in text if c.isprintable() or c.isspace())
                # If more than 80% of characters are printable, it's likely text
                is_text = printable_chars / len(text) > 0.8

                if is_text:
                    # Make sure we have some actual content
                    non_whitespace = sum(1 for c in text if not c.isspace())
                    if non_whitespace < 1:
                        continue

                    logger.info(f"Data appears to be {encoding} text with null bytes")
                    return True
            except (UnicodeDecodeError, LookupError):
                continue

        # If we get here, it's not recognizable as text in any common encoding
        return False

    def _clean_null_bytes(self, data: bytes) -> bytes:
        """
        Remove null bytes from data.

        Args:
            data: Raw data bytes

        Returns:
            Cleaned data bytes
        """
        # Remove null bytes
        cleaned = data.replace(b'\x00', b'')

        # If cleaning would result in empty data, preserve some of the original
        if not cleaned and data:
            logger.warning("Cleaning would result in empty data, preserving original")
            # Try to extract any non-null content
            chunks = [chunk for chunk in data.split(b'\x00') if chunk]
            if chunks:
                # Join non-empty chunks with spaces
                logger.info(f"Extracted {len(chunks)} non-empty chunks from data")
                return b' '.join(chunks)
            else:
                # If all chunks are empty, return the original data
                logger.warning("No non-empty chunks found, returning original data")
                return data

        return cleaned

    def _parse_fountain_packets(self, data: bytes) -> List[Dict[str, Any]]:
        """
        Parse fountain packets from raw data.

        This implementation attempts to parse standard MegaCode fountain packets
        and falls back to raw data if no valid packets are found.

        Args:
            data: Raw data bytes

        Returns:
            List of fountain packets
        """
        import struct

        packets = []
        offset = 0

        # Define known packet formats
        FORMATS = [
            {
                "name": "standard",
                "header_format": "!HIHHI",  # magic, seed, num_indices, total_blocks, original_size
                "header_size": 14,
                "magic": 0x4D46,  # 'MF'
                "index_size": 2,  # 2 bytes per index
            },
            {
                "name": "compact",
                "header_format": "!HIHB",  # magic, seed, num_indices, total_blocks
                "header_size": 9,
                "magic": 0x4D43,  # 'MC'
                "index_size": 1,  # 1 byte per index
            },
            {
                "name": "legacy",
                "header_format": "!HIH",  # magic, seed, num_indices
                "header_size": 8,
                "magic": 0x4D4C,  # 'ML'
                "index_size": 2,  # 2 bytes per index
            }
        ]

        # Check if data is valid
        if not data:
            logger.warning("Empty data provided to _parse_fountain_packets")
            return []

        # Try to parse fountain packets
        while offset < len(data):
            packet_found = False

            # Try each format
            for fmt in FORMATS:
                # Check if we have enough data for the header
                if offset + fmt["header_size"] > len(data):
                    continue

                try:
                    # Parse header
                    header_values = struct.unpack(
                        fmt["header_format"],
                        data[offset:offset + fmt["header_size"]]
                    )

                    # Check magic number
                    if header_values[0] != fmt["magic"]:
                        continue

                    # Extract header values
                    if fmt["name"] == "standard":
                        _, seed, num_indices, total_blocks, original_size = header_values
                    elif fmt["name"] == "compact":
                        _, seed, num_indices, total_blocks = header_values
                        original_size = total_blocks * 256  # Estimate
                    elif fmt["name"] == "legacy":
                        _, seed, num_indices = header_values
                        total_blocks = 1
                        original_size = len(data)

                    # Check if we have enough data for the indices
                    indices_size = num_indices * fmt["index_size"]
                    if offset + fmt["header_size"] + indices_size > len(data):
                        continue

                    # Parse indices
                    indices = []
                    for i in range(num_indices):
                        idx_offset = offset + fmt["header_size"] + i * fmt["index_size"]
                        if fmt["index_size"] == 2:
                            idx = struct.unpack("!H", data[idx_offset:idx_offset + 2])[0]
                        else:
                            idx = data[idx_offset]
                        indices.append(idx)

                    # Calculate packet size (estimate if needed)
                    if total_blocks > 0 and original_size > 0:
                        block_size = original_size // total_blocks
                    else:
                        block_size = 256  # Default block size

                    packet_size = fmt["header_size"] + indices_size + block_size

                    # Check if we have enough data for the packet
                    if offset + packet_size > len(data):
                        # Use remaining data
                        packet_data = data[offset + fmt["header_size"] + indices_size:]
                    else:
                        packet_data = data[offset + fmt["header_size"] + indices_size:offset + packet_size]

                    # Create packet
                    packet = {
                        "format": fmt["name"],
                        "seed": seed,
                        "block_indices": indices,
                        "total_blocks": total_blocks,
                        "original_size": original_size,
                        "data": packet_data,
                    }

                    packets.append(packet)
                    logger.info(f"Found {fmt['name']} packet: seed={seed}, blocks={total_blocks}, size={len(packet_data)} bytes")

                    # Move to next packet
                    offset += packet_size
                    packet_found = True
                    break

                except Exception as e:
                    logger.debug(f"Failed to parse {fmt['name']} packet at offset {offset}: {e}")

            # If no packet found, move to next byte
            if not packet_found:
                offset += 1

        # If no packets found, create a raw packet
        if not packets:
            try:
                # Check if it's a simple text format
                if all(b in range(32, 127) or b in (9, 10, 13) for b in data):
                    # Likely text data
                    logger.info("No fountain packets found, but data appears to be text")
                    packet = {
                        "format": "raw_text",
                        "seed": 0,
                        "block_indices": [0],
                        "total_blocks": 1,
                        "original_size": len(data),
                        "data": data,
                    }
                    packets.append(packet)
                else:
                    # Binary data
                    logger.info("No fountain packets found, treating as raw binary data")
                    packet = {
                        "format": "raw_binary",
                        "seed": 0,
                        "block_indices": [0],
                        "total_blocks": 1,
                        "original_size": len(data),
                        "data": data,
                    }
                    packets.append(packet)
            except Exception as e:
                logger.warning(f"Failed to parse raw data: {e}")
                # Create a simple raw packet as fallback
                packet = {
                    "format": "raw_binary",
                    "seed": 0,
                    "block_indices": [0],
                    "total_blocks": 1,
                    "original_size": len(data),
                    "data": data,
                }
                packets.append(packet)

        logger.info(f"Parsed {len(packets)} fountain packets")
        return packets

    def _decode_fountain_packets(self, packets: List[Dict[str, Any]]) -> Optional[bytes]:
        """
        Decode fountain packets to recover the original data.

        This implementation handles both standard fountain packets and raw data packets.

        Args:
            packets: List of fountain packets

        Returns:
            Decoded data or None if decoding fails
        """
        if not packets:
            logger.warning("No packets to decode")
            return None

        # Check if we have raw data packets
        if len(packets) == 1 and packets[0].get("format", "") in ["raw_text", "raw_binary"]:
            logger.info(f"Using raw data packet ({len(packets[0]['data'])} bytes)")
            return packets[0]["data"]

        # Extract parameters from first packet
        total_blocks = packets[0]["total_blocks"]
        original_size = packets[0]["original_size"]

        logger.info(
            f"Decoding {len(packets)} packets to recover {total_blocks} blocks "
            f"({original_size} bytes, avg packet size: {len(packets[0]['data'])} bytes)"
        )

        # Initialize decoded blocks
        decoded_blocks = [None] * total_blocks
        decoded_count = 0

        # Initialize ripple (packets that can decode exactly one block)
        ripple = []

        # Process all packets
        remaining_packets = list(packets)

        # Continue until all blocks are decoded or no more progress
        while decoded_count < total_blocks and remaining_packets:
            # Find packets that can decode exactly one block
            for packet in list(remaining_packets):
                # Skip if already in ripple
                if packet in ripple:
                    continue

                # Check if this packet can decode exactly one block
                unknown_blocks = [
                    idx for idx in packet["block_indices"]
                    if idx < len(decoded_blocks) and decoded_blocks[idx] is None
                ]

                if len(unknown_blocks) == 1:
                    ripple.append(packet)
                    remaining_packets.remove(packet)

            # If ripple is empty, we can't make progress
            if not ripple:
                # Try to find a packet with the fewest unknown blocks
                if remaining_packets:
                    best_packet = None
                    min_unknown = float('inf')

                    for packet in remaining_packets:
                        unknown_blocks = [
                            idx for idx in packet["block_indices"]
                            if idx < len(decoded_blocks) and decoded_blocks[idx] is None
                        ]

                        if 0 < len(unknown_blocks) < min_unknown:
                            min_unknown = len(unknown_blocks)
                            best_packet = packet

                    if best_packet:
                        logger.warning(
                            f"Decoding stalled, trying packet with {min_unknown} unknown blocks"
                        )
                        ripple.append(best_packet)
                        remaining_packets.remove(best_packet)
                    else:
                        logger.warning(
                            f"Decoding stalled: {decoded_count}/{total_blocks} blocks decoded"
                        )
                        break
                else:
                    logger.warning(
                        f"Decoding stalled: {decoded_count}/{total_blocks} blocks decoded"
                    )
                    break

            # Process a packet from the ripple
            packet = ripple.pop(0)

            # Find the unknown block
            unknown_blocks = [
                idx for idx in packet["block_indices"]
                if idx < len(decoded_blocks) and decoded_blocks[idx] is None
            ]

            if not unknown_blocks:
                # All blocks in this packet are already decoded
                continue

            unknown_idx = unknown_blocks[0]

            # XOR with all known blocks to recover the unknown block
            block_data = bytearray(packet["data"])

            for idx in packet["block_indices"]:
                if idx != unknown_idx and idx < len(decoded_blocks) and decoded_blocks[idx] is not None:
                    for j in range(min(len(block_data), len(decoded_blocks[idx]))):
                        block_data[j] ^= decoded_blocks[idx][j]

            # Store the decoded block
            decoded_blocks[unknown_idx] = bytes(block_data)
            decoded_count += 1

            logger.debug(f"Decoded block {unknown_idx} ({decoded_count}/{total_blocks})")

        # Check if we decoded all blocks
        if decoded_count < total_blocks:
            logger.warning(f"Only decoded {decoded_count}/{total_blocks} blocks")

            # If we have at least one block, return what we have
            if decoded_count > 0:
                # Combine decoded blocks
                result = b''.join([b for b in decoded_blocks if b is not None])

                # Truncate to original size if needed
                if len(result) > original_size:
                    result = result[:original_size]

                logger.info(f"Returning partial data: {len(result)}/{original_size} bytes")
                return result

            return None

        # Combine decoded blocks
        result = b''.join(decoded_blocks)

        # Truncate to original size if needed
        if len(result) > original_size:
            result = result[:original_size]

        logger.info(f"Successfully decoded {len(result)} bytes")
        return result
