"""
MegaCode decoder integration.
"""

import os
import sys
import logging
import subprocess
import tempfile
import time
import json
from typing import Dict, Any, Callable, Optional

# Configure logger
logger = logging.getLogger(__name__)

# Add megacode packages to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", "megacode-encoder")))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", "megacode-scanner")))

# Try to import MegaCode modules
try:
    from megacode.acquisition.image_loader import ImageLoader
    from megacode.detection.code_detector import CodeDetector
    from megacode.detection.grid_extractor import GridExtractor
    from megacode.classification.color_classifier import ColorClassifier
    from megacode.decoding.decoder import Decoder
    MEGACODE_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import MegaCode modules: {e}")
    MEGACODE_AVAILABLE = False

# Import our custom decoder
from .custom_decoder import CustomDecoder

from ..utils.config import Config


class MegaCodeDecoder:
    """MegaCode decoder integration."""

    def __init__(self, parent=None):
        """
        Initialize the decoder.

        Args:
            parent: Parent object (usually the main window)
        """
        self.config = Config()
        self.parent = parent

        # Check if MegaCode is available
        if not MEGACODE_AVAILABLE:
            logger.warning("MegaCode modules not available, using CLI fallback")

    def decode(
        self,
        image_path: str,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> Dict[str, Any]:
        """
        Decode a MegaCode symbol from an image.

        Args:
            image_path: Path to the image file
            options: Decoding options
            progress_callback: Callback for progress updates

        Returns:
            Dictionary with decoded data and metadata

        Raises:
            RuntimeError: If decoding fails
        """
        # Update progress
        if progress_callback:
            progress_callback(0)

        # Check if force_local is set in options
        force_local = options.get("force_local", False)

        # Check if we should use the API (only if not forcing local)
        if not force_local and self.config.get("use_api", False):
            try:
                return self._decode_api(image_path, options, progress_callback)
            except Exception as e:
                logger.warning(f"API decoding failed, falling back to local: {e}")
                # If API fails, fall back to local processing
                if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                    self.parent.set_api_status(f"API failed, using local processing: {str(e)}", error=True)

        # Use local processing
        # Check if MegaCode is available
        if MEGACODE_AVAILABLE:
            return self._decode_direct(image_path, options, progress_callback)
        else:
            return self._decode_cli(image_path, options, progress_callback)

    def _decode_direct(
        self,
        image_path: str,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> Dict[str, Any]:
        """
        Decode a MegaCode symbol using direct MegaCode module imports.

        Args:
            image_path: Path to the image file
            options: Decoding options
            progress_callback: Callback for progress updates

        Returns:
            Dictionary with decoded data and metadata

        Raises:
            RuntimeError: If decoding fails
        """
        try:
            # Extract options
            palette = options.get("palette", "auto")
            # debug = options.get("debug", False)  # Currently unused

            # Update progress
            if progress_callback:
                progress_callback(10)

            # Initialize components
            image_loader = ImageLoader()
            code_detector = CodeDetector()
            grid_extractor = GridExtractor()
            color_classifier = ColorClassifier()
            # Use our custom decoder instead of the standard one
            decoder = CustomDecoder()

            # Load image
            image = image_loader.load_image(image_path)
            logger.info(f"Loaded image: {image.shape}")

            # Update progress
            if progress_callback:
                progress_callback(30)

            # Detect code
            detection_result = code_detector.detect_code(image)
            if not detection_result["found"]:
                logger.error("No code detected in the image")
                return {
                    "success": False,
                    "error": "No code detected in the image",
                }
            logger.info(f"Code detected with confidence: {detection_result['confidence']:.2f}")

            # Update progress
            if progress_callback:
                progress_callback(50)

            # Extract grid
            extraction_result = grid_extractor.extract_grid(image, detection_result)
            if not extraction_result["success"]:
                logger.error("Failed to extract grid")
                return {
                    "success": False,
                    "error": "Failed to extract grid",
                }
            logger.info(f"Grid extracted: {extraction_result['grid_size'][0]}x{extraction_result['grid_size'][1]} modules")

            # Update progress
            if progress_callback:
                progress_callback(70)

            # Classify colors
            palette_name = None if palette == "auto" else palette
            classification_result = color_classifier.classify_colors(
                extraction_result["grid"],
                extraction_result["calibration"],
                palette_name=palette_name,
            )
            if not classification_result["success"]:
                logger.error("Failed to classify colors")
                return {
                    "success": False,
                    "error": "Failed to classify colors",
                }
            logger.info(f"Colors classified with palette: {classification_result['palette'].name}")

            # Convert symbols to bits
            bits = color_classifier.symbols_to_bits(
                classification_result["symbols"],
                classification_result["palette"],
            )

            # Convert bits to bytes
            raw_data = color_classifier.bits_to_bytes(bits)

            # Update progress
            if progress_callback:
                progress_callback(90)

            # Decode data
            try:
                decoding_result = decoder.decode_data(raw_data)
                if not decoding_result["success"]:
                    error_message = decoding_result.get("error_message", "Failed to decode data")
                    logger.error(f"Failed to decode data: {error_message}")

                    # Try to recover from the error
                    if "list index out of range" in str(error_message):
                        logger.info("Attempting to recover from list index out of range error")
                        # This is likely the fountain packet decoding error
                        # Just return the raw data as text if possible
                        try:
                            text = raw_data.decode('utf-8')
                            if all(c.isprintable() or c.isspace() for c in text):
                                logger.info("Recovered data as text")
                                return {
                                    "success": True,
                                    "data": raw_data,
                                    "metadata": {
                                        "format": "text",
                                        "note": "Recovered from decoding error"
                                    },
                                }
                        except UnicodeDecodeError:
                            # Not valid UTF-8 text, return as binary
                            logger.info("Recovered data as binary")
                            return {
                                "success": True,
                                "data": raw_data,
                                "metadata": {
                                    "format": "binary",
                                    "note": "Recovered from decoding error"
                                },
                            }

                    return {
                        "success": False,
                        "error": error_message,
                    }
            except Exception as e:
                logger.error(f"Exception during decoding: {e}")
                # Try to recover from the exception
                try:
                    text = raw_data.decode('utf-8')
                    if all(c.isprintable() or c.isspace() for c in text):
                        logger.info("Recovered data as text after exception")
                        return {
                            "success": True,
                            "data": raw_data,
                            "metadata": {
                                "format": "text",
                                "note": "Recovered from exception"
                            },
                        }
                except UnicodeDecodeError:
                    # Not valid UTF-8 text, return as binary
                    logger.info("Recovered data as binary after exception")
                    return {
                        "success": True,
                        "data": raw_data,
                        "metadata": {
                            "format": "binary",
                            "note": "Recovered from exception"
                        },
                    }

                return {
                    "success": False,
                    "error": f"Failed to decode data: {e}",
                }

            # If we get here, decoding was successful
            logger.info(f"Data decoded: {len(decoding_result['data'])} bytes")

            # Create metadata
            metadata = {
                "format": "text" if self._is_text(decoding_result["data"]) else "binary",
                "size": len(decoding_result["data"]),
                "errors_corrected": decoding_result["errors_corrected"],
                "palette": classification_result["palette"].name,
                "grid_size": extraction_result["grid_size"],
            }

            # Update progress
            if progress_callback:
                progress_callback(100)

            return {
                "success": True,
                "data": decoding_result["data"],
                "metadata": metadata,
            }

        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    def _is_text(self, data: bytes) -> bool:
        """
        Check if data is text.

        This method also handles text with null bytes.
        """
        # First, try direct decoding
        try:
            data.decode("utf-8")
            return True
        except UnicodeDecodeError:
            # Check if it might be text with null bytes
            if b'\x00' in data:
                # Remove null bytes and try again
                cleaned = data.replace(b'\x00', b'')
                if not cleaned:
                    return False

                try:
                    text = cleaned.decode('utf-8')
                    # Check if the text contains printable characters
                    printable_chars = sum(1 for c in text if c.isprintable() or c.isspace())
                    # If more than 80% of characters are printable, it's likely text
                    return printable_chars / len(text) > 0.8
                except UnicodeDecodeError:
                    return False
            return False

    def _decode_cli(
        self,
        image_path: str,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> Dict[str, Any]:
        """
        Decode a MegaCode symbol using the MegaCode CLI.

        Args:
            image_path: Path to the image file
            options: Decoding options
            progress_callback: Callback for progress updates

        Returns:
            Dictionary with decoded data and metadata

        Raises:
            RuntimeError: If decoding fails
        """
        try:
            # Create temporary output file
            with tempfile.NamedTemporaryFile(delete=False) as output_file:
                output_path = output_file.name

            # Create temporary metadata file
            with tempfile.NamedTemporaryFile(delete=False) as metadata_file:
                metadata_path = metadata_file.name

            # Extract options
            palette = options.get("palette", "auto")
            debug = options.get("debug", False)

            # Build command
            cmd = [
                sys.executable,
                "-m",
                "megacode.cli.decode",
                "--input",
                image_path,
                "--output",
                output_path,
                "--metadata",
                metadata_path,
                "--palette",
                palette,
            ]

            if debug:
                cmd.append("--debug")

            # Update progress
            if progress_callback:
                progress_callback(10)

            # Run command
            logger.info(f"Running command: {' '.join(cmd)}")
            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                )
            except FileNotFoundError:
                logger.error("megacode.cli.decode module not found")
                return {
                    "success": False,
                    "error": "MegaCode scanner module not installed. Please install the megacode-scanner package.",
                }

            # Monitor progress
            if progress_callback:
                # Simulate progress
                for i in range(11, 100, 10):
                    time.sleep(0.5)
                    progress_callback(i)

            # Wait for process to complete
            _, stderr = process.communicate()

            # Check if process succeeded
            if process.returncode != 0:
                logger.error(f"Decoding failed: {stderr}")
                return {
                    "success": False,
                    "error": stderr,
                }

            # Read output file
            try:
                with open(output_path, "rb") as f:
                    data = f.read()

                # Read metadata file
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)

                # Clean up temporary files
                os.unlink(output_path)
                os.unlink(metadata_path)

                # Update progress
                if progress_callback:
                    progress_callback(100)

                return {
                    "success": True,
                    "data": data,
                    "metadata": metadata,
                }
            except Exception as e:
                logger.error(f"Failed to read output files: {e}")
                return {
                    "success": False,
                    "error": f"Failed to read output files: {e}",
                }

        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    def _decode_api(
        self,
        image_path: str,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> Dict[str, Any]:
        """
        Decode a MegaCode symbol using the MegaCode API.

        Args:
            image_path: Path to the image file
            options: Decoding options
            progress_callback: Callback for progress updates

        Returns:
            Dictionary with decoded data and metadata

        Raises:
            RuntimeError: If decoding fails
        """
        try:
            from .api_client import MegaCodeAPIClient
        except ImportError as e:
            logger.error(f"Failed to import API client: {e}")
            return {
                "success": False,
                "error": "API client not available. Please check your installation.",
            }

        try:
            # Update progress
            if progress_callback:
                progress_callback(10)

            # Get API configuration
            api_key = self.config.get("api_key")
            api_url = self.config.get("api_url")

            if not api_key:
                logger.error("API key not configured")
                return {
                    "success": False,
                    "error": "API key not configured. Please set your API key in the settings.",
                }

            # Create API client
            client = MegaCodeAPIClient(api_key=api_key, api_url=api_url)

            # Update progress
            if progress_callback:
                progress_callback(30)

            # Decode using API
            api_options = {
                "palette": options.get("palette", "auto"),
                "debug": options.get("debug", False),
            }

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_active'):
                self.parent.set_api_active(True, 50)
                self.parent.set_api_status("Decoding image via API...", timeout=0)

            # Update progress - API request in progress
            if progress_callback:
                progress_callback(50)

            result = client.decode(image_path, api_options)

            # Update progress
            if progress_callback:
                progress_callback(90)

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_active'):
                self.parent.set_api_active(False)

                # Check rate limit
                if 'rate_limit' in result:
                    remaining = result['rate_limit'].get('remaining', 0)
                    limit = result['rate_limit'].get('limit', 0)
                    if limit > 0:
                        self.parent.set_api_status(f"API rate limit: {remaining}/{limit} remaining", timeout=5000)

            if not result.get("success"):
                error_msg = result.get("error", "Unknown error")
                logger.error(f"API decoding failed: {error_msg}")

                # Update API status in main window if available
                if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                    self.parent.set_api_status(f"API error: {error_msg}", error=True)

                return {
                    "success": False,
                    "error": error_msg,
                }

            # Update progress
            if progress_callback:
                progress_callback(100)

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                self.parent.set_api_status("API decoding completed successfully", timeout=3000)

            # Return result
            return {
                "success": True,
                "data": result.get("data", b""),
                "metadata": result.get("metadata", {}),
            }

        except Exception as e:
            logger.error(f"API decoding failed: {e}")
            return {
                "success": False,
                "error": f"API decoding failed: {e}",
            }
