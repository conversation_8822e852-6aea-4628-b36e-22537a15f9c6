"""
API cache for the MegaCode Desktop application.
"""

import os
import json
import time
import hashlib
import logging
import tempfile
import shutil
from typing import Dict, Any, Optional, Union, List, Tuple

logger = logging.getLogger(__name__)


class APICache:
    """Cache for API responses."""

    def __init__(self, cache_dir: Optional[str] = None, max_age: int = 86400):
        """
        Initialize the API cache.

        Args:
            cache_dir: Directory to store cache files (default: ~/.megacode/cache)
            max_age: Maximum age of cache entries in seconds (default: 24 hours)
        """
        if cache_dir is None:
            # Use default cache directory
            home_dir = os.path.expanduser("~")
            cache_dir = os.path.join(home_dir, ".megacode", "cache")

        self.cache_dir = cache_dir
        self.max_age = max_age

        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)

        # Initialize cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "stores": 0,
        }

    def _get_cache_key(self, method: str, params: Dict[str, Any]) -> str:
        """
        Generate a cache key for the given method and parameters.

        Args:
            method: API method name
            params: API parameters

        Returns:
            Cache key as a string
        """
        # Convert parameters to a stable string representation
        params_str = json.dumps(params, sort_keys=True)

        # Create a hash of the method and parameters
        key = f"{method}:{params_str}"
        return hashlib.md5(key.encode()).hexdigest()

    def _get_cache_path(self, key: str) -> str:
        """
        Get the file path for a cache entry.

        Args:
            key: Cache key

        Returns:
            File path for the cache entry
        """
        return os.path.join(self.cache_dir, f"{key}.json")

    def get(self, method: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Get a cached API response.

        Args:
            method: API method name
            params: API parameters

        Returns:
            Cached API response, or None if not found or expired
        """
        key = self._get_cache_key(method, params)
        cache_path = self._get_cache_path(key)

        try:
            # Check if cache file exists
            if not os.path.exists(cache_path):
                self.stats["misses"] += 1
                return None

            # Read cache file
            with open(cache_path, "r") as f:
                cache_data = json.load(f)

            # Check if cache entry is expired
            if time.time() - cache_data["timestamp"] > self.max_age:
                self.stats["misses"] += 1
                return None

            # Return cached response
            self.stats["hits"] += 1
            return cache_data["response"]

        except Exception as e:
            logger.error(f"Failed to get cache entry: {e}")
            self.stats["misses"] += 1
            return None

    def store(self, method: str, params: Dict[str, Any], response: Dict[str, Any]) -> bool:
        """
        Store an API response in the cache.

        Args:
            method: API method name
            params: API parameters
            response: API response

        Returns:
            True if the response was stored, False otherwise
        """
        key = self._get_cache_key(method, params)
        cache_path = self._get_cache_path(key)

        try:
            # Create cache entry
            cache_data = {
                "timestamp": time.time(),
                "method": method,
                "params": params,
                "response": response,
            }

            # Write cache file atomically
            with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
                json.dump(cache_data, f, indent=2)
                temp_path = f.name

            # Move temporary file to cache file
            shutil.move(temp_path, cache_path)

            self.stats["stores"] += 1
            return True

        except Exception as e:
            logger.error(f"Failed to store cache entry: {e}")
            return False

    def clear(self) -> int:
        """
        Clear all cache entries.

        Returns:
            Number of cache entries cleared
        """
        try:
            # Get all cache files
            cache_files = [
                os.path.join(self.cache_dir, f)
                for f in os.listdir(self.cache_dir)
                if f.endswith(".json")
            ]

            # Delete all cache files
            for cache_file in cache_files:
                os.unlink(cache_file)

            # Reset statistics
            self.stats = {
                "hits": 0,
                "misses": 0,
                "stores": 0,
            }

            return len(cache_files)

        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
            return 0

    def get_stats(self) -> Dict[str, int]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        # Add cache size
        try:
            cache_files = [
                os.path.join(self.cache_dir, f)
                for f in os.listdir(self.cache_dir)
                if f.endswith(".json")
            ]
            cache_size = sum(os.path.getsize(f) for f in cache_files)
            entries = len(cache_files)
        except Exception:
            cache_size = 0
            entries = 0

        return {
            **self.stats,
            "size": cache_size,
            "entries": entries,
        }
