"""
MegaCode encoder integration.
"""

import os
import sys
import logging
import subprocess
import tempfile
import time
from typing import Dict, Any, Callable, Optional, Union, List, Tuple

# Try to import MegaCode modules
try:
    from megacode.compression import compress_data
    from megacode.ecc import apply_reed_solomon, ECCLevel
    from megacode.fountain import generate_fountain_packets
    from megacode.symbol import map_bits_to_colors
    from megacode.layout import generate_grid
    from megacode.renderer import render_svg, render_png, render_stl
    MEGACODE_AVAILABLE = True
except ImportError:
    MEGACODE_AVAILABLE = False

from ..utils.config import Config

logger = logging.getLogger(__name__)


class MegaCodeEncoder:
    """MegaCode encoder integration."""

    def __init__(self, parent=None):
        """
        Initialize the encoder.

        Args:
            parent: Parent object (usually the main window)
        """
        self.config = Config()
        self.parent = parent

        # Check if MegaCode is available
        if not MEGACODE_AVAILABLE:
            logger.warning("MegaCode modules not available, using CLI fallback")

    def encode(
        self,
        data: bytes,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> bytes:
        """
        Encode data into a MegaCode symbol.

        Args:
            data: Data to encode
            options: Encoding options
            progress_callback: Callback for progress updates

        Returns:
            Encoded data as bytes

        Raises:
            RuntimeError: If encoding fails
        """
        # Update progress
        if progress_callback:
            progress_callback(0)

        # Check if force_local is set in options
        force_local = options.get("force_local", False)

        # Check if we should use the API (only if not forcing local)
        if not force_local and self.config.get("use_api", False):
            try:
                return self._encode_api(data, options, progress_callback)
            except Exception as e:
                logger.warning(f"API encoding failed, falling back to local: {e}")
                # If API fails, fall back to local processing
                if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                    self.parent.set_api_status(f"API failed, using local processing: {str(e)}", error=True)

        # Use local processing
        # Check if MegaCode is available
        if MEGACODE_AVAILABLE:
            return self._encode_direct(data, options, progress_callback)
        else:
            return self._encode_cli(data, options, progress_callback)

    def _encode_direct(
        self,
        data: bytes,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> bytes:
        """
        Encode data using direct MegaCode module imports.

        Args:
            data: Data to encode
            options: Encoding options
            progress_callback: Callback for progress updates

        Returns:
            Encoded data as bytes

        Raises:
            RuntimeError: If encoding fails
        """
        try:
            # Extract options
            format = options.get("format", "svg")
            compression = options.get("compression", "brotli")
            compression_level = options.get("compression_level", 9)
            ecc_level_str = options.get("ecc_level", "M")
            fountain_overhead = options.get("fountain_overhead", 0.1)
            palette = options.get("palette", "8-color")
            module_size = options.get("module_size", 10)
            finder_patterns = options.get("finder_patterns", True)
            calibration_patterns = options.get("calibration_patterns", True)

            # Map ECC level string to enum
            ecc_level = {
                "L": ECCLevel.L,
                "M": ECCLevel.M,
                "Q": ECCLevel.Q,
                "H": ECCLevel.H,
            }.get(ecc_level_str, ECCLevel.M)

            # Update progress
            if progress_callback:
                progress_callback(10)

            # Compress data
            compressed_data = compress_data(
                data,
                method=compression,
                level=compression_level,
            )
            logger.info(f"Compressed data: {len(compressed_data)} bytes")

            # Update progress
            if progress_callback:
                progress_callback(30)

            # Apply Reed-Solomon error correction
            ecc_data = apply_reed_solomon(compressed_data, level=ecc_level)
            logger.info(f"Applied Reed-Solomon ECC: {len(ecc_data)} bytes")

            # Update progress
            if progress_callback:
                progress_callback(50)

            # Convert RS blocks to bytes
            rs_data = [block.to_bytes() for block in ecc_data]

            # Generate fountain packets
            packets = generate_fountain_packets(rs_data, overhead=fountain_overhead)
            logger.info(f"Generated {len(packets)} fountain packets")

            # Update progress
            if progress_callback:
                progress_callback(70)

            # Convert packets to bytes
            packet_bytes = [packet.to_bytes() for packet in packets]

            # Combine all packet bytes into a single bytes object
            combined_bytes = b''.join(packet_bytes)

            # Map bits to colors
            colors = map_bits_to_colors(combined_bytes, palette=palette)
            logger.info(f"Mapped bits to {len(colors)} color symbols")

            # Update progress
            if progress_callback:
                progress_callback(80)

            # Generate grid layout
            layout = generate_grid(
                colors,
                finder_patterns=finder_patterns,
                calibration_patterns=calibration_patterns,
            )
            logger.info(f"Generated grid layout of size {layout.width}x{layout.height}")

            # Update progress
            if progress_callback:
                progress_callback(90)

            # Render output
            if format == "svg":
                output = render_svg(layout, module_size=module_size)
            elif format == "png":
                output = render_png(layout, module_size=module_size)
            elif format == "stl":
                output = render_stl(
                    layout,
                    module_size=module_size,
                    base_height=1.0,
                    module_height=1.0,
                )
            else:
                raise ValueError(f"Unsupported format: {format}")

            logger.info(f"Rendered output: {len(output)} bytes")

            # Update progress
            if progress_callback:
                progress_callback(100)

            return output

        except Exception as e:
            logger.error(f"Encoding failed: {e}")
            raise RuntimeError(f"Encoding failed: {e}")

    def _encode_cli(
        self,
        data: bytes,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> bytes:
        """
        Encode data using the MegaCode CLI.

        Args:
            data: Data to encode
            options: Encoding options
            progress_callback: Callback for progress updates

        Returns:
            Encoded data as bytes

        Raises:
            RuntimeError: If encoding fails
        """
        try:
            # Create temporary files
            with tempfile.NamedTemporaryFile(delete=False) as input_file:
                input_file.write(data)
                input_path = input_file.name

            # Create temporary output file
            with tempfile.NamedTemporaryFile(delete=False) as output_file:
                output_path = output_file.name

            # Extract options
            format = options.get("format", "svg")
            compression = options.get("compression", "brotli")
            compression_level = options.get("compression_level", 9)
            ecc_level = options.get("ecc_level", "M")
            fountain_overhead = options.get("fountain_overhead", 0.1)
            palette = options.get("palette", "8-color")
            module_size = options.get("module_size", 10)
            finder_patterns = options.get("finder_patterns", True)
            calibration_patterns = options.get("calibration_patterns", True)

            # Build command
            cmd = [
                sys.executable,
                "-m",
                "megacode.cli.encode",
                "--input",
                input_path,
                "--output",
                output_path,
                "--format",
                format,
                "--compression",
                compression,
                "--compression-level",
                str(compression_level),
                "--ecc-level",
                ecc_level,
                "--fountain-overhead",
                str(fountain_overhead),
                "--palette",
                palette,
                "--module-size",
                str(module_size),
            ]

            if finder_patterns:
                cmd.append("--finder-patterns")

            if calibration_patterns:
                cmd.append("--calibration-patterns")

            # Update progress
            if progress_callback:
                progress_callback(10)

            # Run command
            logger.info(f"Running command: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )

            # Monitor progress
            if progress_callback:
                # Simulate progress
                for i in range(11, 100, 10):
                    time.sleep(0.5)
                    progress_callback(i)

            # Wait for process to complete
            stdout, stderr = process.communicate()

            # Check if process succeeded
            if process.returncode != 0:
                logger.error(f"Encoding failed: {stderr}")
                raise RuntimeError(f"Encoding failed: {stderr}")

            # Read output file
            with open(output_path, "rb") as f:
                output = f.read()

            # Clean up temporary files
            os.unlink(input_path)
            os.unlink(output_path)

            # Update progress
            if progress_callback:
                progress_callback(100)

            return output

        except Exception as e:
            logger.error(f"Encoding failed: {e}")
            raise RuntimeError(f"Encoding failed: {e}")

    def _encode_api(
        self,
        data: bytes,
        options: Dict[str, Any],
        progress_callback: Optional[Callable[[int], None]] = None,
    ) -> bytes:
        """
        Encode data using the MegaCode API.

        Args:
            data: Data to encode
            options: Encoding options
            progress_callback: Callback for progress updates

        Returns:
            Encoded data as bytes

        Raises:
            RuntimeError: If encoding fails
        """
        try:
            from .api_client import MegaCodeAPIClient
        except ImportError as e:
            logger.error(f"Failed to import API client: {e}")
            raise RuntimeError("API client not available. Please check your installation.")

        try:
            # Update progress
            if progress_callback:
                progress_callback(10)

            # Get API configuration
            api_key = self.config.get("api_key")
            api_url = self.config.get("api_url")

            if not api_key:
                logger.error("API key not configured")
                raise RuntimeError("API key not configured. Please set your API key in the settings.")

            # Create API client
            client = MegaCodeAPIClient(api_key=api_key, api_url=api_url)

            # Update progress
            if progress_callback:
                progress_callback(30)

            # Extract options
            format_type = options.get("format", "svg")
            compression = options.get("compression", "brotli")
            compression_level = options.get("compression_level", 9)
            ecc_level = options.get("ecc_level", "M")
            fountain_overhead = options.get("fountain_overhead", 0.1)
            palette = options.get("palette", "8-color")
            module_size = options.get("module_size", 10)
            finder_patterns = options.get("finder_patterns", True)
            calibration_patterns = options.get("calibration_patterns", True)

            # Prepare API options
            api_options = {
                "format": format_type,
                "compression": compression,
                "compression_level": compression_level,
                "ecc_level": ecc_level,
                "fountain_overhead": fountain_overhead,
                "palette": palette,
                "module_size": module_size,
                "finder_patterns": finder_patterns,
                "calibration_patterns": calibration_patterns,
            }

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_active'):
                self.parent.set_api_active(True, 50)
                self.parent.set_api_status("Encoding data via API...", timeout=0)

            # Update progress - API request in progress
            if progress_callback:
                progress_callback(50)

            # Encode using API
            result = client.encode(data, api_options)

            # Update progress
            if progress_callback:
                progress_callback(90)

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_active'):
                self.parent.set_api_active(False)

                # Check rate limit
                if 'rate_limit' in result:
                    remaining = result['rate_limit'].get('remaining', 0)
                    limit = result['rate_limit'].get('limit', 0)
                    if limit > 0:
                        self.parent.set_api_status(f"API rate limit: {remaining}/{limit} remaining", timeout=5000)

            if not result.get("success"):
                error_msg = result.get("error", "Unknown error")
                logger.error(f"API encoding failed: {error_msg}")

                # Update API status in main window if available
                if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                    self.parent.set_api_status(f"API error: {error_msg}", error=True)

                raise RuntimeError(f"API encoding failed: {error_msg}")

            # Get encoded data
            encoded_data = result.get("files", {}).get(format_type)
            if not encoded_data:
                logger.error("API did not return encoded data")

                # Update API status in main window if available
                if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                    self.parent.set_api_status("API error: No encoded data returned", error=True)

                raise RuntimeError("API did not return encoded data")

            # Update progress
            if progress_callback:
                progress_callback(100)

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                self.parent.set_api_status("API encoding completed successfully", timeout=3000)

            return encoded_data

        except Exception as e:
            logger.error(f"API encoding failed: {e}")

            # Update API status in main window if available
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_active'):
                self.parent.set_api_active(False)
            if hasattr(self, 'parent') and hasattr(self.parent, 'set_api_status'):
                self.parent.set_api_status(f"API error: {str(e)}", error=True)

            raise RuntimeError(f"API encoding failed: {e}")
