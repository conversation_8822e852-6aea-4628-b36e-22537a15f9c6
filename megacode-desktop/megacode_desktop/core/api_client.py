"""
MegaCode API client.
"""

import os
import json
import logging
import requests
import time
import hashlib
from typing import Dict, Any, Optional, Union, List, Tuple, BinaryIO

from .api_cache import APICache

logger = logging.getLogger(__name__)


class MegaCodeAPIClient:
    """Client for the MegaCode API."""

    def __init__(self, api_key: Optional[str] = None, api_url: Optional[str] = None, use_cache: bool = True):
        """
        Initialize the API client.

        Args:
            api_key: API key for authentication
            api_url: Base URL for the API
            use_cache: Whether to use the API cache
        """
        self.api_key = api_key or os.environ.get("MEGACODE_API_KEY")
        self.api_url = api_url or os.environ.get("MEGACODE_API_URL", "https://api.megacode.example.com/v1")
        self.use_cache = use_cache

        # Initialize cache
        if self.use_cache:
            self.cache = APICache()

        # Initialize rate limit tracking
        self.rate_limit = {
            "limit": 0,
            "remaining": 0,
            "reset": 0,
        }

    def encode(
        self,
        data: Union[str, bytes],
        options: Dict[str, Any],
        bypass_cache: bool = False,
    ) -> Dict[str, Any]:
        """
        Encode data using the MegaCode API.

        Args:
            data: Data to encode
            options: Encoding options
            bypass_cache: Whether to bypass the cache

        Returns:
            Dictionary with encoding results
        """
        try:
            # Create cache parameters
            cache_params = {
                "options": options,
                "data_hash": hashlib.md5(data if isinstance(data, bytes) else data.encode()).hexdigest(),
            }

            # Check cache if enabled and not bypassed
            if self.use_cache and not bypass_cache:
                cached_result = self.cache.get("encode", cache_params)
                if cached_result:
                    logger.info("Using cached API response for encoding")
                    return cached_result

            # Prepare request data
            files = {}
            payload = {
                "options": json.dumps(options),
            }

            # Handle data
            if isinstance(data, str):
                payload["data"] = data
            else:
                files["data"] = ("data.bin", data)

            # Make API request
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"

            response = requests.post(
                f"{self.api_url}/encode",
                data=payload,
                files=files,
                headers=headers,
            )

            # Update rate limit information
            self._update_rate_limits(response)

            # Check response
            response.raise_for_status()
            result = response.json()

            # Download encoded files if available
            if "file_urls" in result:
                result["files"] = {}
                for file_type, url in result["file_urls"].items():
                    file_response = requests.get(url)
                    file_response.raise_for_status()
                    result["files"][file_type] = file_response.content

            # Cache the result if successful
            if self.use_cache and result.get("success", False):
                self.cache.store("encode", cache_params, result)
                logger.info("Cached API response for encoding")

            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                "success": False,
                "error": f"API request failed: {e}",
                "rate_limit": self.rate_limit,
            }
        except Exception as e:
            logger.error(f"Encoding failed: {e}")
            return {
                "success": False,
                "error": f"Encoding failed: {e}",
            }

    def _update_rate_limits(self, response):
        """
        Update rate limit information from response headers.

        Args:
            response: Response object from requests
        """
        try:
            # Extract rate limit headers
            if "X-RateLimit-Limit" in response.headers:
                self.rate_limit["limit"] = int(response.headers["X-RateLimit-Limit"])
            if "X-RateLimit-Remaining" in response.headers:
                self.rate_limit["remaining"] = int(response.headers["X-RateLimit-Remaining"])
            if "X-RateLimit-Reset" in response.headers:
                self.rate_limit["reset"] = int(response.headers["X-RateLimit-Reset"])
        except Exception as e:
            logger.warning(f"Failed to parse rate limit headers: {e}")

    def get_rate_limit(self) -> Dict[str, int]:
        """
        Get current rate limit information.

        Returns:
            Dictionary with rate limit information
        """
        return self.rate_limit

    def decode(
        self,
        image_path: str,
        options: Dict[str, Any],
        bypass_cache: bool = False,
    ) -> Dict[str, Any]:
        """
        Decode a MegaCode symbol using the MegaCode API.

        Args:
            image_path: Path to the image file
            options: Decoding options
            bypass_cache: Whether to bypass the cache

        Returns:
            Dictionary with decoded data and metadata
        """
        try:
            # Create cache parameters
            # Use file hash and modification time to identify the image
            file_stat = os.stat(image_path)
            with open(image_path, "rb") as f:
                file_hash = hashlib.md5(f.read(8192)).hexdigest()  # Read first 8KB for hash

            cache_params = {
                "options": options,
                "file_hash": file_hash,
                "file_mtime": file_stat.st_mtime,
                "file_size": file_stat.st_size,
            }

            # Check cache if enabled and not bypassed
            if self.use_cache and not bypass_cache:
                cached_result = self.cache.get("decode", cache_params)
                if cached_result:
                    logger.info("Using cached API response for decoding")
                    return cached_result

            # Prepare request data
            with open(image_path, "rb") as f:
                files = {
                    "image": (os.path.basename(image_path), f),
                }

            payload = {
                "options": json.dumps(options),
            }

            # Make API request
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"

            response = requests.post(
                f"{self.api_url}/decode",
                data=payload,
                files=files,
                headers=headers,
            )

            # Update rate limit information
            self._update_rate_limits(response)

            # Check response
            response.raise_for_status()
            result = response.json()

            # Handle binary data
            if result.get("success") and result.get("data_format") == "binary":
                # Decode base64 data
                import base64
                result["data"] = base64.b64decode(result["data_base64"])

            # Cache the result if successful
            if self.use_cache and result.get("success", False):
                self.cache.store("decode", cache_params, result)
                logger.info("Cached API response for decoding")

            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                "success": False,
                "error": f"API request failed: {e}",
                "rate_limit": self.rate_limit,
            }
        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            return {
                "success": False,
                "error": f"Decoding failed: {e}",
            }
