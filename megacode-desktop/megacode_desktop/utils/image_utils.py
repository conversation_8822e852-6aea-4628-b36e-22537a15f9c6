"""
Image utility functions for the MegaCode Desktop application.
"""

import os
import io
import logging
import numpy as np
import tempfile
from typing import Op<PERSON>, <PERSON><PERSON>, Union
from PIL import Image
from PyQt6.QtGui import QImage, QPixmap
from PyQt6.QtCore import Qt

# Set up logger
logger = logging.getLogger(__name__)

# Try to import CairoSVG for SVG conversion
try:
    import cairosvg
    CAIROSVG_AVAILABLE = True
except ImportError:
    CAIROSVG_AVAILABLE = False
    logger.warning("CairoSVG not available, SVG conversion will be limited")


def load_image(image_path: str) -> np.ndarray:
    """
    Load an image from a file.

    Args:
        image_path: Path to the image file

    Returns:
        NumPy array containing the image data

    Raises:
        ValueError: If the image cannot be loaded
    """
    try:
        # Open the image with PIL
        pil_image = Image.open(image_path)

        # Convert to RGB if needed
        if pil_image.mode != "RGB":
            pil_image = pil_image.convert("RGB")

        # Convert to NumPy array
        image = np.array(pil_image)

        return image
    except Exception as e:
        logger.error(f"Failed to load image: {e}")
        raise ValueError(f"Failed to load image: {e}")


def resize_image(
    image: Union[np.ndarray, QImage, QPixmap],
    width: Optional[int] = None,
    height: Optional[int] = None,
    keep_aspect_ratio: bool = True,
) -> Union[np.ndarray, QImage, QPixmap]:
    """
    Resize an image.

    Args:
        image: Image to resize (NumPy array, QImage, or QPixmap)
        width: Target width (if None, calculated from height)
        height: Target height (if None, calculated from width)
        keep_aspect_ratio: Whether to maintain the aspect ratio

    Returns:
        Resized image (same type as input)

    Raises:
        ValueError: If both width and height are None
        TypeError: If the image type is not supported
    """
    if width is None and height is None:
        raise ValueError("At least one of width or height must be specified")

    if isinstance(image, np.ndarray):
        return _resize_numpy_image(image, width, height, keep_aspect_ratio)
    elif isinstance(image, QImage):
        return _resize_qimage(image, width, height, keep_aspect_ratio)
    elif isinstance(image, QPixmap):
        return _resize_qpixmap(image, width, height, keep_aspect_ratio)
    else:
        raise TypeError(f"Unsupported image type: {type(image)}")


def _resize_numpy_image(
    image: np.ndarray,
    width: Optional[int] = None,
    height: Optional[int] = None,
    keep_aspect_ratio: bool = True,
) -> np.ndarray:
    """
    Resize a NumPy image.

    Args:
        image: NumPy array containing the image data
        width: Target width (if None, calculated from height)
        height: Target height (if None, calculated from width)
        keep_aspect_ratio: Whether to maintain the aspect ratio

    Returns:
        Resized NumPy array
    """
    try:
        # Get original dimensions
        orig_height, orig_width = image.shape[:2]

        # Calculate target dimensions
        if width is None:
            # Calculate width from height
            if keep_aspect_ratio:
                width = int(orig_width * (height / orig_height))
            else:
                width = orig_width
        elif height is None:
            # Calculate height from width
            if keep_aspect_ratio:
                height = int(orig_height * (width / orig_width))
            else:
                height = orig_height
        elif keep_aspect_ratio:
            # Use the smaller scaling factor
            scale_width = width / orig_width
            scale_height = height / orig_height

            if scale_width < scale_height:
                height = int(orig_height * scale_width)
            else:
                width = int(orig_width * scale_height)

        # Convert to PIL image for resizing
        pil_image = Image.fromarray(image)
        resized_pil = pil_image.resize((width, height), Image.LANCZOS)

        # Convert back to NumPy array
        resized = np.array(resized_pil)

        return resized
    except Exception as e:
        logger.error(f"Failed to resize NumPy image: {e}")
        return image


def _resize_qimage(
    image: QImage,
    width: Optional[int] = None,
    height: Optional[int] = None,
    keep_aspect_ratio: bool = True,
) -> QImage:
    """
    Resize a QImage.

    Args:
        image: QImage to resize
        width: Target width (if None, calculated from height)
        height: Target height (if None, calculated from width)
        keep_aspect_ratio: Whether to maintain the aspect ratio

    Returns:
        Resized QImage
    """
    try:
        # Get original dimensions
        orig_width = image.width()
        orig_height = image.height()

        # Calculate target dimensions
        if width is None:
            # Calculate width from height
            if keep_aspect_ratio:
                width = int(orig_width * (height / orig_height))
            else:
                width = orig_width
        elif height is None:
            # Calculate height from width
            if keep_aspect_ratio:
                height = int(orig_height * (width / orig_width))
            else:
                height = orig_height

        # Resize the image
        if keep_aspect_ratio:
            return image.scaled(
                width,
                height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
        else:
            return image.scaled(
                width,
                height,
                Qt.AspectRatioMode.IgnoreAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
    except Exception as e:
        logger.error(f"Failed to resize QImage: {e}")
        return image


def _resize_qpixmap(
    image: QPixmap,
    width: Optional[int] = None,
    height: Optional[int] = None,
    keep_aspect_ratio: bool = True,
) -> QPixmap:
    """
    Resize a QPixmap.

    Args:
        image: QPixmap to resize
        width: Target width (if None, calculated from height)
        height: Target height (if None, calculated from width)
        keep_aspect_ratio: Whether to maintain the aspect ratio

    Returns:
        Resized QPixmap
    """
    try:
        # Get original dimensions
        orig_width = image.width()
        orig_height = image.height()

        # Calculate target dimensions
        if width is None:
            # Calculate width from height
            if keep_aspect_ratio:
                width = int(orig_width * (height / orig_height))
            else:
                width = orig_width
        elif height is None:
            # Calculate height from width
            if keep_aspect_ratio:
                height = int(orig_height * (width / orig_width))
            else:
                height = orig_height

        # Resize the image
        if keep_aspect_ratio:
            return image.scaled(
                width,
                height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
        else:
            return image.scaled(
                width,
                height,
                Qt.AspectRatioMode.IgnoreAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
    except Exception as e:
        logger.error(f"Failed to resize QPixmap: {e}")
        return image


def svg_to_png(
    svg_content: str,
    width: Optional[int] = None,
    height: Optional[int] = None,
) -> bytes:
    """
    Convert SVG content to PNG image data.

    Args:
        svg_content: SVG content as string
        width: Target width (if None, uses original width)
        height: Target height (if None, uses original height)

    Returns:
        PNG image data as bytes

    Raises:
        ValueError: If conversion fails
    """
    if not svg_content:
        raise ValueError("Empty SVG content")

    # Try to use CairoSVG if available
    if CAIROSVG_AVAILABLE:
        try:
            # Convert SVG to PNG using CairoSVG
            png_data = cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                output_width=width,
                output_height=height,
            )
            logger.info("Converted SVG to PNG using CairoSVG")
            return png_data
        except Exception as e:
            logger.error(f"Failed to convert SVG to PNG using CairoSVG: {e}")

    # Fallback to using temporary files and PIL
    try:
        # Create temporary files
        with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as svg_file:
            svg_path = svg_file.name
            svg_file.write(svg_content.encode('utf-8'))

        # Use PIL to convert SVG to PNG
        # Note: PIL doesn't support SVG directly, so this is just a placeholder
        # that will likely fail, but we include it for completeness
        try:
            img = Image.open(svg_path)
            if width and height:
                img = img.resize((width, height), Image.LANCZOS)

            # Save as PNG
            with io.BytesIO() as output:
                img.save(output, format='PNG')
                png_data = output.getvalue()

            logger.info("Converted SVG to PNG using PIL")
            return png_data
        except Exception as e:
            logger.error(f"Failed to convert SVG to PNG using PIL: {e}")
            raise ValueError(f"Failed to convert SVG to PNG: {e}")
        finally:
            # Clean up temporary file
            if os.path.exists(svg_path):
                os.unlink(svg_path)
    except Exception as e:
        logger.error(f"Failed to convert SVG to PNG: {e}")
        raise ValueError(f"Failed to convert SVG to PNG: {e}")


def svg_to_pixmap(
    svg_content: str,
    width: Optional[int] = None,
    height: Optional[int] = None,
) -> QPixmap:
    """
    Convert SVG content to QPixmap.

    Args:
        svg_content: SVG content as string
        width: Target width (if None, uses original width)
        height: Target height (if None, uses original height)

    Returns:
        QPixmap containing the rendered SVG

    Raises:
        ValueError: If conversion fails
    """
    try:
        # Convert SVG to PNG
        png_data = svg_to_png(svg_content, width, height)

        # Create QPixmap from PNG data
        pixmap = QPixmap()
        pixmap.loadFromData(png_data)

        if pixmap.isNull():
            raise ValueError("Failed to create QPixmap from PNG data")

        return pixmap
    except Exception as e:
        logger.error(f"Failed to convert SVG to QPixmap: {e}")

        # Create a fallback pixmap with text
        pixmap = QPixmap(width or 300, height or 200)
        pixmap.fill(Qt.GlobalColor.white)

        # Return the fallback pixmap
        return pixmap
