"""
Configuration management for the MegaCode Desktop application.
"""

import os
import json
import logging
from typing import Any, Dict, Optional, Union
from PyQt6.QtCore import QSettings

logger = logging.getLogger(__name__)


class Config:
    """Configuration manager for the MegaCode Desktop application."""

    def __init__(self):
        """Initialize the configuration manager."""
        self.settings = QSettings("MegaCode", "MegaCode Desktop")
        self._cache = {}
        self._load()

    def _load(self):
        """Load settings from QSettings."""
        # Load all keys
        for key in self.settings.allKeys():
            self._cache[key] = self.settings.value(key)

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        value = self._cache.get(key, default)

        # Handle type conversion for boolean values
        if isinstance(default, bool) and isinstance(value, str):
            return value.lower() == "true"

        return value

    def set(self, key: str, value: Any):
        """
        Set a configuration value.

        Args:
            key: Configuration key
            value: Configuration value
        """
        self._cache[key] = value
        self.settings.setValue(key, value)

    def save(self):
        """Save all configuration values."""
        for key, value in self._cache.items():
            self.settings.setValue(key, value)
        self.settings.sync()

    def clear(self):
        """Clear all configuration values."""
        self._cache = {}
        self.settings.clear()

    def export(self, file_path: str):
        """
        Export configuration to a JSON file.

        Args:
            file_path: Path to the JSON file

        Raises:
            IOError: If the file cannot be written
        """
        try:
            with open(file_path, "w") as f:
                json.dump(self._cache, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to export configuration: {e}")
            raise IOError(f"Failed to export configuration: {e}")

    def import_(self, file_path: str):
        """
        Import configuration from a JSON file.

        Args:
            file_path: Path to the JSON file

        Raises:
            IOError: If the file cannot be read
            ValueError: If the file is not valid JSON
        """
        try:
            with open(file_path, "r") as f:
                config = json.load(f)

            # Update cache and settings
            self._cache.update(config)
            for key, value in config.items():
                self.settings.setValue(key, value)

            self.settings.sync()
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse configuration file: {e}")
            raise ValueError(f"Failed to parse configuration file: {e}")
        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")
            raise IOError(f"Failed to import configuration: {e}")
