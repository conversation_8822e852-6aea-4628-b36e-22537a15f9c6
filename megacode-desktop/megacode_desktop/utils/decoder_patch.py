"""
Patch for the MegaCode decoder to handle edge cases.

This module provides functions to patch the MegaCode decoder to handle edge cases
that might cause errors in the original implementation.
"""

import logging
import struct
from typing import Dict, Any, List, Optional, Tuple, Union

# Configure logger
logger = logging.getLogger(__name__)


def safe_decode_fountain_packets(decoder, packets: List[Dict[str, Any]]) -> Optional[bytes]:
    """
    Safely decode fountain packets, handling edge cases that might cause errors.

    This function wraps the original _decode_fountain_packets method and adds
    additional error handling to prevent common errors.

    Args:
        decoder: The decoder instance
        packets: List of fountain packets

    Returns:
        Decoded data or None if decoding fails
    """
    if not packets:
        logger.warning("No packets to decode")
        return None

    try:
        # Check if we have raw data packets
        if len(packets) == 1:
            # Check if it's a raw data packet
            if "format" in packets[0] and packets[0]["format"] in ["raw_text", "raw_binary"]:
                logger.info(f"Using raw data packet ({len(packets[0]['data'])} bytes)")
                return packets[0]["data"]

            # Check if it's a simple packet without proper format
            if "data" in packets[0]:
                logger.info(f"Using simple data packet ({len(packets[0]['data'])} bytes)")
                return packets[0]["data"]

        # Make sure required fields are present
        for required_field in ["total_blocks", "original_size", "data"]:
            if required_field not in packets[0]:
                logger.warning(f"Packet missing required field: {required_field}")
                # If we have data, return it as a fallback
                if "data" in packets[0]:
                    logger.info(f"Using packet data as fallback ({len(packets[0]['data'])} bytes)")
                    return packets[0]["data"]
                return None

        # Call the original method
        return decoder._decode_fountain_packets(packets)

    except Exception as e:
        logger.error(f"Error in fountain packet decoding: {e}")
        # Try to recover by returning the raw data from the first packet
        if packets and "data" in packets[0]:
            logger.info(f"Using packet data as fallback after error ({len(packets[0]['data'])} bytes)")
            return packets[0]["data"]
        return None


def patch_decoder(decoder_class):
    """
    Patch the decoder class to use the safe decoding methods.

    Args:
        decoder_class: The decoder class to patch

    Returns:
        The patched decoder class
    """
    # Store the original method - use decode for desktop app, decode_data for scanner
    if hasattr(decoder_class, 'decode_data'):
        original_method = decoder_class.decode_data
        method_name = 'decode_data'
    else:
        original_method = decoder_class.decode
        method_name = 'decode'

    # Define the patched method
    def patched_method(self, *args, **kwargs):
        """
        Patched decode method that uses safe_decode_fountain_packets.
        """
        # Call the original method
        result = original_method(self, *args, **kwargs)

        # If the result is successful, return it
        if result.get('success', False):
            return result

        # If the result is not successful, try to recover
        try:
            # For desktop app, the first argument is the image path
            # For scanner, the first argument is the raw data
            if method_name == 'decode':
                # Desktop app - we can't easily recover here
                return result
            else:
                # Scanner app - we can try to recover
                raw_data = args[0]

                # Try to parse fountain packets
                packets = self._parse_fountain_packets(raw_data)

                if not packets:
                    return result

                # Use the safe decoding method
                fountain_data = safe_decode_fountain_packets(self, packets)

                if fountain_data is None:
                    return result

                # Try to determine if it's text or binary
                try:
                    text = fountain_data.decode('utf-8')
                    if all(c.isprintable() or c.isspace() for c in text):
                        return {
                            "success": True,
                            "data": fountain_data,
                            "metadata": {"format": "text", "encoding": "utf-8"},
                            "errors_corrected": 0,
                            "error_message": None
                        }
                except UnicodeDecodeError:
                    # Not valid UTF-8 text, return as binary
                    return {
                        "success": True,
                        "data": fountain_data,
                        "metadata": {"format": "binary"},
                        "errors_corrected": 0,
                        "error_message": None
                    }
        except Exception as e:
            logger.error(f"Recovery attempt failed: {e}")

        # If recovery failed, return the original result
        return result

    # Replace the original method with the patched one
    if method_name == 'decode_data':
        decoder_class.decode_data = patched_method
    else:
        decoder_class.decode = patched_method

    return decoder_class
