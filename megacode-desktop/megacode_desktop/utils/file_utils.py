"""
File utility functions for the MegaCode Desktop application.
"""

import os
import mimetypes
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


def get_file_size_str(file_path: str) -> str:
    """
    Get a human-readable file size string.
    
    Args:
        file_path: Path to the file
    
    Returns:
        Human-readable file size string
    """
    try:
        size = os.path.getsize(file_path)
        
        # Convert to human-readable format
        for unit in ["B", "KB", "MB", "GB", "TB"]:
            if size < 1024.0:
                return f"{size:.2f} {unit}"
            size /= 1024.0
        
        return f"{size:.2f} PB"
    except Exception as e:
        logger.error(f"Failed to get file size: {e}")
        return "Unknown size"


def get_file_type(file_path: str) -> str:
    """
    Get the MIME type of a file.
    
    Args:
        file_path: Path to the file
    
    Returns:
        MIME type string
    """
    try:
        # Initialize mimetypes
        if not mimetypes.inited:
            mimetypes.init()
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if mime_type:
            return mime_type
        else:
            # Try to determine type based on extension
            ext = os.path.splitext(file_path)[1].lower()
            
            if ext in [".svg"]:
                return "image/svg+xml"
            elif ext in [".png"]:
                return "image/png"
            elif ext in [".jpg", ".jpeg"]:
                return "image/jpeg"
            elif ext in [".gif"]:
                return "image/gif"
            elif ext in [".bmp"]:
                return "image/bmp"
            elif ext in [".txt"]:
                return "text/plain"
            elif ext in [".html", ".htm"]:
                return "text/html"
            elif ext in [".json"]:
                return "application/json"
            elif ext in [".xml"]:
                return "application/xml"
            elif ext in [".pdf"]:
                return "application/pdf"
            elif ext in [".zip"]:
                return "application/zip"
            elif ext in [".stl"]:
                return "model/stl"
            else:
                return "application/octet-stream"
    except Exception as e:
        logger.error(f"Failed to get file type: {e}")
        return "application/octet-stream"


def is_text_file(file_path: str) -> bool:
    """
    Check if a file is a text file.
    
    Args:
        file_path: Path to the file
    
    Returns:
        True if the file is a text file, False otherwise
    """
    mime_type = get_file_type(file_path)
    return mime_type.startswith("text/") or mime_type in [
        "application/json",
        "application/xml",
    ]


def is_image_file(file_path: str) -> bool:
    """
    Check if a file is an image file.
    
    Args:
        file_path: Path to the file
    
    Returns:
        True if the file is an image file, False otherwise
    """
    mime_type = get_file_type(file_path)
    return mime_type.startswith("image/")
