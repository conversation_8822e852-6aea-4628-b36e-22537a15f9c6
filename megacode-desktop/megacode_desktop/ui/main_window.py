"""
Main window for the MegaCode Desktop application.
"""

import os
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QMenu, QToolBar,
    QStatusBar, QMessageBox, QFileDialog
)
from PyQt6.QtGui import QIcon, QKeySequence, QAction
from PyQt6.QtCore import Qt, QSize

from .encode_tab import EncodeTab
from .decode_tab import DecodeTab
from .settings_dialog import SettingsDialog
from .about_dialog import AboutDialog
from .api_status import APIStatusWidget
from ..utils.config import Config

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """Main window for the MegaCode Desktop application."""

    def __init__(self):
        """Initialize the main window."""
        super().__init__()

        # Load configuration
        self.config = Config()

        # Set window properties
        self.setWindowTitle("MegaCode Desktop")
        self.setMinimumSize(800, 600)

        # Create the central widget
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)

        # Create tabs
        self.encode_tab = EncodeTab(self)
        self.decode_tab = DecodeTab(self)

        # Add tabs
        self.tabs.addTab(self.encode_tab, "Encode")
        self.tabs.addTab(self.decode_tab, "Decode")

        # Create menus and toolbar
        self._create_actions()
        self._create_menus()
        self._create_toolbar()
        self._create_statusbar()

        # Restore window geometry
        self._restore_geometry()

    def _create_actions(self):
        """Create actions for menus and toolbar."""
        # File menu actions
        self.open_action = QAction("&Open...", self)
        self.open_action.setShortcut(QKeySequence.StandardKey.Open)
        self.open_action.setStatusTip("Open a file")
        self.open_action.triggered.connect(self._open_file)

        self.save_action = QAction("&Save...", self)
        self.save_action.setShortcut(QKeySequence.StandardKey.Save)
        self.save_action.setStatusTip("Save the current file")
        self.save_action.triggered.connect(self._save_file)

        self.exit_action = QAction("E&xit", self)
        self.exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        self.exit_action.setStatusTip("Exit the application")
        self.exit_action.triggered.connect(self.close)

        # Edit menu actions
        self.settings_action = QAction("&Settings", self)
        self.settings_action.setStatusTip("Configure application settings")
        self.settings_action.triggered.connect(self._show_settings)

        # Help menu actions
        self.about_action = QAction("&About", self)
        self.about_action.setStatusTip("Show the application's About box")
        self.about_action.triggered.connect(self._show_about)

    def _create_menus(self):
        """Create the application menus."""
        # File menu
        self.file_menu = self.menuBar().addMenu("&File")
        self.file_menu.addAction(self.open_action)
        self.file_menu.addAction(self.save_action)
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_action)

        # Edit menu
        self.edit_menu = self.menuBar().addMenu("&Edit")
        self.edit_menu.addAction(self.settings_action)

        # Help menu
        self.help_menu = self.menuBar().addMenu("&Help")
        self.help_menu.addAction(self.about_action)

    def _create_toolbar(self):
        """Create the application toolbar."""
        self.toolbar = QToolBar("Main Toolbar")
        self.toolbar.setMovable(False)
        self.toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(self.toolbar)

        self.toolbar.addAction(self.open_action)
        self.toolbar.addAction(self.save_action)
        self.toolbar.addSeparator()
        self.toolbar.addAction(self.settings_action)

    def _create_statusbar(self):
        """Create the status bar."""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)

        # Create API status widget
        self.api_status = APIStatusWidget(self)
        self.api_status.clear_cache_requested.connect(self._clear_api_cache)

        # Add API status widget to the right side of the status bar
        self.statusbar.addPermanentWidget(self.api_status)

        # Set initial status
        self.statusbar.showMessage("Ready")

        # Update API status based on config
        use_api = self.config.get("use_api", False)
        prefer_local = self.config.get("prefer_local", True)

        # Update API status
        self.api_status.set_api_enabled(use_api and not prefer_local)

        # Show processing mode in status bar
        if prefer_local:
            self.set_status("Using local processing")
        elif use_api:
            self.set_status("Using API processing with local fallback")

    def _restore_geometry(self):
        """Restore window geometry from settings."""
        geometry = self.config.get("window_geometry")
        if geometry:
            self.restoreGeometry(geometry)
        else:
            # Default to center of screen
            self.setGeometry(100, 100, 1000, 700)

    def closeEvent(self, event):
        """Handle window close event."""
        # Save window geometry
        self.config.set("window_geometry", self.saveGeometry())
        self.config.save()
        event.accept()

    def _open_file(self):
        """Open a file."""
        # Determine which tab is active
        current_tab = self.tabs.currentWidget()
        if current_tab == self.encode_tab:
            # Open file for encoding
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Open File for Encoding",
                "",
                "All Files (*)",
            )
            if file_path:
                self.encode_tab.load_file(file_path)
        else:
            # Open file for decoding
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Open Image for Decoding",
                "",
                "Image Files (*.png *.jpg *.jpeg *.bmp *.svg);;All Files (*)",
            )
            if file_path:
                self.decode_tab.load_image(file_path)

    def _save_file(self):
        """Save a file."""
        # Determine which tab is active
        current_tab = self.tabs.currentWidget()
        if current_tab == self.encode_tab:
            # Save encoded file
            self.encode_tab.save_output()
        else:
            # Save decoded data
            self.decode_tab.save_output()

    def _show_settings(self):
        """Show the settings dialog."""
        dialog = SettingsDialog(self)
        dialog.exec()

    def _show_about(self):
        """Show the about dialog."""
        dialog = AboutDialog(self)
        dialog.exec()

    def _clear_api_cache(self):
        """Clear the API cache."""
        # Implement API cache clearing
        from ..core.api_cache import APICache
        cache = APICache()
        cache.clear()
        self.api_status.set_status("API cache cleared", timeout=3000)
        logger.info("API cache cleared")

    def set_status(self, message):
        """Set the status bar message."""
        self.statusbar.showMessage(message)

    def set_api_status(self, message, error=False, timeout=5000):
        """
        Set the API status message.

        Args:
            message: Status message
            error: Whether this is an error message
            timeout: Time in milliseconds before the message is cleared
        """
        self.api_status.set_status(message, error, timeout)

    def set_api_active(self, active, progress=0):
        """
        Set whether the API is currently active.

        Args:
            active: Whether the API is active
            progress: Current progress (0-100)
        """
        self.api_status.set_api_active(active)
        if active:
            self.api_status.update_progress(progress)

    def update_api_progress(self, progress):
        """
        Update the API progress bar.

        Args:
            progress: Progress value (0-100)
        """
        self.api_status.update_progress(progress)
