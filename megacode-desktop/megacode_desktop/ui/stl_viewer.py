"""
STL viewer widget for the MegaCode Desktop application.
"""

import os
import logging

# Configure logger
logger = logging.getLogger(__name__)

# Try to import required modules
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    logger.warning("NumPy not available, STL preview will be limited")

try:
    from PyQt6.QtOpenGLWidgets import QOpenGLWidget
    from PyQt6.QtWidgets import QSizePolicy, QLabel, QVBoxLayout
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal
    from PyQt6.QtGui import QSurfaceFormat, QOpenGLContext
    QT_OPENGL_AVAILABLE = True
except ImportError:
    QT_OPENGL_AVAILABLE = False
    logger.warning("PyQt6 OpenGL widgets not available, STL preview will be limited")
    # Import basic widgets for fallback
    try:
        from PyQt6.QtWidgets import QWidget, QSizePolicy, Q<PERSON>abe<PERSON>, QVBoxLayout
        from PyQt6.QtCore import Qt, pyqtSignal
        QT_BASIC_AVAILABLE = True
    except ImportError:
        QT_BASIC_AVAILABLE = False
        logger.warning("PyQt6 basic widgets not available, STL preview will not work")

try:
    import OpenGL.GL as GL
    import OpenGL.GLU as GLU
    from OpenGL.arrays import vbo
    from stl import mesh
    OPENGL_AVAILABLE = True
except ImportError:
    OPENGL_AVAILABLE = False
    logger.warning("OpenGL not available, STL preview will be limited")


# Define the STL viewer widget based on available libraries
if QT_OPENGL_AVAILABLE and OPENGL_AVAILABLE and NUMPY_AVAILABLE:
    # Full-featured OpenGL STL viewer
    class STLViewerWidget(QOpenGLWidget):
        """OpenGL widget for viewing STL files."""

        # Signal emitted when the widget is ready
        initialized = pyqtSignal()
elif QT_BASIC_AVAILABLE:
    # Fallback to a basic widget with a label
    class STLViewerWidget(QLabel):
        """Fallback STL viewer widget."""

        # Signal emitted when the widget is ready (for compatibility)
        initialized = pyqtSignal()

        def __init__(self, parent=None):
            """Initialize the fallback STL viewer widget."""
            super().__init__(parent)
            self.setText("STL Preview not available.\nInstall PyOpenGL and numpy-stl for 3D preview.")
            self.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setStyleSheet("background-color: #f0f0f0; padding: 20px;")

        def load_stl_data(self, stl_data):
            """Stub method for compatibility."""
            self.setText(f"STL Preview not available.\nSTL file size: {len(stl_data)} bytes\nInstall PyOpenGL and numpy-stl for 3D preview.")
else:
    # Absolute fallback if even basic Qt widgets are not available
    class STLViewerWidget:
        """Dummy STL viewer class for when Qt is not available."""

        initialized = None  # No signal

        def __init__(self, parent=None):
            """Initialize the dummy STL viewer."""
            logger.error("Cannot create STL viewer: Qt widgets not available")

        def load_stl_data(self, stl_data):
            """Stub method that does nothing."""
            pass

# Continue with the OpenGL implementation if available
if QT_OPENGL_AVAILABLE and OPENGL_AVAILABLE and NUMPY_AVAILABLE:
    # Properly define the OpenGL STL viewer class
    class STLViewerWidget(QOpenGLWidget):
        """OpenGL widget for viewing STL files."""

        # Signal emitted when the widget is ready
        initialized = pyqtSignal()

        def __init__(self, parent=None):
            """Initialize the STL viewer widget."""
            super().__init__(parent)

            # Set OpenGL format
            fmt = QSurfaceFormat()
            fmt.setSamples(4)  # Anti-aliasing
            fmt.setDepthBufferSize(24)
            QSurfaceFormat.setDefaultFormat(fmt)

            # Set widget properties
            self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.setMinimumSize(200, 200)
            self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

            # Initialize variables
            self.mesh_data = None
            self.vbo = None
            self.rotation = [0, 0, 0]
            self.scale = 1.0
            self.position = [0, 0, -5]
            self.last_pos = None
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_rotation)
            self.timer.start(30)  # Update every 30ms

            # Check if OpenGL is available
            if not OPENGL_AVAILABLE:
                logger.warning("OpenGL not available, STL preview will be limited")

        def load_stl_data(self, stl_data):
            """
            Load STL data from bytes.

            Args:
                stl_data: STL file content as bytes
            """
            if not OPENGL_AVAILABLE:
                return

            try:
                # Create a temporary file to load the STL data
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as tmp:
                    tmp_path = tmp.name
                    tmp.write(stl_data)

                # Load the STL file
                self.mesh_data = mesh.Mesh.from_file(tmp_path)

                # Clean up the temporary file
                os.unlink(tmp_path)

                # Calculate center and scale
                self._calculate_center_and_scale()

                # Update the widget
                self.update()
                logger.info("STL data loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load STL data: {e}")
                self.mesh_data = None

        def _calculate_center_and_scale(self):
            """Calculate the center and scale for the mesh."""
            if self.mesh_data is None:
                return

            # Get the min and max coordinates
            min_coords = self.mesh_data.points.reshape(-1, 3).min(axis=0)
            max_coords = self.mesh_data.points.reshape(-1, 3).max(axis=0)

            # Calculate center
            center = (min_coords + max_coords) / 2
            self.position = [-center[0], -center[1], -5]

            # Calculate scale
            size = max_coords - min_coords
            max_size = max(size)
            if max_size > 0:
                self.scale = 2.0 / max_size
            else:
                self.scale = 1.0

        def initializeGL(self):
            """Initialize OpenGL."""
            if not OPENGL_AVAILABLE:
                return

            try:
                # Set clear color (background)
                GL.glClearColor(0.2, 0.2, 0.2, 1.0)

                # Enable depth testing
                GL.glEnable(GL.GL_DEPTH_TEST)

                # Enable lighting
                GL.glEnable(GL.GL_LIGHTING)
                GL.glEnable(GL.GL_LIGHT0)

                # Set light position
                GL.glLightfv(GL.GL_LIGHT0, GL.GL_POSITION, [0, 0, 10, 1])

                # Set material properties
                GL.glMaterialfv(GL.GL_FRONT_AND_BACK, GL.GL_AMBIENT, [0.2, 0.2, 0.2, 1.0])
                GL.glMaterialfv(GL.GL_FRONT_AND_BACK, GL.GL_DIFFUSE, [0.8, 0.8, 0.8, 1.0])
                GL.glMaterialfv(GL.GL_FRONT_AND_BACK, GL.GL_SPECULAR, [1.0, 1.0, 1.0, 1.0])
                GL.glMaterialf(GL.GL_FRONT_AND_BACK, GL.GL_SHININESS, 50.0)

                # Enable color material
                GL.glEnable(GL.GL_COLOR_MATERIAL)
                GL.glColorMaterial(GL.GL_FRONT_AND_BACK, GL.GL_AMBIENT_AND_DIFFUSE)

                # Signal that initialization is complete
                self.initialized.emit()
                logger.info("OpenGL initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize OpenGL: {e}")

        def resizeGL(self, width, height):
            """
            Handle widget resize events.

            Args:
                width: New width
                height: New height
            """
            if not OPENGL_AVAILABLE:
                return

            try:
                # Set viewport
                GL.glViewport(0, 0, width, height)

                # Set projection matrix
                GL.glMatrixMode(GL.GL_PROJECTION)
                GL.glLoadIdentity()
                aspect = width / height if height > 0 else 1
                GLU.gluPerspective(45, aspect, 0.1, 100.0)

                # Reset model view matrix
                GL.glMatrixMode(GL.GL_MODELVIEW)
                GL.glLoadIdentity()
            except Exception as e:
                logger.error(f"Failed to resize OpenGL viewport: {e}")

        def paintGL(self):
            """Paint the scene."""
            if not OPENGL_AVAILABLE or self.mesh_data is None:
                return

            try:
                # Clear the buffer
                GL.glClear(GL.GL_COLOR_BUFFER_BIT | GL.GL_DEPTH_BUFFER_BIT)

                # Set up the model view matrix
                GL.glMatrixMode(GL.GL_MODELVIEW)
                GL.glLoadIdentity()

                # Apply transformations
                GL.glTranslatef(self.position[0], self.position[1], self.position[2])
                GL.glRotatef(self.rotation[0], 1, 0, 0)
                GL.glRotatef(self.rotation[1], 0, 1, 0)
                GL.glRotatef(self.rotation[2], 0, 0, 1)
                GL.glScalef(self.scale, self.scale, self.scale)

                # Set color
                GL.glColor3f(0.7, 0.7, 1.0)

                # Draw the mesh
                GL.glBegin(GL.GL_TRIANGLES)
                for i in range(len(self.mesh_data.vectors)):
                    # Calculate normal
                    triangle = self.mesh_data.vectors[i]
                    normal = np.cross(
                        triangle[1] - triangle[0],
                        triangle[2] - triangle[0]
                    )
                    normal = normal / np.linalg.norm(normal)

                    # Set normal and draw triangle
                    GL.glNormal3fv(normal)
                    GL.glVertex3fv(triangle[0])
                    GL.glVertex3fv(triangle[1])
                    GL.glVertex3fv(triangle[2])
                GL.glEnd()
            except Exception as e:
                logger.error(f"Failed to paint OpenGL scene: {e}")

        def update_rotation(self):
            """Update rotation for animation."""
            if self.mesh_data is not None:
                self.rotation[1] += 0.5  # Rotate around Y axis
                if self.rotation[1] >= 360:
                    self.rotation[1] -= 360
                self.update()

        def mousePressEvent(self, event):
            """Handle mouse press events."""
            self.last_pos = event.position()

        def mouseMoveEvent(self, event):
            """Handle mouse move events."""
            if self.last_pos is None:
                return

            dx = event.position().x() - self.last_pos.x()
            dy = event.position().y() - self.last_pos.y()

            if event.buttons() & Qt.MouseButton.LeftButton:
                # Rotate
                self.rotation[0] += 0.5 * dy
                self.rotation[1] += 0.5 * dx
            elif event.buttons() & Qt.MouseButton.RightButton:
                # Pan
                self.position[0] += 0.01 * dx
                self.position[1] -= 0.01 * dy

            self.last_pos = event.position()
            self.update()

        def wheelEvent(self, event):
            """Handle mouse wheel events."""
            delta = event.angleDelta().y()
            if delta > 0:
                self.scale *= 1.1
            else:
                self.scale /= 1.1
            self.update()
