"""
Decode tab for the MegaCode Desktop application.
"""

import os
import logging
import threading
import io
import tempfile
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QCheckBox, QFileDialog,
    QTextEdit, QGroupBox, QFormLayout, QProgressBar, QMessageBox,
    QSplitter, QScrollArea, QTabWidget
)
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QSize, QByteArray

# Configure logger
logger = logging.getLogger(__name__)

# Try to import QSvgWidget, but provide a fallback if it's not available
try:
    from PyQt6.QtSvg import QSvgWidget
    SVG_WIDGET_AVAILABLE = True
except ImportError:
    SVG_WIDGET_AVAILABLE = False
    logger.warning("QSvgWidget not available, SVG preview will be limited")

from ..core.decoder import MegaCodeDecoder
from ..utils.file_utils import get_file_size_str, get_file_type
from ..utils.image_utils import load_image, resize_image, svg_to_pixmap

logger = logging.getLogger(__name__)


class DecoderWorker(QThread):
    """Worker thread for decoding operations."""

    # Signals
    progress_updated = pyqtSignal(int)
    decoding_complete = pyqtSignal(bool, bytes, dict, str)

    def __init__(self, decoder, image_path, options):
        """Initialize the worker thread."""
        super().__init__()
        self.decoder = decoder
        self.image_path = image_path
        self.options = options

    def run(self):
        """Run the decoding operation."""
        try:
            # Decode the image
            result = self.decoder.decode(
                self.image_path,
                self.options,
                progress_callback=self.progress_updated.emit,
            )

            if result["success"]:
                # Emit the result
                self.decoding_complete.emit(
                    True,
                    result["data"],
                    result["metadata"],
                    "",
                )
            else:
                # Emit the error
                self.decoding_complete.emit(
                    False,
                    b"",
                    result.get("metadata", {}),
                    result.get("error", "Unknown error"),
                )
        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            self.decoding_complete.emit(False, b"", {}, str(e))


class DecodeTab(QWidget):
    """Decode tab for the MegaCode Desktop application."""

    def __init__(self, parent=None):
        """Initialize the decode tab."""
        super().__init__(parent)
        self.parent = parent
        self.decoder = MegaCodeDecoder(parent)
        self.image_path = None
        self.decoded_data = None
        self.decoded_metadata = None

        # Set up the UI
        self._setup_ui()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Create a splitter for input/options and output
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel (input and options)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Input section
        input_group = QGroupBox("Input Image")
        input_layout = QVBoxLayout()
        input_group.setLayout(input_layout)

        # Input file selection
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("No image selected")
        file_layout.addWidget(self.file_path_label)

        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self._browse_input_image)
        file_layout.addWidget(self.browse_button)

        input_layout.addLayout(file_layout)

        # Image preview
        self.image_preview = QLabel("No image selected")
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setMinimumSize(300, 300)

        # Create a scroll area for the preview
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_preview)
        scroll_area.setWidgetResizable(True)
        input_layout.addWidget(scroll_area)

        # Add input group to left panel
        left_layout.addWidget(input_group)

        # Options section
        options_group = QGroupBox("Decoding Options")
        options_layout = QFormLayout()
        options_group.setLayout(options_layout)

        # Palette selection
        self.palette_combo = QComboBox()
        self.palette_combo.addItems(["auto", "2-color", "4-color", "8-color", "16-color"])
        self.palette_combo.setCurrentText("auto")
        options_layout.addRow("Color Palette:", self.palette_combo)

        # Debug mode
        self.debug_mode = QCheckBox()
        options_layout.addRow("Debug Mode:", self.debug_mode)

        # Add options group to left panel
        left_layout.addWidget(options_group)

        # Decode button
        self.decode_button = QPushButton("Decode")
        self.decode_button.clicked.connect(self._decode)
        self.decode_button.setEnabled(False)
        left_layout.addWidget(self.decode_button)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        left_layout.addWidget(self.progress_bar)

        # Add left panel to splitter
        splitter.addWidget(left_panel)

        # Right panel (output)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Output section
        output_group = QGroupBox("Decoded Output")
        output_layout = QVBoxLayout()
        output_group.setLayout(output_layout)

        # Create a tab widget for different output views
        self.output_tabs = QTabWidget()
        output_layout.addWidget(self.output_tabs)

        # Text output tab
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setPlaceholderText("Decoded data will appear here...")
        self.output_tabs.addTab(self.output_text, "Text View")

        # Preview tab for special file types
        self.preview_widget = QWidget()
        self.preview_layout = QVBoxLayout(self.preview_widget)
        self.preview_label = QLabel("No preview available")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_layout.addWidget(self.preview_label)
        self.output_tabs.addTab(self.preview_widget, "Preview")

        # Metadata section
        self.metadata_label = QLabel("No metadata available")
        output_layout.addWidget(self.metadata_label)

        # Save button
        self.save_button = QPushButton("Save Output...")
        self.save_button.clicked.connect(self.save_output)
        self.save_button.setEnabled(False)
        output_layout.addWidget(self.save_button)

        # Add output group to right panel
        right_layout.addWidget(output_group)

        # Add right panel to splitter
        splitter.addWidget(right_panel)

        # Set initial splitter sizes
        splitter.setSizes([400, 600])

    def _browse_input_image(self):
        """Browse for an input image."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Image for Decoding",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.svg);;All Files (*)",
        )
        if file_path:
            self.load_image(file_path)

    def load_image(self, image_path):
        """Load an image for decoding."""
        try:
            # Store the image path
            self.image_path = image_path

            # Update the UI
            file_name = os.path.basename(image_path)
            file_size = get_file_size_str(image_path)
            file_type = get_file_type(image_path)

            self.file_path_label.setText(f"{file_name} ({file_size}, {file_type})")

            # Check if it's an SVG file
            if file_name.lower().endswith('.svg'):
                if SVG_WIDGET_AVAILABLE:
                    # Use QSvgWidget if available
                    # Clear any existing layout
                    if self.image_preview.layout():
                        # Delete the layout
                        QWidget().setLayout(self.image_preview.layout())

                    # Create SVG widget
                    self.svg_widget = QSvgWidget(image_path)

                    # Create a layout for the preview
                    layout = QVBoxLayout()
                    layout.addWidget(self.svg_widget)

                    # Set the layout on the preview widget
                    self.image_preview.setLayout(layout)

                    logger.info("SVG preview updated successfully using QSvgWidget")
                else:
                    # Use our custom SVG to PNG conversion
                    try:
                        # Read SVG file content
                        with open(image_path, 'r') as f:
                            svg_content = f.read()

                        # Convert SVG to pixmap
                        pixmap = svg_to_pixmap(
                            svg_content,
                            width=self.image_preview.width(),
                            height=self.image_preview.height()
                        )

                        if not pixmap.isNull():
                            # Display the converted PNG
                            self.image_preview.setPixmap(pixmap)
                            logger.info("SVG preview updated successfully using CairoSVG conversion")
                        else:
                            # Fallback to text if conversion failed
                            self.image_preview.setText(f"SVG Preview\n\n{file_name}\nSize: {file_size}\nFormat: SVG")
                            logger.info("SVG preview shown as text (conversion failed)")
                    except Exception as e:
                        # Fallback for when conversion fails
                        self.image_preview.setText(f"SVG Preview\n\n{file_name}\nSize: {file_size}\nFormat: SVG\n\nPreview not available: {str(e)}")
                        logger.warning(f"SVG preview shown as text (conversion failed: {e})")
            else:
                # Load and display the image
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # Resize the image to fit the preview area
                    pixmap = pixmap.scaled(
                        self.image_preview.width(),
                        self.image_preview.height(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    self.image_preview.setPixmap(pixmap)
                else:
                    self.image_preview.setText("Failed to load image")

            # Enable the decode button
            self.decode_button.setEnabled(True)

            # Update status
            self.parent.set_status(f"Loaded {file_name} ({file_size})")
        except Exception as e:
            logger.error(f"Failed to load image: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load image: {e}",
            )

    def _decode(self):
        """Decode the input image."""
        # Check if we have an image
        if not self.image_path:
            QMessageBox.warning(
                self,
                "No Image",
                "Please select an image to decode.",
            )
            return

        # Store the original image path
        original_image_path = self.image_path
        temp_file = None

        try:
            # Check if it's an SVG file
            if self.image_path.lower().endswith('.svg'):
                # Ask the user if they want to convert to PNG
                response = QMessageBox.question(
                    self,
                    "Convert SVG to PNG",
                    "SVG files cannot be decoded directly. Would you like to automatically convert it to PNG for decoding?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if response == QMessageBox.StandardButton.No:
                    return

                # Convert SVG to PNG
                try:
                    # Read SVG file content
                    with open(self.image_path, 'r') as f:
                        svg_content = f.read()

                    # Convert SVG to PNG using our utility function
                    png_data = svg_to_pixmap(svg_content).toImage().convertToFormat(QImage.Format.Format_RGBA8888)

                    # Create a temporary file for the PNG
                    temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
                    temp_path = temp_file.name
                    temp_file.close()

                    # Save the PNG to the temporary file
                    png_data.save(temp_path, "PNG")

                    # Update the image path to use the temporary PNG
                    self.image_path = temp_path

                    # Inform the user
                    self.parent.set_status(f"Converted SVG to PNG for decoding")
                    logger.info(f"Converted SVG to PNG: {original_image_path} -> {temp_path}")
                except Exception as e:
                    logger.error(f"Failed to convert SVG to PNG: {e}")
                    QMessageBox.critical(
                        self,
                        "Conversion Failed",
                        f"Failed to convert SVG to PNG: {e}",
                    )
                    return

            # Get decoding options
            options = {
                "palette": self.palette_combo.currentText(),
                "debug": self.debug_mode.isChecked(),
                "force_local": self.parent.config.get("prefer_local", True),
            }

            # Disable UI during decoding
            self._set_ui_enabled(False)

            # Create and start the worker thread
            self.worker = DecoderWorker(self.decoder, self.image_path, options)
            self.worker.progress_updated.connect(self._update_progress)
            self.worker.decoding_complete.connect(lambda success, data, metadata, error:
                self._decoding_complete_cleanup(success, data, metadata, error, temp_file, original_image_path))
            self.worker.start()

            # Update status
            self.parent.set_status("Decoding...")
        except Exception as e:
            # Clean up temporary file if needed
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

            # Restore original image path
            self.image_path = original_image_path

            # Show error
            logger.error(f"Error during decode preparation: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Error during decode preparation: {e}",
            )

            # Re-enable UI
            self._set_ui_enabled(True)

    def _update_progress(self, progress):
        """Update the progress bar."""
        self.progress_bar.setValue(progress)

    def _decoding_complete_cleanup(self, success, data, metadata, error, temp_file=None, original_image_path=None):
        """
        Handle decoding completion with cleanup of temporary files.

        Args:
            success: Whether decoding was successful
            data: Decoded data
            metadata: Metadata from decoding
            error: Error message if decoding failed
            temp_file: Temporary file to clean up
            original_image_path: Original image path to restore
        """
        # Clean up temporary file if needed
        if temp_file and os.path.exists(temp_file.name):
            try:
                os.unlink(temp_file.name)
                logger.info(f"Cleaned up temporary file: {temp_file.name}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file: {e}")

        # Restore original image path if needed
        if original_image_path:
            self.image_path = original_image_path

        # Call the regular decoding complete handler
        self._decoding_complete(success, data, metadata, error)

    def _decoding_complete(self, success, data, metadata, error):
        """Handle decoding completion."""
        # Re-enable UI
        self._set_ui_enabled(True)

        if success:
            # Store the decoded data
            self.decoded_data = data
            self.decoded_metadata = metadata

            # Check if data is empty
            if not data:
                self.output_text.setPlainText("No data decoded. The image may not contain any encoded information.")
                logger.warning("Decoded data is empty")

                # Add note to metadata
                if self.decoded_metadata is None:
                    self.decoded_metadata = {}
                self.decoded_metadata["note"] = "No data decoded"
            else:
                # Check metadata format to determine how to display the data
                format_type = metadata.get("format", "unknown") if metadata else "unknown"

                # Update the output text based on format
                if format_type == "text":
                    # Handle text data
                    try:
                        # Try to decode as UTF-8 text
                        text = data.decode("utf-8", errors="replace")

                        # Check if text is empty or just whitespace
                        if not text.strip():
                            logger.warning("Decoded text is empty or whitespace only")
                            self.output_text.setPlainText("Decoded text is empty or contains only whitespace characters.")

                            # Add note to metadata
                            if self.decoded_metadata is None:
                                self.decoded_metadata = {}
                            self.decoded_metadata["note"] = "Empty text decoded"
                        else:
                            # Display the text
                            self.output_text.setPlainText(text)
                    except Exception as e:
                        logger.error(f"Failed to decode text: {e}")
                        self.output_text.setPlainText(f"Error displaying text: {e}")
                else:
                    # Try multiple approaches to display the data
                    try:
                        # First try: direct UTF-8 decoding
                        try:
                            text = data.decode("utf-8")
                            if text.strip():
                                logger.info("Successfully decoded as UTF-8 text")
                                self.output_text.setPlainText(text)
                                return
                        except UnicodeDecodeError:
                            pass

                        # Second try: handle null bytes
                        if b'\x00' in data:
                            # Remove null bytes and try again
                            cleaned = data.replace(b'\x00', b'')
                            try:
                                text = cleaned.decode('utf-8', errors='replace')

                                # Check if cleaned text is empty
                                if not text.strip():
                                    logger.warning("Cleaned text is empty or whitespace only")

                                    # Try to extract any visible content from the original data
                                    visible_parts = []
                                    for chunk in data.split(b'\x00'):
                                        try:
                                            if chunk:
                                                chunk_text = chunk.decode('utf-8', errors='replace')
                                                if any(c.isprintable() for c in chunk_text):
                                                    visible_parts.append(chunk_text)
                                        except:
                                            pass

                                    if visible_parts:
                                        self.output_text.setPlainText(
                                            "Extracted visible content (separated by null bytes):\n\n" +
                                            "\n---\n".join(visible_parts)
                                        )
                                        # Update the decoded data to use the cleaned version
                                        self.decoded_data = b'\n'.join(chunk.encode('utf-8') for chunk in visible_parts)
                                        return
                                else:
                                    # Display the cleaned text
                                    self.output_text.setPlainText(text)
                                    # Update the decoded data to use the cleaned version
                                    self.decoded_data = cleaned
                                    # Add note to metadata
                                    if self.decoded_metadata is None:
                                        self.decoded_metadata = {}
                                    self.decoded_metadata["note"] = "Null bytes removed for display"
                                    return
                            except UnicodeDecodeError:
                                pass

                        # Third try: check for other common encodings
                        for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                            try:
                                text = data.decode(encoding)
                                if text.strip() and any(c.isprintable() for c in text):
                                    logger.info(f"Successfully decoded as {encoding} text")
                                    self.output_text.setPlainText(text)
                                    # Add note to metadata
                                    if self.decoded_metadata is None:
                                        self.decoded_metadata = {}
                                    self.decoded_metadata["note"] = f"Decoded using {encoding} encoding"
                                    return
                            except (UnicodeDecodeError, LookupError):
                                pass

                        # Fourth try: try to find text in binary data
                        text_chunks = []
                        current_chunk = bytearray()

                        for byte in data:
                            # If it's a printable ASCII character or common whitespace
                            if 32 <= byte <= 126 or byte in (9, 10, 13):  # tab, LF, CR
                                current_chunk.append(byte)
                            else:
                                # End of a text chunk
                                if len(current_chunk) >= 4:  # Only keep chunks of reasonable size
                                    text_chunks.append(bytes(current_chunk))
                                current_chunk = bytearray()

                        # Add the last chunk if it exists
                        if len(current_chunk) >= 4:
                            text_chunks.append(bytes(current_chunk))

                        if text_chunks:
                            # Join the text chunks with separators
                            text_content = "\n--- Text chunk ---\n".join(
                                chunk.decode('utf-8', errors='replace') for chunk in text_chunks
                            )
                            self.output_text.setPlainText(
                                f"Extracted text chunks from binary data:\n\n{text_content}"
                            )
                            return

                        # Final fallback: display as hex dump
                        hex_dump = []
                        for i in range(0, len(data), 16):
                            chunk = data[i:i+16]
                            hex_line = ' '.join(f'{b:02x}' for b in chunk)

                            # Try to show ASCII representation
                            ascii_line = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)

                            # Add line number, hex values, and ASCII representation
                            hex_dump.append(f"{i:04x}: {hex_line.ljust(48)} | {ascii_line}")

                        # Display the hex dump
                        self.output_text.setPlainText(
                            f"Binary data: {len(data)} bytes\n\n"
                            f"Hex dump (offset: hex values | ASCII):\n\n" +
                            "\n".join(hex_dump)
                        )
                    except Exception as e:
                        # Ultimate fallback
                        logger.error(f"Failed to process data for display: {e}")
                        self.output_text.setPlainText(f"Binary data: {len(data)} bytes (Error: {e})")

            # Update preview based on file format
            self._update_preview(data, metadata)

            # Update metadata display
            if metadata:
                metadata_text = "\n".join([f"{k}: {v}" for k, v in metadata.items()])
                self.metadata_label.setText(f"Metadata:\n{metadata_text}")
            else:
                self.metadata_label.setText("No metadata available")

            # Enable the save button
            self.save_button.setEnabled(True)

            # Update status
            self.parent.set_status("Decoding complete")
        else:
            # Show error message
            QMessageBox.critical(
                self,
                "Decoding Failed",
                f"Failed to decode image: {error}",
            )

            # Update status
            self.parent.set_status("Decoding failed")

    def save_output(self):
        """Save the decoded data."""
        if not self.decoded_data:
            return

        # Determine if the data is text or binary
        is_text = False
        has_null_bytes = False

        try:
            # Try to decode as UTF-8 text
            self.decoded_data.decode("utf-8")
            is_text = True
        except UnicodeDecodeError:
            # Check if it might be text with null bytes
            if b'\x00' in self.decoded_data:
                has_null_bytes = True
                # Remove null bytes and try again
                cleaned = self.decoded_data.replace(b'\x00', b'')
                try:
                    cleaned.decode('utf-8')
                    is_text = True
                except UnicodeDecodeError:
                    is_text = False
            else:
                is_text = False

        # Determine file filter based on content type
        if is_text:
            file_filter = "Text Files (*.txt);;All Files (*)"
            default_ext = ".txt"
        else:
            file_filter = "Binary Files (*.bin);;All Files (*)"
            default_ext = ".bin"

        # Show save dialog
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Decoded Data",
            f"decoded{default_ext}",
            file_filter,
        )

        if file_path:
            try:
                # If it's text with null bytes, ask if the user wants to remove them
                data_to_save = self.decoded_data

                if has_null_bytes and is_text:
                    response = QMessageBox.question(
                        self,
                        "Remove Null Bytes?",
                        "The data contains null bytes. Do you want to remove them before saving?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes
                    )

                    if response == QMessageBox.StandardButton.Yes:
                        data_to_save = self.decoded_data.replace(b'\x00', b'')

                # Save the file
                with open(file_path, "wb") as f:
                    f.write(data_to_save)

                # Update status
                self.parent.set_status(f"Saved decoded data to {file_path}")
            except Exception as e:
                logger.error(f"Failed to save decoded data: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to save decoded data: {e}",
                )

    def _update_preview(self, data, metadata):
        """Update the preview tab based on the decoded data format."""
        if not data or not metadata:
            self.preview_label.setText("No preview available")
            return

        format_type = metadata.get("format", "unknown")
        description = metadata.get("description", "Unknown format")

        try:
            if format_type == "stl":
                # STL 3D model preview
                self._show_stl_preview(data, description)
            elif format_type == "svg":
                # SVG vector image preview
                self._show_svg_preview(data, description)
            elif format_type in ["png", "jpeg", "gif"]:
                # Image preview
                self._show_image_preview(data, description)
            elif format_type == "json":
                # JSON data preview
                self._show_json_preview(data, description)
            else:
                # Default preview
                self.preview_label.setText(f"Preview not available for {description}")
        except Exception as e:
            logger.error(f"Failed to update preview: {e}")
            self.preview_label.setText(f"Preview error: {e}")

    def _show_stl_preview(self, data, description):
        """Show STL 3D model preview."""
        try:
            # Import STL viewer
            from .stl_viewer import STLViewerWidget

            # Clear existing preview
            self._clear_preview_layout()

            # Create STL viewer
            stl_viewer = STLViewerWidget(self.preview_widget)
            self.preview_layout.addWidget(stl_viewer)

            # Load STL data
            stl_viewer.load_stl_data(data)

            # Add description label
            desc_label = QLabel(f"{description} - Use mouse to rotate/zoom")
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.preview_layout.addWidget(desc_label)

            # Switch to preview tab
            self.output_tabs.setCurrentIndex(1)

            logger.info("STL preview loaded successfully")
        except Exception as e:
            logger.error(f"Failed to show STL preview: {e}")
            self.preview_label.setText(f"STL preview not available: {e}")

    def _clear_preview_layout(self):
        """Clear the preview layout."""
        # Remove all widgets from preview layout except the default label
        while self.preview_layout.count() > 0:
            child = self.preview_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Re-add the default label
        self.preview_label = QLabel("No preview available")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_layout.addWidget(self.preview_label)

    def _set_ui_enabled(self, enabled):
        """Enable or disable UI elements during decoding."""
        self.browse_button.setEnabled(enabled)
        self.palette_combo.setEnabled(enabled)
        self.debug_mode.setEnabled(enabled)
        self.decode_button.setEnabled(enabled)
