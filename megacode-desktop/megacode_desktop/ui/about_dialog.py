"""
About dialog for the MegaCode Desktop application.
"""

import logging
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QDialogButtonBox, QTextBrowser
)
from PyQt6.QtGui import QPixmap, QFont
from PyQt6.QtCore import Qt

from .. import __version__

logger = logging.getLogger(__name__)


class AboutDialog(QDialog):
    """About dialog for the MegaCode Desktop application."""
    
    def __init__(self, parent=None):
        """Initialize the about dialog."""
        super().__init__(parent)
        self.parent = parent
        
        # Set dialog properties
        self.setWindowTitle("About MegaCode Desktop")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        # Set up the UI
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # Header layout
        header_layout = QHBoxLayout()
        
        # Logo (placeholder)
        logo_label = QLabel()
        # logo_label.setPixmap(QPixmap(":/images/logo.png").scaled(64, 64, Qt.AspectRatioMode.KeepAspectRatio))
        logo_label.setText("MegaCode")
        logo_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        header_layout.addWidget(logo_label)
        
        # Title and version
        title_layout = QVBoxLayout()
        
        title_label = QLabel("MegaCode Desktop")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        
        version_label = QLabel(f"Version {__version__}")
        title_layout.addWidget(version_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Description
        description_text = QTextBrowser()
        description_text.setOpenExternalLinks(True)
        description_text.setHtml("""
        <p>MegaCode Desktop is a PyQt6-based desktop application for encoding and decoding high-capacity 2D and 3D codes generated by the MegaCode system.</p>
        
        <p>MegaCode is a system for generating high-capacity 2D and 3D codes capable of storing up to 4MB of data (approximately 1 million AI prompt tokens).</p>
        
        <h3>Features</h3>
        <ul>
            <li>Compress data using gzip/Brotli</li>
            <li>Apply Reed-Solomon error correction</li>
            <li>Implement Luby Transform fountain codes for resilience</li>
            <li>Generate color-enhanced 2D codes with configurable palettes</li>
            <li>Create mosaic layouts of standard codes (QR, DataMatrix)</li>
            <li>Export to SVG/PDF for printing and STL for 3D printing</li>
        </ul>
        
        <p>For more information, visit <a href="https://megacode.example.com">megacode.example.com</a></p>
        
        <h3>License</h3>
        <p>MIT License</p>
        <p>Copyright (c) 2023 MegaCode Team</p>
        """)
        main_layout.addWidget(description_text)
        
        # Dialog buttons
        self.button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        self.button_box.accepted.connect(self.accept)
        main_layout.addWidget(self.button_box)
