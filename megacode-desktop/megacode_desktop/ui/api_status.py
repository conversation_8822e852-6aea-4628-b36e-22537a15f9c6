"""
API status widget for the MegaCode Desktop application.
"""

import logging
from PyQt6.QtWidgets import (
    QWidget, QLabel, QHBoxLayout, QVBoxLayout,
    QPushButton, QProgressBar, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QColor, QPalette

logger = logging.getLogger(__name__)


class APIStatusWidget(QWidget):
    """Widget for displaying API status."""

    # Signal emitted when the user clicks the clear cache button
    clear_cache_requested = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the API status widget."""
        super().__init__(parent)
        self.parent = parent

        # Set up timer for auto-hiding status messages
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._clear_status)
        self.status_timer.setSingleShot(True)

        # Set up the UI
        self._setup_ui()

        # Initialize state
        self.reset()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(5, 2, 5, 2)
        self.setLayout(main_layout)

        # API indicator
        self.api_indicator = QLabel("API")
        self.api_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.api_indicator.setFixedWidth(30)
        self.api_indicator.setStyleSheet("background-color: #444; color: #888; border-radius: 3px; padding: 2px;")
        main_layout.addWidget(self.api_indicator)

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        main_layout.addWidget(self.status_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedWidth(100)
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Clear cache button
        self.clear_cache_button = QPushButton("Clear Cache")
        self.clear_cache_button.setFixedWidth(100)
        self.clear_cache_button.clicked.connect(self.clear_cache_requested.emit)
        main_layout.addWidget(self.clear_cache_button)

    def set_api_enabled(self, enabled):
        """
        Set whether the API is enabled.

        Args:
            enabled: Whether the API is enabled
        """
        if enabled:
            self.api_indicator.setStyleSheet("background-color: #2a6; color: white; border-radius: 3px; padding: 2px;")
            self.api_indicator.setToolTip("API is enabled")
        else:
            self.api_indicator.setStyleSheet("background-color: #444; color: #888; border-radius: 3px; padding: 2px;")
            self.api_indicator.setToolTip("API is disabled")

    def set_api_active(self, active):
        """
        Set whether the API is currently active (making a request).

        Args:
            active: Whether the API is active
        """
        if active:
            self.api_indicator.setStyleSheet("background-color: #26a; color: white; border-radius: 3px; padding: 2px;")
            self.progress_bar.setVisible(True)
        else:
            # Restore normal state based on whether API is enabled
            self.set_api_enabled(self.parent.config.get("use_api", False))
            self.progress_bar.setVisible(False)
            self.progress_bar.setValue(0)

    def set_api_error(self, error=True):
        """
        Set whether there is an API error.

        Args:
            error: Whether there is an error
        """
        if error:
            self.api_indicator.setStyleSheet("background-color: #a42; color: white; border-radius: 3px; padding: 2px;")
            self.api_indicator.setToolTip("API error occurred")
        else:
            # Restore normal state based on whether API is enabled
            self.set_api_enabled(self.parent.config.get("use_api", False))

    def set_status(self, message, error=False, timeout=5000):
        """
        Set the status message.

        Args:
            message: Status message
            error: Whether this is an error message
            timeout: Time in milliseconds before the message is cleared (0 for no timeout)
        """
        self.status_label.setText(message)

        if error:
            self.status_label.setStyleSheet("color: #f44;")
        else:
            self.status_label.setStyleSheet("")

        # Set timer for auto-clearing if timeout > 0
        if timeout > 0:
            self.status_timer.start(timeout)

    def update_progress(self, value):
        """
        Update the progress bar.

        Args:
            value: Progress value (0-100)
        """
        self.progress_bar.setValue(value)

    def reset(self):
        """Reset the widget to its default state."""
        self.set_api_enabled(False)
        self.set_status("Ready")
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)

    def _clear_status(self):
        """Clear the status message."""
        self.status_label.setText("Ready")
        self.status_label.setStyleSheet("")
