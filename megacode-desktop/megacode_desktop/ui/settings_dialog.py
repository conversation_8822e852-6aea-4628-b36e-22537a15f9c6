"""
Settings dialog for the MegaCode Desktop application.
"""

import logging
import time
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QCheckBox, QTabWidget, QWidget,
    QGroupBox, QFormLayout, QDialogButtonBox, QLineEdit
)
from PyQt6.QtCore import Qt

from ..utils.config import Config

logger = logging.getLogger(__name__)


class SettingsDialog(QDialog):
    """Settings dialog for the MegaCode Desktop application."""

    def __init__(self, parent=None):
        """Initialize the settings dialog."""
        super().__init__(parent)
        self.parent = parent
        self.config = Config()

        # Set dialog properties
        self.setWindowTitle("Settings")
        self.setMinimumWidth(400)

        # Set up the UI
        self._setup_ui()

        # Load settings
        self._load_settings()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Create tabs
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # General settings tab
        general_tab = QWidget()
        general_layout = QVBoxLayout()
        general_tab.setLayout(general_layout)

        # Appearance group
        appearance_group = QGroupBox("Appearance")
        appearance_layout = QFormLayout()
        appearance_group.setLayout(appearance_layout)

        # Theme selection
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Light", "Dark", "Auto"])
        appearance_layout.addRow("Theme:", self.theme_combo)

        # Add appearance group to general tab
        general_layout.addWidget(appearance_group)

        # File handling group
        file_group = QGroupBox("File Handling")
        file_layout = QFormLayout()
        file_group.setLayout(file_layout)

        # Default save directory
        self.save_dir_label = QLabel("Default")
        file_layout.addRow("Save Directory:", self.save_dir_label)

        self.browse_save_dir_button = QPushButton("Browse...")
        file_layout.addRow("", self.browse_save_dir_button)

        # Add file group to general tab
        general_layout.addWidget(file_group)

        # Add general tab to tabs
        self.tabs.addTab(general_tab, "General")

        # Encoding settings tab
        encoding_tab = QWidget()
        encoding_layout = QVBoxLayout()
        encoding_tab.setLayout(encoding_layout)

        # Default encoding options group
        encoding_group = QGroupBox("Default Encoding Options")
        encoding_options_layout = QFormLayout()
        encoding_group.setLayout(encoding_options_layout)

        # Default format
        self.default_format_combo = QComboBox()
        self.default_format_combo.addItems(["svg", "png", "stl"])
        encoding_options_layout.addRow("Default Format:", self.default_format_combo)

        # Default compression
        self.default_compression_combo = QComboBox()
        self.default_compression_combo.addItems(["brotli", "gzip", "none"])
        encoding_options_layout.addRow("Default Compression:", self.default_compression_combo)

        # Default ECC level
        self.default_ecc_level_combo = QComboBox()
        self.default_ecc_level_combo.addItems(["L", "M", "Q", "H"])
        encoding_options_layout.addRow("Default ECC Level:", self.default_ecc_level_combo)

        # Default palette
        self.default_palette_combo = QComboBox()
        self.default_palette_combo.addItems(["2-color", "4-color", "8-color", "16-color"])
        encoding_options_layout.addRow("Default Palette:", self.default_palette_combo)

        # Add encoding group to encoding tab
        encoding_layout.addWidget(encoding_group)

        # Add encoding tab to tabs
        self.tabs.addTab(encoding_tab, "Encoding")

        # Decoding settings tab
        decoding_tab = QWidget()
        decoding_layout = QVBoxLayout()
        decoding_tab.setLayout(decoding_layout)

        # Default decoding options group
        decoding_group = QGroupBox("Default Decoding Options")
        decoding_options_layout = QFormLayout()
        decoding_group.setLayout(decoding_options_layout)

        # Default palette
        self.default_decode_palette_combo = QComboBox()
        self.default_decode_palette_combo.addItems(["auto", "2-color", "4-color", "8-color", "16-color"])
        decoding_options_layout.addRow("Default Palette:", self.default_decode_palette_combo)

        # Default debug mode
        self.default_debug_mode = QCheckBox()
        decoding_options_layout.addRow("Debug Mode:", self.default_debug_mode)

        # Add decoding group to decoding tab
        decoding_layout.addWidget(decoding_group)

        # Add decoding tab to tabs
        self.tabs.addTab(decoding_tab, "Decoding")

        # Advanced settings tab
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout()
        advanced_tab.setLayout(advanced_layout)

        # API settings group
        api_group = QGroupBox("API Settings")
        api_layout = QFormLayout()
        api_group.setLayout(api_layout)

        # Processing options
        self.prefer_local = QCheckBox()
        self.prefer_local.stateChanged.connect(self._toggle_api_settings)
        api_layout.addRow("Prefer Local Processing:", self.prefer_local)

        # Use API
        self.use_api = QCheckBox()
        self.use_api.stateChanged.connect(self._toggle_api_settings)
        api_layout.addRow("Use API as Fallback:", self.use_api)

        # API URL
        self.api_url_input = QLineEdit()
        self.api_url_input.setPlaceholderText("https://api.megacode.example.com/v1")
        api_layout.addRow("API URL:", self.api_url_input)

        # API Key
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your API key")
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        api_layout.addRow("API Key:", self.api_key_input)

        # API Description
        api_description = QLabel(
            "The MegaCode API allows you to encode and decode MegaCode symbols "
            "using a remote server. This can be useful for devices with limited "
            "processing power or for accessing advanced features."
        )
        api_description.setWordWrap(True)
        api_layout.addRow(api_description)

        # API Usage Statistics
        self.api_stats_label = QLabel("No API usage statistics available")
        self.api_stats_label.setWordWrap(True)
        api_layout.addRow("API Usage:", self.api_stats_label)

        # Refresh API Stats button
        self.refresh_stats_button = QPushButton("Refresh Stats")
        self.refresh_stats_button.clicked.connect(self._refresh_api_stats)
        api_layout.addRow("", self.refresh_stats_button)

        # Clear API Cache button
        self.clear_cache_button = QPushButton("Clear API Cache")
        self.clear_cache_button.clicked.connect(self._clear_api_cache)
        api_layout.addRow("", self.clear_cache_button)

        # Add API group to advanced tab
        advanced_layout.addWidget(api_group)

        # Add advanced tab to tabs
        self.tabs.addTab(advanced_tab, "Advanced")

        # Dialog buttons
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        self.button_box.accepted.connect(self._save_settings)
        self.button_box.rejected.connect(self.reject)
        main_layout.addWidget(self.button_box)

    def _load_settings(self):
        """Load settings from the configuration."""
        # General settings
        self.theme_combo.setCurrentText(self.config.get("theme", "Auto"))
        self.save_dir_label.setText(self.config.get("save_directory", "Default"))

        # Encoding settings
        self.default_format_combo.setCurrentText(self.config.get("default_format", "svg"))
        self.default_compression_combo.setCurrentText(self.config.get("default_compression", "brotli"))
        self.default_ecc_level_combo.setCurrentText(self.config.get("default_ecc_level", "M"))
        self.default_palette_combo.setCurrentText(self.config.get("default_palette", "8-color"))

        # Decoding settings
        self.default_decode_palette_combo.setCurrentText(self.config.get("default_decode_palette", "auto"))
        self.default_debug_mode.setChecked(self.config.get("default_debug_mode", False))

        # Advanced settings
        self.api_url_input.setText(self.config.get("api_url", "https://api.megacode.example.com/v1"))
        self.api_key_input.setText(self.config.get("api_key", ""))
        self.prefer_local.setChecked(self.config.get("prefer_local", True))
        self.use_api.setChecked(self.config.get("use_api", False))

        # Toggle API settings based on use_api checkbox
        self._toggle_api_settings()

    def _save_settings(self):
        """Save settings to the configuration."""
        # General settings
        self.config.set("theme", self.theme_combo.currentText())
        self.config.set("save_directory", self.save_dir_label.text())

        # Encoding settings
        self.config.set("default_format", self.default_format_combo.currentText())
        self.config.set("default_compression", self.default_compression_combo.currentText())
        self.config.set("default_ecc_level", self.default_ecc_level_combo.currentText())
        self.config.set("default_palette", self.default_palette_combo.currentText())

        # Decoding settings
        self.config.set("default_decode_palette", self.default_decode_palette_combo.currentText())
        self.config.set("default_debug_mode", self.default_debug_mode.isChecked())

        # Advanced settings
        self.config.set("api_url", self.api_url_input.text())
        self.config.set("api_key", self.api_key_input.text())
        self.config.set("prefer_local", self.prefer_local.isChecked())
        self.config.set("use_api", self.use_api.isChecked())

        # Save the configuration
        self.config.save()

        # Apply theme
        self._apply_theme()

        # Close the dialog
        self.accept()

    def _toggle_api_settings(self, state=None):
        """Toggle API settings based on checkboxes."""
        # If prefer_local is checked, disable use_api
        if self.prefer_local.isChecked():
            self.use_api.setEnabled(False)
            self.use_api.setChecked(False)
            enabled = False
        else:
            self.use_api.setEnabled(True)
            enabled = self.use_api.isChecked()

        # Update API settings based on enabled state
        self.api_url_input.setEnabled(enabled)
        self.api_key_input.setEnabled(enabled)
        self.refresh_stats_button.setEnabled(enabled)
        self.clear_cache_button.setEnabled(enabled)

        # Refresh API stats if enabled
        if enabled:
            self._refresh_api_stats()

    def _refresh_api_stats(self):
        """Refresh API usage statistics."""
        try:
            # Get API client
            from ..core.api_client import MegaCodeAPIClient

            # Create API client with current settings
            api_key = self.api_key_input.text()
            api_url = self.api_url_input.text()
            client = MegaCodeAPIClient(api_key=api_key, api_url=api_url)

            # Get rate limit information
            rate_limit = client.get_rate_limit()

            # Get cache statistics
            from ..core.api_cache import APICache
            cache = APICache()
            cache_stats = cache.get_stats()

            # Format statistics
            stats_text = (
                f"Rate Limits:\n"
                f"  Limit: {rate_limit['limit']}\n"
                f"  Remaining: {rate_limit['remaining']}\n"
                f"  Reset: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(rate_limit['reset']))}\n\n"
                f"Cache Statistics:\n"
                f"  Entries: {cache_stats['entries']}\n"
                f"  Size: {self._format_size(cache_stats['size'])}\n"
                f"  Hits: {cache_stats['hits']}\n"
                f"  Misses: {cache_stats['misses']}\n"
                f"  Hit Rate: {self._calculate_hit_rate(cache_stats):.1f}%"
            )

            # Update statistics label
            self.api_stats_label.setText(stats_text)

        except Exception as e:
            self.api_stats_label.setText(f"Failed to get API statistics: {e}")

    def _clear_api_cache(self):
        """Clear the API cache."""
        try:
            # Get cache
            from ..core.api_cache import APICache
            cache = APICache()

            # Clear cache
            cleared = cache.clear()

            # Update statistics label
            self.api_stats_label.setText(f"API cache cleared ({cleared} entries)")

        except Exception as e:
            self.api_stats_label.setText(f"Failed to clear API cache: {e}")

    def _format_size(self, size_bytes):
        """Format size in bytes to human-readable format."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def _calculate_hit_rate(self, stats):
        """Calculate cache hit rate."""
        total = stats['hits'] + stats['misses']
        if total == 0:
            return 0.0
        return (stats['hits'] / total) * 100.0

    def _apply_theme(self):
        """Apply the selected theme."""
        theme = self.config.get("theme", "Auto")
        if theme == "Light":
            import qdarktheme
            qdarktheme.setup_theme("light")
        elif theme == "Dark":
            import qdarktheme
            qdarktheme.setup_theme("dark")
        else:  # Auto
            import qdarktheme
            qdarktheme.setup_theme("auto")
