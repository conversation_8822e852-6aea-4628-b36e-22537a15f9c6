"""
Encode tab for the MegaCode Desktop application.
"""

import os
import logging
import threading
import tempfile
import io

# Configure logger
logger = logging.getLogger(__name__)
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QFileDialog,
    QTextEdit, QGroupBox, QFormLayout, QProgressBar, QMessageBox,
    QSplitter, QScrollArea
)
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QSize, QUrl, QByteArray

# Try to import QSvgWidget, but provide a fallback if it's not available
try:
    from PyQt6.QtSvg import QSvgWidget
    SVG_WIDGET_AVAILABLE = True
except ImportError:
    SVG_WIDGET_AVAILABLE = False
    logger.warning("QSvgWidget not available, SVG preview will be limited")

from .stl_viewer import STLViewerWidget

from ..core.encoder import MegaCodeEncoder
from ..utils.file_utils import get_file_size_str, get_file_type
from ..utils.image_utils import resize_image, svg_to_pixmap

logger = logging.getLogger(__name__)


class EncoderWorker(QThread):
    """Worker thread for encoding operations."""

    # Signals
    progress_updated = pyqtSignal(int)
    encoding_complete = pyqtSignal(bool, str, str)

    def __init__(self, encoder, input_data, options):
        """Initialize the worker thread."""
        super().__init__()
        self.encoder = encoder
        self.input_data = input_data
        self.options = options

    def run(self):
        """Run the encoding operation."""
        try:
            # Encode the data
            result = self.encoder.encode(
                self.input_data,
                self.options,
                progress_callback=self.progress_updated.emit,
            )

            # Emit the result
            self.encoding_complete.emit(True, result, "")
        except Exception as e:
            logger.error(f"Encoding failed: {e}")
            self.encoding_complete.emit(False, "", str(e))


class EncodeTab(QWidget):
    """Encode tab for the MegaCode Desktop application."""

    def __init__(self, parent=None):
        """Initialize the encode tab."""
        super().__init__(parent)
        self.parent = parent
        self.encoder = MegaCodeEncoder(parent)
        self.input_data = None
        self.output_data = None

        # Set up the UI
        self._setup_ui()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # Create a splitter for input/options and output
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel (input and options)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Input section
        input_group = QGroupBox("Input")
        input_layout = QVBoxLayout()
        input_group.setLayout(input_layout)

        # Input file selection
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("No file selected")
        file_layout.addWidget(self.file_path_label)

        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self._browse_input_file)
        file_layout.addWidget(self.browse_button)

        input_layout.addLayout(file_layout)

        # Input text area
        self.input_text = QTextEdit()
        self.input_text.setPlaceholderText("Enter text to encode or load a file...")
        input_layout.addWidget(self.input_text)

        # Add input group to left panel
        left_layout.addWidget(input_group)

        # Options section
        options_group = QGroupBox("Encoding Options")
        options_layout = QFormLayout()
        options_group.setLayout(options_layout)

        # Format selection
        self.format_combo = QComboBox()
        self.format_combo.addItems(["svg", "png", "stl"])
        options_layout.addRow("Output Format:", self.format_combo)

        # Compression selection
        self.compression_combo = QComboBox()
        self.compression_combo.addItems(["brotli", "gzip", "none"])
        options_layout.addRow("Compression:", self.compression_combo)

        # Compression level
        self.compression_level = QSpinBox()
        self.compression_level.setRange(1, 11)
        self.compression_level.setValue(9)
        options_layout.addRow("Compression Level:", self.compression_level)

        # ECC level
        self.ecc_level_combo = QComboBox()
        self.ecc_level_combo.addItems(["L", "M", "Q", "H"])
        self.ecc_level_combo.setCurrentText("M")
        options_layout.addRow("ECC Level:", self.ecc_level_combo)

        # Fountain overhead
        self.fountain_overhead = QDoubleSpinBox()
        self.fountain_overhead.setRange(0.0, 1.0)
        self.fountain_overhead.setSingleStep(0.05)
        self.fountain_overhead.setValue(0.1)
        options_layout.addRow("Fountain Overhead:", self.fountain_overhead)

        # Color palette
        self.palette_combo = QComboBox()
        self.palette_combo.addItems(["2-color", "4-color", "8-color", "16-color"])
        self.palette_combo.setCurrentText("8-color")
        options_layout.addRow("Color Palette:", self.palette_combo)

        # Module size
        self.module_size = QSpinBox()
        self.module_size.setRange(1, 50)
        self.module_size.setValue(10)
        options_layout.addRow("Module Size:", self.module_size)

        # Finder patterns
        self.finder_patterns = QCheckBox()
        self.finder_patterns.setChecked(True)
        options_layout.addRow("Finder Patterns:", self.finder_patterns)

        # Calibration patterns
        self.calibration_patterns = QCheckBox()
        self.calibration_patterns.setChecked(True)
        options_layout.addRow("Calibration Patterns:", self.calibration_patterns)

        # Add options group to left panel
        left_layout.addWidget(options_group)

        # Encode button
        self.encode_button = QPushButton("Encode")
        self.encode_button.clicked.connect(self._encode)
        left_layout.addWidget(self.encode_button)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        left_layout.addWidget(self.progress_bar)

        # Add left panel to splitter
        splitter.addWidget(left_panel)

        # Right panel (output)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Output section
        output_group = QGroupBox("Output")
        output_layout = QVBoxLayout()
        output_group.setLayout(output_layout)

        # Output preview
        self.output_preview = QLabel("No output yet")
        self.output_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.output_preview.setMinimumSize(300, 300)

        # Create a scroll area for the preview
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.output_preview)
        scroll_area.setWidgetResizable(True)
        output_layout.addWidget(scroll_area)

        # Save button
        self.save_button = QPushButton("Save Output...")
        self.save_button.clicked.connect(self.save_output)
        self.save_button.setEnabled(False)
        output_layout.addWidget(self.save_button)

        # Add output group to right panel
        right_layout.addWidget(output_group)

        # Add right panel to splitter
        splitter.addWidget(right_panel)

        # Set initial splitter sizes
        splitter.setSizes([400, 600])

    def _browse_input_file(self):
        """Browse for an input file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open File for Encoding",
            "",
            "All Files (*)",
        )
        if file_path:
            self.load_file(file_path)

    def load_file(self, file_path):
        """Load a file for encoding."""
        try:
            # Read the file
            with open(file_path, "rb") as f:
                self.input_data = f.read()

            # Update the UI
            file_name = os.path.basename(file_path)
            file_size = get_file_size_str(file_path)
            file_type = get_file_type(file_path)

            self.file_path_label.setText(f"{file_name} ({file_size}, {file_type})")

            # Try to display text content if it's a text file
            if file_type.startswith("text/"):
                try:
                    self.input_text.setPlainText(self.input_data.decode("utf-8"))
                except UnicodeDecodeError:
                    self.input_text.setPlainText(f"Binary file: {file_name}")
            else:
                self.input_text.setPlainText(f"Binary file: {file_name}")

            # Update status
            self.parent.set_status(f"Loaded {file_name} ({file_size})")
        except Exception as e:
            logger.error(f"Failed to load file: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load file: {e}",
            )

    def _encode(self):
        """Encode the input data."""
        # Check if we have input data
        if not self.input_data and not self.input_text.toPlainText():
            QMessageBox.warning(
                self,
                "No Input",
                "Please enter text or load a file to encode.",
            )
            return

        # Get the input data
        if self.input_text.toPlainText() and not self.input_data:
            # Use the text from the text area
            self.input_data = self.input_text.toPlainText().encode("utf-8")

        # Get encoding options
        options = {
            "format": self.format_combo.currentText(),
            "compression": self.compression_combo.currentText(),
            "compression_level": self.compression_level.value(),
            "ecc_level": self.ecc_level_combo.currentText(),
            "fountain_overhead": self.fountain_overhead.value(),
            "palette": self.palette_combo.currentText(),
            "module_size": self.module_size.value(),
            "finder_patterns": self.finder_patterns.isChecked(),
            "calibration_patterns": self.calibration_patterns.isChecked(),
            "force_local": self.parent.config.get("prefer_local", True),
        }

        # Disable UI during encoding
        self._set_ui_enabled(False)

        # Create and start the worker thread
        self.worker = EncoderWorker(self.encoder, self.input_data, options)
        self.worker.progress_updated.connect(self._update_progress)
        self.worker.encoding_complete.connect(self._encoding_complete)
        self.worker.start()

        # Update status
        self.parent.set_status("Encoding...")

    def _update_progress(self, progress):
        """Update the progress bar."""
        self.progress_bar.setValue(progress)

    def _encoding_complete(self, success, result, error):
        """Handle encoding completion."""
        # Re-enable UI
        self._set_ui_enabled(True)

        if success:
            # Store the output data
            self.output_data = result

            # Update the preview
            self._update_preview()

            # Enable the save button
            self.save_button.setEnabled(True)

            # Update status
            self.parent.set_status("Encoding complete")
        else:
            # Show error message
            QMessageBox.critical(
                self,
                "Encoding Failed",
                f"Failed to encode data: {error}",
            )

            # Update status
            self.parent.set_status("Encoding failed")

    def _update_preview(self):
        """Update the output preview."""
        if not self.output_data:
            return

        # Get the output format
        output_format = self.format_combo.currentText()

        # Clear the current preview
        if hasattr(self, 'svg_widget') and SVG_WIDGET_AVAILABLE:
            self.svg_widget.deleteLater()
            delattr(self, 'svg_widget')

        if hasattr(self, 'stl_viewer'):
            self.stl_viewer.deleteLater()
            delattr(self, 'stl_viewer')

        # Reset the preview label
        self.output_preview.clear()
        self.output_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Remove any existing layout
        if self.output_preview.layout():
            # Remove all widgets from the layout
            while self.output_preview.layout().count():
                item = self.output_preview.layout().takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Delete the layout
            QWidget().setLayout(self.output_preview.layout())

        try:
            if output_format == "svg":
                # Display SVG preview
                if SVG_WIDGET_AVAILABLE:
                    # Use QSvgWidget if available
                    self.svg_widget = QSvgWidget()
                    self.svg_widget.renderer().load(QByteArray(self.output_data.encode('utf-8')))

                    # Create a layout for the preview
                    layout = QVBoxLayout()
                    layout.addWidget(self.svg_widget)

                    # Set the layout on the preview widget
                    self.output_preview.setLayout(layout)

                    # Log success
                    logger.info("SVG preview updated successfully using QSvgWidget")
                else:
                    try:
                        # Use our custom SVG to PNG conversion
                        pixmap = svg_to_pixmap(
                            self.output_data,
                            width=self.output_preview.width(),
                            height=self.output_preview.height()
                        )

                        if not pixmap.isNull():
                            # Display the converted PNG
                            self.output_preview.setPixmap(pixmap)
                            logger.info("SVG preview updated successfully using CairoSVG conversion")
                        else:
                            # Fallback to text if conversion failed
                            self.output_preview.setText(
                                "SVG Preview\n\n"
                                "Size: {} bytes\n"
                                "Format: SVG\n\n"
                                "SVG preview not available. Install PyQt6-QtSvg for SVG preview.".format(len(self.output_data))
                            )
                            logger.info("SVG preview shown as text (conversion failed)")
                    except Exception as e:
                        # Fallback for when conversion fails
                        self.output_preview.setText(
                            "SVG Preview\n\n"
                            "Size: {} bytes\n"
                            "Format: SVG\n\n"
                            "SVG preview not available: {}".format(len(self.output_data), str(e))
                        )
                        logger.warning(f"SVG preview shown as text (conversion failed: {e})")

            elif output_format == "png":
                # Display PNG preview
                pixmap = QPixmap()
                pixmap.loadFromData(self.output_data)

                if not pixmap.isNull():
                    # Resize the image to fit the preview area
                    pixmap = resize_image(
                        pixmap,
                        width=self.output_preview.width(),
                        height=self.output_preview.height(),
                        keep_aspect_ratio=True
                    )
                    self.output_preview.setPixmap(pixmap)

                    # Log success
                    logger.info("PNG preview updated successfully")
                else:
                    self.output_preview.setText("Failed to load PNG preview")
                    logger.error("Failed to load PNG preview")

            elif output_format == "stl":
                # Create STL viewer if it doesn't exist
                if not hasattr(self, 'stl_viewer'):
                    try:
                        self.stl_viewer = STLViewerWidget()

                        # Create a layout for the preview
                        layout = QVBoxLayout()
                        layout.addWidget(self.stl_viewer)

                        # Set the layout on the preview widget
                        self.output_preview.setLayout(layout)
                    except Exception as e:
                        logger.error(f"Failed to create STL viewer: {e}")
                        self.output_preview.setText(
                            "STL Preview (3D model)\n\n"
                            "Size: {} bytes\n"
                            "Format: STL\n\n"
                            "Failed to create STL viewer: {}".format(len(self.output_data), str(e))
                        )
                        return

                # Load STL data
                try:
                    self.stl_viewer.load_stl_data(self.output_data)
                    logger.info("STL preview updated successfully")
                except Exception as e:
                    logger.error(f"Failed to load STL data: {e}")
                    self.output_preview.setText(
                        "STL Preview (3D model)\n\n"
                        "Size: {} bytes\n"
                        "Format: STL\n\n"
                        "Failed to load STL data: {}".format(len(self.output_data), str(e))
                    )
        except Exception as e:
            # Handle any errors
            self.output_preview.setText(f"Failed to display preview: {e}")
            logger.error(f"Failed to update preview: {e}")

    def save_output(self):
        """Save the output data."""
        if not self.output_data:
            return

        # Get the output format
        output_format = self.format_combo.currentText()

        # Determine file filter based on format
        if output_format == "svg":
            file_filter = "SVG Files (*.svg)"
            default_ext = ".svg"
        elif output_format == "png":
            file_filter = "PNG Files (*.png)"
            default_ext = ".png"
        elif output_format == "stl":
            file_filter = "STL Files (*.stl)"
            default_ext = ".stl"
        else:
            file_filter = "All Files (*)"
            default_ext = ""

        # Show save dialog
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Output",
            f"megacode{default_ext}",
            file_filter,
        )

        if file_path:
            try:
                # Save the file
                output_format = self.format_combo.currentText()
                if output_format == "svg":
                    # SVG is a string, so write it as text
                    with open(file_path, "w") as f:
                        f.write(self.output_data)
                else:
                    # PNG and STL are bytes, so write them as binary
                    with open(file_path, "wb") as f:
                        f.write(self.output_data)

                # Update status
                self.parent.set_status(f"Saved output to {file_path}")
            except Exception as e:
                logger.error(f"Failed to save output: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to save output: {e}",
                )

    def _set_ui_enabled(self, enabled):
        """Enable or disable UI elements during encoding."""
        self.browse_button.setEnabled(enabled)
        self.input_text.setEnabled(enabled)
        self.format_combo.setEnabled(enabled)
        self.compression_combo.setEnabled(enabled)
        self.compression_level.setEnabled(enabled)
        self.ecc_level_combo.setEnabled(enabled)
        self.fountain_overhead.setEnabled(enabled)
        self.palette_combo.setEnabled(enabled)
        self.module_size.setEnabled(enabled)
        self.finder_patterns.setEnabled(enabled)
        self.calibration_patterns.setEnabled(enabled)
        self.encode_button.setEnabled(enabled)
