#!/usr/bin/env python3
"""
Test script for enhanced MegaCode features including 3D integration and improved decoder.
"""

import sys
import os
import tempfile
import json

# Add paths for megacode modules
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath(os.path.join('.', '..', 'megacode-encoder')))
sys.path.insert(0, os.path.abspath(os.path.join('.', '..', 'megacode-scanner')))

def test_enhanced_decoder():
    """Test the enhanced custom decoder with various file formats."""
    print("🧪 Testing Enhanced Decoder...")
    
    from megacode_desktop.core.custom_decoder import CustomDecoder
    decoder = CustomDecoder()
    
    # Test cases with different file formats
    test_cases = [
        {
            "name": "ASCII STL",
            "data": b'solid test\nfacet normal 0 0 1\n  outer loop\n    vertex 0 0 0\n    vertex 1 0 0\n    vertex 0 1 0\n  endloop\nendfacet\nendsolid test',
            "expected_format": "stl"
        },
        {
            "name": "JSON Data",
            "data": b'{"name": "test", "value": 42, "array": [1, 2, 3]}',
            "expected_format": "json"
        },
        {
            "name": "Plain Text",
            "data": b'Hello, World!\nThis is a test message.',
            "expected_format": "text"
        },
        {
            "name": "SVG Image",
            "data": b'<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><circle cx="50" cy="50" r="40" fill="red"/></svg>',
            "expected_format": "svg"
        },
        {
            "name": "Binary STL Header",
            "data": b'\x00' * 80 + b'\x01\x00\x00\x00',  # 80-byte header + triangle count
            "expected_format": "stl"
        }
    ]
    
    for test_case in test_cases:
        print(f"  Testing {test_case['name']}...")
        format_info = decoder._detect_file_format(test_case['data'])
        
        if format_info['format'] == test_case['expected_format']:
            print(f"    ✅ Correctly detected as {format_info['description']}")
        else:
            print(f"    ❌ Expected {test_case['expected_format']}, got {format_info['format']}")
        
        # Test decoding
        result = decoder.decode_data(test_case['data'])
        if result['success']:
            print(f"    ✅ Decoding successful, {len(result['data'])} bytes")
        else:
            print(f"    ❌ Decoding failed: {result.get('error_message', 'Unknown error')}")
    
    print("✅ Enhanced Decoder tests completed\n")

def test_3d_integration():
    """Test 3D integration features."""
    print("🎮 Testing 3D Integration...")
    
    try:
        from megacode_desktop.ui.stl_viewer import STLViewerWidget
        print("  ✅ STLViewerWidget imported successfully")
        
        # Test STL data loading (without creating Qt application)
        sample_stl = b'solid test\nfacet normal 0 0 1\n  outer loop\n    vertex 0 0 0\n    vertex 1 0 0\n    vertex 0 1 0\n  endloop\nendfacet\nendsolid test'
        
        # Check if OpenGL dependencies are available
        try:
            import OpenGL.GL as GL
            import OpenGL.GLU as GLU
            from stl import mesh
            print("  ✅ OpenGL and STL dependencies available")
            
            # Test STL parsing
            with tempfile.NamedTemporaryFile(suffix='.stl', delete=False) as tmp:
                tmp.write(sample_stl)
                tmp_path = tmp.name
            
            try:
                stl_mesh = mesh.Mesh.from_file(tmp_path)
                print(f"  ✅ STL parsing successful, {len(stl_mesh.vectors)} triangles")
            except Exception as e:
                print(f"  ❌ STL parsing failed: {e}")
            finally:
                os.unlink(tmp_path)
                
        except ImportError as e:
            print(f"  ⚠️  3D dependencies not fully available: {e}")
            
    except Exception as e:
        print(f"  ❌ 3D integration test failed: {e}")
    
    print("✅ 3D Integration tests completed\n")

def test_preview_functionality():
    """Test preview functionality for different file types."""
    print("🖼️  Testing Preview Functionality...")
    
    try:
        from megacode_desktop.ui.decode_tab import DecodeTab
        print("  ✅ Enhanced DecodeTab imported successfully")
        
        # Test format detection for preview
        from megacode_desktop.core.custom_decoder import CustomDecoder
        decoder = CustomDecoder()
        
        preview_test_cases = [
            {
                "name": "STL File",
                "data": b'solid test\nfacet normal 0 0 1\n  outer loop\n    vertex 0 0 0\n    vertex 1 0 0\n    vertex 0 1 0\n  endloop\nendfacet\nendsolid test',
                "should_have_preview": True
            },
            {
                "name": "JSON File",
                "data": json.dumps({"test": "data", "numbers": [1, 2, 3]}).encode('utf-8'),
                "should_have_preview": True
            },
            {
                "name": "SVG File",
                "data": b'<svg xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="40"/></svg>',
                "should_have_preview": True
            }
        ]
        
        for test_case in preview_test_cases:
            format_info = decoder._detect_file_format(test_case['data'])
            print(f"  Testing {test_case['name']}: {format_info['description']}")
            
            # Check if format supports preview
            preview_formats = ["stl", "svg", "png", "jpeg", "gif", "json"]
            has_preview = format_info['format'] in preview_formats
            
            if has_preview == test_case['should_have_preview']:
                print(f"    ✅ Preview support correctly detected")
            else:
                print(f"    ❌ Preview support detection failed")
        
    except Exception as e:
        print(f"  ❌ Preview functionality test failed: {e}")
    
    print("✅ Preview Functionality tests completed\n")

def test_fountain_packet_parsing():
    """Test improved fountain packet parsing."""
    print("🌊 Testing Fountain Packet Parsing...")
    
    try:
        from megacode_desktop.core.custom_decoder import CustomDecoder
        decoder = CustomDecoder()
        
        # Test with sample data
        test_data = b"Hello, World! This is test data for fountain packet parsing."
        
        # Parse fountain packets
        packets = decoder._parse_fountain_packets(test_data)
        print(f"  ✅ Parsed {len(packets)} fountain packets")
        
        if packets:
            packet = packets[0]
            print(f"    Format: {packet.get('format', 'unknown')}")
            print(f"    Data size: {len(packet.get('data', b''))} bytes")
            print(f"    Original size: {packet.get('original_size', 0)} bytes")
        
        # Test decoding
        decoded = decoder._decode_fountain_packets(packets)
        if decoded:
            print(f"  ✅ Decoded {len(decoded)} bytes successfully")
        else:
            print(f"  ⚠️  No data decoded (expected for raw data)")
        
    except Exception as e:
        print(f"  ❌ Fountain packet parsing test failed: {e}")
    
    print("✅ Fountain Packet Parsing tests completed\n")

def main():
    """Run all enhanced feature tests."""
    print("🚀 MegaCode Enhanced Features Test Suite")
    print("=" * 50)
    
    test_enhanced_decoder()
    test_3d_integration()
    test_preview_functionality()
    test_fountain_packet_parsing()
    
    print("🎉 All enhanced feature tests completed!")
    print("\nKey improvements implemented:")
    print("  ✅ Enhanced file format detection (STL, SVG, JSON, etc.)")
    print("  ✅ 3D STL model preview with OpenGL")
    print("  ✅ Improved fountain packet parsing")
    print("  ✅ Better error handling and recovery")
    print("  ✅ Multi-tab output display (Text + Preview)")
    print("  ✅ Enhanced metadata with size and description")

if __name__ == "__main__":
    main()
