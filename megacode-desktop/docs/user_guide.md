# MegaCode Desktop User Guide

## Introduction

MegaCode Desktop is a powerful application for encoding and decoding high-capacity 2D and 3D codes. This guide will help you get started with the application and explain its features in detail.

## Installation

### Windows

1. Download the latest installer from the [releases page](https://github.com/megacode/megacode/releases).
2. Run the installer and follow the on-screen instructions.
3. Launch MegaCode Desktop from the Start menu or desktop shortcut.

### macOS

1. Download the latest DMG file from the [releases page](https://github.com/megacode/megacode/releases).
2. Open the DMG file and drag MegaCode Desktop to the Applications folder.
3. Launch MegaCode Desktop from the Applications folder or Launchpad.

### Linux

1. Download the latest AppImage from the [releases page](https://github.com/megacode/megacode/releases).
2. Make the AppImage executable: `chmod +x MegaCodeDesktop-*.AppImage`
3. Run the AppImage: `./MegaCodeDesktop-*.AppImage`

### From Source

If you prefer to install from source:

1. Clone the repository: `git clone https://github.com/megacode/megacode.git`
2. Navigate to the desktop app directory: `cd megacode/megacode-desktop`
3. Install dependencies: `pip install -r requirements.txt`
4. Run the application: `python main.py`

## Getting Started

When you first launch MegaCode Desktop, you'll see a window with two tabs: "Encode" and "Decode". The Encode tab is used to create MegaCode symbols, and the Decode tab is used to read them.

### Encoding Data

To encode data into a MegaCode symbol:

1. Select the "Encode" tab.
2. Enter text in the input field or click "Browse..." to select a file.
3. Configure the encoding options:
   - **Output Format**: Choose between SVG, PNG, or STL (for 3D printing).
   - **Compression**: Select the compression method (Brotli, GZip, or none).
   - **Compression Level**: Set the compression level (1-11).
   - **ECC Level**: Choose the error correction level (L, M, Q, or H).
   - **Fountain Overhead**: Set the fountain code overhead (0.0-1.0).
   - **Color Palette**: Choose the color palette (2, 4, 8, or 16 colors).
   - **Module Size**: Set the size of each module in pixels.
   - **Finder Patterns**: Enable or disable finder patterns.
   - **Calibration Patterns**: Enable or disable calibration patterns.
4. Click "Encode" to generate the MegaCode symbol.
5. Once encoding is complete, the symbol will be displayed in the preview area.
6. Click "Save Output..." to save the generated symbol to a file.

### Decoding Data

To decode a MegaCode symbol:

1. Select the "Decode" tab.
2. Click "Browse..." to select an image file containing a MegaCode symbol.
3. Configure the decoding options:
   - **Color Palette**: Choose the color palette (auto, 2, 4, 8, or 16 colors).
   - **Debug Mode**: Enable or disable debug mode.
4. Click "Decode" to read the MegaCode symbol.
5. Once decoding is complete, the extracted data will be displayed in the output area.
6. Click "Save Output..." to save the decoded data to a file.

## Advanced Features

### Settings

You can access the application settings by clicking "Edit" > "Settings" in the menu bar. The settings dialog allows you to configure:

- **General Settings**:
  - **Theme**: Choose between Light, Dark, or Auto (system default).
  - **Save Directory**: Set the default directory for saving files.

- **Encoding Settings**:
  - **Default Format**: Set the default output format.
  - **Default Compression**: Set the default compression method.
  - **Default ECC Level**: Set the default error correction level.
  - **Default Palette**: Set the default color palette.

- **Decoding Settings**:
  - **Default Palette**: Set the default color palette for decoding.
  - **Debug Mode**: Enable or disable debug mode by default.

- **Advanced Settings**:
  - **API URL**: Set the URL for the MegaCode API.
  - **Use API**: Enable or disable API integration.

### Command Line Interface

MegaCode Desktop can also be used from the command line:

```bash
# Encode a file
python main.py encode --input data.txt --output code.svg --format svg --palette 8-color

# Decode an image
python main.py decode --input code.png --output decoded.txt
```

Run `python main.py --help` for more information on command line options.

## Troubleshooting

### Common Issues

- **Encoding fails**: Check that the input data is valid and not too large. Try using a higher compression level or a smaller color palette.
- **Decoding fails**: Ensure the image is clear and well-lit. Try using a different color palette or enabling debug mode.
- **Application crashes**: Check that you have the latest version of the application and all dependencies installed.

### Getting Help

If you encounter any issues not covered in this guide, please:

1. Check the [FAQ](https://github.com/megacode/megacode/wiki/FAQ) for common questions.
2. Search for similar issues in the [issue tracker](https://github.com/megacode/megacode/issues).
3. If you can't find a solution, create a new issue with a detailed description of the problem.

## License

MegaCode Desktop is licensed under the MIT License. See the LICENSE file for details.
