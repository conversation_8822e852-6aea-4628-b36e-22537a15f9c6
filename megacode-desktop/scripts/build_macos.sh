#!/bin/bash
# Build script for MegaCode Desktop on macOS

echo "Building MegaCode Desktop for macOS..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python not found. Please install Python 3.8 or higher."
    exit 1
fi

# Check if PyInstaller is installed
if ! python3 -c "import PyInstaller" &> /dev/null; then
    echo "PyInstaller not found. Installing..."
    pip3 install pyinstaller
fi

# Create build directory
mkdir -p build

# Install dependencies
echo "Installing dependencies..."
pip3 install -r requirements.txt

# Run tests
echo "Running tests..."
python3 -m pytest tests

# Build application
echo "Building application..."
pyinstaller --name "MegaCode Desktop" \
    --windowed \
    --icon=megacode_desktop/resources/icons/app.icns \
    --add-data "megacode_desktop/resources:megacode_desktop/resources" \
    --hidden-import PyQt6 \
    --hidden-import PyQt6.QtCore \
    --hidden-import PyQt6.QtGui \
    --hidden-import PyQt6.QtWidgets \
    --hidden-import qdarktheme \
    main.py

# Copy additional files
echo "Copying additional files..."
cp README.md "dist/MegaCode Desktop/"
cp LICENSE "dist/MegaCode Desktop/"
mkdir -p "dist/MegaCode Desktop/docs"
cp docs/*.md "dist/MegaCode Desktop/docs/"

# Create DMG (if create-dmg is available)
if command -v create-dmg &> /dev/null; then
    echo "Creating DMG..."
    create-dmg \
        --volname "MegaCode Desktop" \
        --volicon "megacode_desktop/resources/icons/app.icns" \
        --window-pos 200 120 \
        --window-size 800 400 \
        --icon-size 100 \
        --icon "MegaCode Desktop.app" 200 190 \
        --hide-extension "MegaCode Desktop.app" \
        --app-drop-link 600 185 \
        "dist/MegaCode Desktop.dmg" \
        "dist/MegaCode Desktop.app"
    echo "DMG created: dist/MegaCode Desktop.dmg"
else
    echo "create-dmg not found. Skipping DMG creation."
    echo "To create a DMG, install create-dmg: brew install create-dmg"
fi

echo "Build completed successfully."
echo "Application: dist/MegaCode Desktop.app"
