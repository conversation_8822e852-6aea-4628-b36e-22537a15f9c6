#!/bin/bash
# Build script for MegaCode Desktop on Linux

echo "Building MegaCode Desktop for Linux..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python not found. Please install Python 3.8 or higher."
    exit 1
fi

# Check if PyInstaller is installed
if ! python3 -c "import PyInstaller" &> /dev/null; then
    echo "PyInstaller not found. Installing..."
    pip3 install pyinstaller
fi

# Create build directory
mkdir -p build

# Install dependencies
echo "Installing dependencies..."
pip3 install -r requirements.txt

# Run tests
echo "Running tests..."
python3 -m pytest tests

# Build application
echo "Building application..."
pyinstaller --name megacode-desktop \
    --windowed \
    --icon=megacode_desktop/resources/icons/app.png \
    --add-data "megacode_desktop/resources:megacode_desktop/resources" \
    --hidden-import PyQt6 \
    --hidden-import PyQt6.QtCore \
    --hidden-import PyQt6.QtGui \
    --hidden-import PyQt6.QtWidgets \
    --hidden-import qdarktheme \
    main.py

# Copy additional files
echo "Copying additional files..."
cp README.md dist/megacode-desktop/
cp LICENSE dist/megacode-desktop/
mkdir -p dist/megacode-desktop/docs
cp docs/*.md dist/megacode-desktop/docs/

# Create desktop entry
echo "Creating desktop entry..."
cat > dist/megacode-desktop.desktop << EOF
[Desktop Entry]
Name=MegaCode Desktop
Comment=Encode and decode high-capacity 2D and 3D codes
Exec=megacode-desktop
Icon=megacode-desktop
Terminal=false
Type=Application
Categories=Utility;Graphics;
EOF

# Create AppImage (if appimagetool is available)
if command -v appimagetool &> /dev/null; then
    echo "Creating AppImage..."
    
    # Create AppDir structure
    mkdir -p build/AppDir/usr/bin
    mkdir -p build/AppDir/usr/share/applications
    mkdir -p build/AppDir/usr/share/icons/hicolor/256x256/apps
    
    # Copy files to AppDir
    cp -r dist/megacode-desktop/* build/AppDir/usr/bin/
    cp dist/megacode-desktop.desktop build/AppDir/usr/share/applications/
    cp megacode_desktop/resources/icons/app.png build/AppDir/usr/share/icons/hicolor/256x256/apps/megacode-desktop.png
    
    # Create AppRun script
    cat > build/AppDir/AppRun << EOF
#!/bin/bash
APPDIR="\$(dirname "\$(readlink -f "\$0")")"
export PATH="\$APPDIR/usr/bin:\$PATH"
export LD_LIBRARY_PATH="\$APPDIR/usr/lib:\$LD_LIBRARY_PATH"
exec "\$APPDIR/usr/bin/megacode-desktop" "\$@"
EOF
    chmod +x build/AppDir/AppRun
    
    # Copy desktop entry and icon to root of AppDir
    cp dist/megacode-desktop.desktop build/AppDir/
    cp megacode_desktop/resources/icons/app.png build/AppDir/megacode-desktop.png
    
    # Build AppImage
    appimagetool build/AppDir dist/MegaCodeDesktop.AppImage
    
    echo "AppImage created: dist/MegaCodeDesktop.AppImage"
else
    echo "appimagetool not found. Skipping AppImage creation."
    echo "To create an AppImage, install appimagetool: https://github.com/AppImage/AppImageKit"
fi

echo "Build completed successfully."
echo "Executable: dist/megacode-desktop/megacode-desktop"
