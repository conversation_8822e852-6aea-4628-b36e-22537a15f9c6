@echo off
REM Build script for MegaCode Desktop on Windows

echo Building MegaCode Desktop for Windows...

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python not found. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if PyInstaller is installed
python -c "import PyInstaller" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo PyInstaller not found. Installing...
    pip install pyinstaller
)

REM Create build directory
if not exist build mkdir build

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Run tests
echo Running tests...
python -m pytest tests

REM Build executable
echo Building executable...
pyinstaller --name MegaCodeDesktop ^
    --windowed ^
    --icon=megacode_desktop/resources/icons/app.ico ^
    --add-data "megacode_desktop/resources;megacode_desktop/resources" ^
    --hidden-import PyQt6 ^
    --hidden-import PyQt6.QtCore ^
    --hidden-import PyQt6.QtGui ^
    --hidden-import PyQt6.QtWidgets ^
    --hidden-import qdarktheme ^
    main.py

REM Copy additional files
echo Copying additional files...
copy README.md dist\MegaCodeDesktop\
copy LICENSE dist\MegaCodeDesktop\
mkdir dist\MegaCodeDesktop\docs
copy docs\*.md dist\MegaCodeDesktop\docs\

REM Create installer using NSIS (if available)
where makensis >nul 2>nul
if %ERRORLEVEL% equ 0 (
    echo Creating installer...
    makensis scripts\windows_installer.nsi
    echo Installer created: dist\MegaCodeDesktop-Setup.exe
) else (
    echo NSIS not found. Skipping installer creation.
    echo To create an installer, install NSIS and run: makensis scripts\windows_installer.nsi
)

echo Build completed successfully.
echo Executable: dist\MegaCodeDesktop\MegaCodeDesktop.exe
