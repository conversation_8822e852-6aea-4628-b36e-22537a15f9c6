"""
Tests for the API client.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

from megacode_desktop.core.api_client import MegaCodeAPIClient


class TestAPIClient(unittest.TestCase):
    """Tests for the MegaCodeAPIClient class."""

    def setUp(self):
        """Set up test fixtures."""
        self.api_key = "test_api_key"
        self.api_url = "https://test.api.example.com"
        self.client = MegaCodeAPIClient(api_key=self.api_key, api_url=self.api_url)
        
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_encode_text(self, mock_post):
        """Test encoding text data."""
        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "success": True,
            "file_urls": {
                "png": "https://example.com/file.png"
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Mock file download
        mock_file_response = MagicMock()
        mock_file_response.content = b"PNG data"
        mock_file_response.raise_for_status.return_value = None
        
        with patch('megacode_desktop.core.api_client.requests.get', return_value=mock_file_response):
            result = self.client.encode("Test data", {"format": "png"})
            
        # Check result
        self.assertTrue(result["success"])
        self.assertEqual(result["files"]["png"], b"PNG data")
        
        # Check API call
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(args[0], f"{self.api_url}/encode")
        self.assertEqual(kwargs["headers"]["Authorization"], f"Bearer {self.api_key}")
        self.assertEqual(kwargs["data"]["data"], "Test data")
        
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_encode_binary(self, mock_post):
        """Test encoding binary data."""
        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "success": True,
            "file_urls": {
                "png": "https://example.com/file.png"
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Mock file download
        mock_file_response = MagicMock()
        mock_file_response.content = b"PNG data"
        mock_file_response.raise_for_status.return_value = None
        
        with patch('megacode_desktop.core.api_client.requests.get', return_value=mock_file_response):
            result = self.client.encode(b"Binary data", {"format": "png"})
            
        # Check result
        self.assertTrue(result["success"])
        self.assertEqual(result["files"]["png"], b"PNG data")
        
        # Check API call
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(args[0], f"{self.api_url}/encode")
        self.assertEqual(kwargs["headers"]["Authorization"], f"Bearer {self.api_key}")
        self.assertIn("files", kwargs)
        
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_encode_error(self, mock_post):
        """Test encoding with API error."""
        # Mock response
        mock_post.side_effect = Exception("API error")
        
        result = self.client.encode("Test data", {"format": "png"})
        
        # Check result
        self.assertFalse(result["success"])
        self.assertIn("API error", result["error"])
        
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_decode(self, mock_post):
        """Test decoding an image."""
        # Create a temporary image file
        with open("test_image.png", "wb") as f:
            f.write(b"PNG data")
        
        try:
            # Mock response
            mock_response = MagicMock()
            mock_response.json.return_value = {
                "success": True,
                "data_format": "text",
                "data": "Decoded text",
                "metadata": {"format": "text"}
            }
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            result = self.client.decode("test_image.png", {"palette": "auto"})
            
            # Check result
            self.assertTrue(result["success"])
            self.assertEqual(result["data"], "Decoded text")
            self.assertEqual(result["metadata"]["format"], "text")
            
            # Check API call
            mock_post.assert_called_once()
            args, kwargs = mock_post.call_args
            self.assertEqual(args[0], f"{self.api_url}/decode")
            self.assertEqual(kwargs["headers"]["Authorization"], f"Bearer {self.api_key}")
            self.assertIn("files", kwargs)
            self.assertIn("image", kwargs["files"])
            
        finally:
            # Clean up
            if os.path.exists("test_image.png"):
                os.unlink("test_image.png")
                
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_decode_binary(self, mock_post):
        """Test decoding an image with binary result."""
        # Create a temporary image file
        with open("test_image.png", "wb") as f:
            f.write(b"PNG data")
        
        try:
            # Mock response
            mock_response = MagicMock()
            mock_response.json.return_value = {
                "success": True,
                "data_format": "binary",
                "data_base64": "SGVsbG8sIHdvcmxkIQ==",  # "Hello, world!" in base64
                "metadata": {"format": "binary"}
            }
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            result = self.client.decode("test_image.png", {"palette": "auto"})
            
            # Check result
            self.assertTrue(result["success"])
            self.assertEqual(result["data"], b"Hello, world!")
            self.assertEqual(result["metadata"]["format"], "binary")
            
        finally:
            # Clean up
            if os.path.exists("test_image.png"):
                os.unlink("test_image.png")
                
    @patch('megacode_desktop.core.api_client.requests.post')
    def test_decode_error(self, mock_post):
        """Test decoding with API error."""
        # Create a temporary image file
        with open("test_image.png", "wb") as f:
            f.write(b"PNG data")
        
        try:
            # Mock response
            mock_post.side_effect = Exception("API error")
            
            result = self.client.decode("test_image.png", {"palette": "auto"})
            
            # Check result
            self.assertFalse(result["success"])
            self.assertIn("API error", result["error"])
            
        finally:
            # Clean up
            if os.path.exists("test_image.png"):
                os.unlink("test_image.png")


if __name__ == "__main__":
    unittest.main()
