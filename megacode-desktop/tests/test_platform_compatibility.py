#!/usr/bin/env python3
"""
Platform compatibility test for MegaCode Desktop application.

This script tests the MegaCode Desktop application on different platforms
(Windows, macOS, Linux) to ensure cross-platform compatibility.
"""

import os
import sys
import platform
import unittest
import subprocess
import tempfile
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class TestPlatformCompatibility(unittest.TestCase):
    """Test case for platform compatibility."""
    
    def setUp(self):
        """Set up test environment."""
        # Get platform information
        self.platform_system = platform.system()
        self.platform_release = platform.release()
        self.platform_version = platform.version()
        
        logger.info(f"Running tests on {self.platform_system} {self.platform_release} ({self.platform_version})")
        
        # Get project root directory
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create test data file
        self.test_data_path = os.path.join(self.test_dir, "test_data.txt")
        with open(self.test_data_path, "w") as f:
            f.write("Hello, MegaCode! This is a platform compatibility test.")
        
        # Create output file path
        self.output_path = os.path.join(self.test_dir, "output.svg")
    
    def test_python_version(self):
        """Test Python version compatibility."""
        # Get Python version
        python_version = sys.version_info
        
        logger.info(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check if Python version is supported
        self.assertGreaterEqual(python_version.major, 3)
        self.assertGreaterEqual(python_version.minor, 8)
    
    def test_qt_availability(self):
        """Test Qt availability."""
        try:
            # Try to import PyQt6
            import PyQt6
            from PyQt6.QtCore import QT_VERSION_STR
            
            logger.info(f"PyQt6 version: {PyQt6.__version__}")
            logger.info(f"Qt version: {QT_VERSION_STR}")
            
            # Check if QApplication can be created
            from PyQt6.QtWidgets import QApplication
            app = QApplication([])
            
            # Clean up
            app.quit()
        except ImportError as e:
            self.fail(f"PyQt6 not available: {e}")
        except Exception as e:
            self.fail(f"Qt test failed: {e}")
    
    def test_run_main_script(self):
        """Test running the main script."""
        # Skip this test if running in CI environment
        if os.environ.get("CI") == "true":
            logger.info("Skipping main script test in CI environment")
            return
        
        try:
            # Run the main script with --help flag
            main_script = os.path.join(self.project_root, "main.py")
            
            # Check if the script exists
            self.assertTrue(os.path.exists(main_script), f"Main script not found: {main_script}")
            
            # Run the script
            result = subprocess.run(
                [sys.executable, main_script, "--help"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            
            # Check if the script ran successfully
            self.assertEqual(result.returncode, 0, f"Main script failed: {result.stderr}")
            
            logger.info("Main script test passed")
        except subprocess.TimeoutExpired:
            self.fail("Main script timed out")
        except Exception as e:
            self.fail(f"Main script test failed: {e}")
    
    def test_platform_specific_features(self):
        """Test platform-specific features."""
        if self.platform_system == "Windows":
            self._test_windows_features()
        elif self.platform_system == "Darwin":
            self._test_macos_features()
        elif self.platform_system == "Linux":
            self._test_linux_features()
        else:
            logger.warning(f"Unknown platform: {self.platform_system}")
    
    def _test_windows_features(self):
        """Test Windows-specific features."""
        logger.info("Testing Windows-specific features")
        
        try:
            # Test file associations
            # This is just a placeholder for actual Windows-specific tests
            import winreg
            logger.info("Windows registry access successful")
        except ImportError:
            logger.warning("winreg module not available")
        except Exception as e:
            self.fail(f"Windows-specific test failed: {e}")
    
    def _test_macos_features(self):
        """Test macOS-specific features."""
        logger.info("Testing macOS-specific features")
        
        try:
            # Test macOS-specific features
            # This is just a placeholder for actual macOS-specific tests
            import objc
            logger.info("Objective-C bridge access successful")
        except ImportError:
            logger.warning("objc module not available")
        except Exception as e:
            self.fail(f"macOS-specific test failed: {e}")
    
    def _test_linux_features(self):
        """Test Linux-specific features."""
        logger.info("Testing Linux-specific features")
        
        try:
            # Test Linux-specific features
            # This is just a placeholder for actual Linux-specific tests
            import dbus
            logger.info("D-Bus access successful")
        except ImportError:
            logger.warning("dbus module not available")
        except Exception as e:
            self.fail(f"Linux-specific test failed: {e}")


if __name__ == "__main__":
    unittest.main()
