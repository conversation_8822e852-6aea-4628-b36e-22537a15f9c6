#!/usr/bin/env python3
"""
Test script for MegaCode Desktop application.

This script tests the core functionality of the MegaCode Desktop application,
including encoding and decoding with various options.
"""

import os
import sys
import unittest
import tempfile
import shutil
import logging
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import MegaCode modules
from megacode_desktop.core.encoder import MegaCodeEncoder
from megacode_desktop.core.decoder import MegaCodeDecoder
from megacode_desktop.utils.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class TestMegaCodeDesktop(unittest.TestCase):
    """Test case for MegaCode Desktop application."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create encoder and decoder instances
        self.encoder = MegaCodeEncoder()
        self.decoder = MegaCodeDecoder()
        
        # Create test data
        self.test_data = b"Hello, MegaCode! This is a test of the desktop application."
        
        # Define test options
        self.encode_options = {
            "format": "svg",
            "compression": "brotli",
            "compression_level": 9,
            "ecc_level": "M",
            "fountain_overhead": 0.1,
            "palette": "8-color",
            "module_size": 10,
            "finder_patterns": True,
            "calibration_patterns": True,
        }
        
        self.decode_options = {
            "palette": "auto",
            "debug": False,
        }
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove temporary directory
        shutil.rmtree(self.test_dir)
    
    def test_encode_svg(self):
        """Test encoding to SVG format."""
        # Set format to SVG
        options = self.encode_options.copy()
        options["format"] = "svg"
        
        # Encode data
        try:
            result = self.encoder.encode(self.test_data, options)
            
            # Check if result is not empty
            self.assertIsNotNone(result)
            self.assertGreater(len(result), 0)
            
            # Save result to file
            output_path = os.path.join(self.test_dir, "test_encode_svg.svg")
            with open(output_path, "wb") as f:
                f.write(result)
            
            logger.info(f"Saved SVG output to {output_path}")
            
            # Check if file exists and is not empty
            self.assertTrue(os.path.exists(output_path))
            self.assertGreater(os.path.getsize(output_path), 0)
            
            return output_path
        except Exception as e:
            self.fail(f"Encoding to SVG failed: {e}")
    
    def test_encode_png(self):
        """Test encoding to PNG format."""
        # Set format to PNG
        options = self.encode_options.copy()
        options["format"] = "png"
        
        # Encode data
        try:
            result = self.encoder.encode(self.test_data, options)
            
            # Check if result is not empty
            self.assertIsNotNone(result)
            self.assertGreater(len(result), 0)
            
            # Save result to file
            output_path = os.path.join(self.test_dir, "test_encode_png.png")
            with open(output_path, "wb") as f:
                f.write(result)
            
            logger.info(f"Saved PNG output to {output_path}")
            
            # Check if file exists and is not empty
            self.assertTrue(os.path.exists(output_path))
            self.assertGreater(os.path.getsize(output_path), 0)
            
            return output_path
        except Exception as e:
            self.fail(f"Encoding to PNG failed: {e}")
    
    def test_encode_decode_cycle(self):
        """Test full encode-decode cycle."""
        # Encode data to SVG
        svg_path = self.test_encode_svg()
        
        # Decode SVG
        try:
            result = self.decoder.decode(svg_path, self.decode_options)
            
            # Check if decoding was successful
            self.assertTrue(result["success"])
            self.assertIsNotNone(result["data"])
            
            # Check if decoded data matches original data
            self.assertEqual(result["data"], self.test_data)
            
            logger.info("Encode-decode cycle successful")
        except Exception as e:
            self.fail(f"Decoding failed: {e}")
    
    def test_different_palettes(self):
        """Test encoding with different color palettes."""
        palettes = ["2-color", "4-color", "8-color", "16-color"]
        
        for palette in palettes:
            # Set palette
            options = self.encode_options.copy()
            options["palette"] = palette
            
            # Encode data
            try:
                result = self.encoder.encode(self.test_data, options)
                
                # Check if result is not empty
                self.assertIsNotNone(result)
                self.assertGreater(len(result), 0)
                
                # Save result to file
                output_path = os.path.join(self.test_dir, f"test_palette_{palette}.svg")
                with open(output_path, "wb") as f:
                    f.write(result)
                
                logger.info(f"Saved {palette} output to {output_path}")
                
                # Check if file exists and is not empty
                self.assertTrue(os.path.exists(output_path))
                self.assertGreater(os.path.getsize(output_path), 0)
            except Exception as e:
                self.fail(f"Encoding with {palette} palette failed: {e}")
    
    def test_different_ecc_levels(self):
        """Test encoding with different ECC levels."""
        ecc_levels = ["L", "M", "Q", "H"]
        
        for ecc_level in ecc_levels:
            # Set ECC level
            options = self.encode_options.copy()
            options["ecc_level"] = ecc_level
            
            # Encode data
            try:
                result = self.encoder.encode(self.test_data, options)
                
                # Check if result is not empty
                self.assertIsNotNone(result)
                self.assertGreater(len(result), 0)
                
                # Save result to file
                output_path = os.path.join(self.test_dir, f"test_ecc_{ecc_level}.svg")
                with open(output_path, "wb") as f:
                    f.write(result)
                
                logger.info(f"Saved ECC level {ecc_level} output to {output_path}")
                
                # Check if file exists and is not empty
                self.assertTrue(os.path.exists(output_path))
                self.assertGreater(os.path.getsize(output_path), 0)
            except Exception as e:
                self.fail(f"Encoding with ECC level {ecc_level} failed: {e}")
    
    def test_large_data(self):
        """Test encoding and decoding large data."""
        # Create large test data (100 KB)
        large_data = b"Large data test. " * 5000
        
        # Encode data
        try:
            options = self.encode_options.copy()
            result = self.encoder.encode(large_data, options)
            
            # Check if result is not empty
            self.assertIsNotNone(result)
            self.assertGreater(len(result), 0)
            
            # Save result to file
            output_path = os.path.join(self.test_dir, "test_large_data.svg")
            with open(output_path, "wb") as f:
                f.write(result)
            
            logger.info(f"Saved large data output to {output_path}")
            
            # Check if file exists and is not empty
            self.assertTrue(os.path.exists(output_path))
            self.assertGreater(os.path.getsize(output_path), 0)
            
            # Decode SVG
            result = self.decoder.decode(output_path, self.decode_options)
            
            # Check if decoding was successful
            self.assertTrue(result["success"])
            self.assertIsNotNone(result["data"])
            
            # Check if decoded data matches original data
            self.assertEqual(result["data"], large_data)
            
            logger.info("Large data encode-decode cycle successful")
        except Exception as e:
            self.fail(f"Large data test failed: {e}")


if __name__ == "__main__":
    unittest.main()
