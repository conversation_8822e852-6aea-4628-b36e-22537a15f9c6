#!/usr/bin/env python3
"""
Test script to check if PyQt6 modules can be imported.
"""

import sys
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path}")

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QTabWidget, QToolBar, QStatusBar, QFileDialog
    print("Successfully imported from PyQt6.QtWidgets")
except ImportError as e:
    print(f"Error importing from PyQt6.QtWidgets: {e}")

try:
    from PyQt6.QtGui import QIcon, QKeySequence, QAction
    print("Successfully imported from PyQt6.QtGui")
except ImportError as e:
    print(f"Error importing from PyQt6.QtGui: {e}")

try:
    from PyQt6.QtCore import Qt, QSize
    print("Successfully imported from PyQt6.QtCore")
except ImportError as e:
    print(f"Error importing from PyQt6.QtCore: {e}")

try:
    import qdarktheme
    print("Successfully imported qdarktheme")
except ImportError as e:
    print(f"Error importing qdarktheme: {e}")
