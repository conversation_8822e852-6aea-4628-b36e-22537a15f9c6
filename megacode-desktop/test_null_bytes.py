#!/usr/bin/env python3
"""
Test script to verify the null byte handling in the custom decoder.
"""

import sys
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the custom decoder
from megacode_desktop.core.custom_decoder import CustomDecoder

def main():
    """Test the custom decoder with null bytes."""
    # Create a test string with null bytes
    test_string = "Hello, World!\x00This\x00is\x00a\x00test\x00with\x00null\x00bytes."
    test_bytes = test_string.encode('utf-8')
    
    print(f"Original string: {repr(test_string)}")
    print(f"Original bytes: {test_bytes}")
    
    # Create a decoder
    decoder = CustomDecoder()
    
    # Test the _is_text_with_nulls method
    is_text = decoder._is_text_with_nulls(test_bytes)
    print(f"Is text with nulls: {is_text}")
    
    # Test the _clean_null_bytes method
    cleaned = decoder._clean_null_bytes(test_bytes)
    print(f"Cleaned bytes: {cleaned}")
    print(f"Cleaned string: {cleaned.decode('utf-8')}")
    
    # Test the decode_data method
    result = decoder.decode_data(test_bytes)
    print(f"Decode result: {result}")
    
    # Create a test with only null bytes
    null_bytes = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
    print(f"\nTesting with only null bytes: {null_bytes}")
    
    # Test the _is_text_with_nulls method
    is_text = decoder._is_text_with_nulls(null_bytes)
    print(f"Is text with nulls: {is_text}")
    
    # Test the _clean_null_bytes method
    cleaned = decoder._clean_null_bytes(null_bytes)
    print(f"Cleaned bytes: {cleaned}")
    
    # Test the decode_data method
    result = decoder.decode_data(null_bytes)
    print(f"Decode result: {result}")
    
    # Create a test with text surrounded by null bytes
    surrounded = b'\x00\x00\x00Hello, World!\x00\x00\x00'
    print(f"\nTesting with text surrounded by null bytes: {surrounded}")
    
    # Test the _is_text_with_nulls method
    is_text = decoder._is_text_with_nulls(surrounded)
    print(f"Is text with nulls: {is_text}")
    
    # Test the _clean_null_bytes method
    cleaned = decoder._clean_null_bytes(surrounded)
    print(f"Cleaned bytes: {cleaned}")
    print(f"Cleaned string: {cleaned.decode('utf-8')}")
    
    # Test the decode_data method
    result = decoder.decode_data(surrounded)
    print(f"Decode result: {result}")
    
    # Create a test with the exact pattern from the user's issue
    null_pattern = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
    
    # Add some text in the middle
    text_in_nulls = null_pattern[:40] + b'This is hidden text inside null bytes' + null_pattern[40:]
    
    print(f"\nTesting with text hidden in null bytes pattern")
    
    # Test the _is_text_with_nulls method
    is_text = decoder._is_text_with_nulls(text_in_nulls)
    print(f"Is text with nulls: {is_text}")
    
    # Test the _clean_null_bytes method
    cleaned = decoder._clean_null_bytes(text_in_nulls)
    print(f"Cleaned bytes: {cleaned}")
    print(f"Cleaned string: {cleaned.decode('utf-8')}")
    
    # Test the decode_data method
    result = decoder.decode_data(text_in_nulls)
    print(f"Decode result success: {result['success']}")
    if result['success'] and result['data']:
        try:
            print(f"Decoded text: {result['data'].decode('utf-8')}")
        except UnicodeDecodeError:
            print(f"Decoded data is not valid UTF-8 text")

if __name__ == "__main__":
    main()
