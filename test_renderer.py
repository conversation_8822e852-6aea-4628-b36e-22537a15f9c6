#!/usr/bin/env python3
"""
Test script for MegaCode renderer module.
"""

import os
import logging
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@dataclass
class GridLayout:
    """2D grid layout for MegaCode."""
    
    width: int
    height: int
    grid: np.ndarray  # 3D array of RGB values
    has_finder_patterns: bool = False
    has_calibration_patterns: bool = False


def create_test_grid(size: int = 20) -> GridLayout:
    """
    Create a test grid with a pattern.
    
    Args:
        size: Size of the grid
    
    Returns:
        GridLayout object
    """
    # Create grid
    grid = np.ones((size, size, 3), dtype=np.uint8) * 255
    
    # Add a pattern
    for y in range(size):
        for x in range(size):
            # Checkerboard pattern
            if (x + y) % 2 == 0:
                grid[y, x] = [0, 0, 0]  # Black
            
            # Add some color
            if x < size // 2 and y < size // 2:
                grid[y, x] = [255, 0, 0]  # Red
            elif x >= size // 2 and y < size // 2:
                grid[y, x] = [0, 255, 0]  # Green
            elif x < size // 2 and y >= size // 2:
                grid[y, x] = [0, 0, 255]  # Blue
            elif x >= size // 2 and y >= size // 2:
                grid[y, x] = [255, 255, 0]  # Yellow
    
    # Create layout
    layout = GridLayout(
        width=size,
        height=size,
        grid=grid,
        has_finder_patterns=True,
        has_calibration_patterns=True,
    )
    
    return layout


def render_svg(
    layout: GridLayout,
    module_size: int = 10,
    output_file: Optional[str] = None,
) -> str:
    """
    Render the grid layout as an SVG image.
    
    Args:
        layout: GridLayout object
        module_size: Size of each module in pixels
        output_file: Path to save the SVG file (if None, returns SVG as string)
    
    Returns:
        SVG content as string
    """
    # Calculate dimensions
    width = layout.width * module_size
    height = layout.height * module_size
    
    # Create SVG content
    svg = f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">\n'
    
    # Add white background
    svg += f'  <rect x="0" y="0" width="{width}" height="{height}" fill="white" />\n'
    
    # Add modules
    for y in range(layout.height):
        for x in range(layout.width):
            # Get color
            color = layout.grid[y, x]
            color_hex = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
            
            # Add rectangle
            svg += f'  <rect x="{x * module_size}" y="{y * module_size}" width="{module_size}" height="{module_size}" fill="{color_hex}" />\n'
    
    # Close SVG
    svg += '</svg>'
    
    # Save to file or return as string
    if output_file:
        with open(output_file, 'w') as f:
            f.write(svg)
        logger.info(f"Saved SVG to {output_file}")
        return ""
    else:
        logger.info(f"Generated SVG ({len(svg)} bytes)")
        return svg


def render_png(
    layout: GridLayout,
    module_size: int = 10,
    output_file: Optional[str] = None,
) -> Optional[bytes]:
    """
    Render the grid layout as a PNG image.
    
    Args:
        layout: GridLayout object
        module_size: Size of each module in pixels
        output_file: Path to save the PNG file (if None, returns PNG as bytes)
    
    Returns:
        PNG content as bytes if output_file is None, otherwise None
    """
    try:
        from PIL import Image
    except ImportError:
        logger.warning("PIL not available, skipping PNG rendering")
        return None
    
    # Calculate dimensions
    width = layout.width * module_size
    height = layout.height * module_size
    
    # Create image
    img = Image.new("RGB", (width, height), "white")
    pixels = img.load()
    
    # Fill pixels
    for y in range(layout.height):
        for x in range(layout.width):
            # Get color
            color = tuple(layout.grid[y, x])
            
            # Fill module area
            for dy in range(module_size):
                for dx in range(module_size):
                    pixels[x * module_size + dx, y * module_size + dy] = color
    
    # Save to file or return as bytes
    if output_file:
        img.save(output_file, format="PNG")
        logger.info(f"Saved PNG to {output_file}")
        return None
    else:
        import io
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        png_bytes = img_bytes.getvalue()
        logger.info(f"Generated PNG ({len(png_bytes)} bytes)")
        return png_bytes


def test_renderer():
    """Test renderer."""
    # Create test grid
    layout = create_test_grid(size=20)
    logger.info(f"Created test grid of size {layout.width}x{layout.height}")
    
    # Test SVG renderer
    svg_output = "test_output.svg"
    svg = render_svg(layout, module_size=10, output_file=svg_output)
    
    # Verify SVG file was created
    if os.path.exists(svg_output):
        logger.info(f"SVG file created: {os.path.getsize(svg_output)} bytes")
    else:
        logger.error(f"SVG file not created: {svg_output}")
    
    # Test PNG renderer
    try:
        png_output = "test_output.png"
        png = render_png(layout, module_size=10, output_file=png_output)
        
        # Verify PNG file was created
        if os.path.exists(png_output):
            logger.info(f"PNG file created: {os.path.getsize(png_output)} bytes")
        else:
            logger.error(f"PNG file not created: {png_output}")
    except ImportError:
        logger.warning("PIL not available, skipping PNG test")
    
    logger.info("Renderer tests completed")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode renderer module")
    
    test_renderer()
    
    logger.info("All tests completed")


if __name__ == "__main__":
    main()
