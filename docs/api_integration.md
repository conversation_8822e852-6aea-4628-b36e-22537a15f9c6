# MegaCode API Integration Guide

This document provides information on integrating with the MegaCode API for encoding and decoding high-capacity 2D and 3D codes.

## API Overview

The MegaCode API provides RESTful endpoints for:

- Encoding data into MegaCode symbols
- Decoding MegaCode symbols from images
- Managing encoding/decoding jobs
- Retrieving job status and results

## Base URL

All API endpoints are relative to the base URL:

```
https://api.megacode.example.com
```

## Authentication

The API uses API keys for authentication. To obtain an API key, contact <NAME_EMAIL>.

Include your API key in the request header:

```
Authorization: Bearer YOUR_API_KEY
```

## Encoding API

### Encode Data

Encode data into a MegaCode symbol.

**Endpoint:** `POST /api/encode`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | File | Yes | The file to encode |
| format | String | No | Output format (svg, png, stl). Default: svg |
| compression | String | No | Compression method (brotli, gzip, none). Default: brotli |
| compressionLevel | Integer | No | Compression level (1-11). Default: 9 |
| eccLevel | String | No | Error correction level (L, M, Q, H). Default: M |
| fountainOverhead | Float | No | Fountain code overhead (0.0-1.0). Default: 0.1 |
| palette | String | No | Color palette (2-color, 4-color, 8-color, 16-color). Default: 8-color |
| moduleSize | Integer | No | Module size in pixels. Default: 10 |
| finderPatterns | Boolean | No | Include finder patterns. Default: true |
| calibrationPatterns | Boolean | No | Include calibration patterns. Default: true |

**Response:**

```json
{
  "success": true,
  "jobId": "job_123456789",
  "status": "completed",
  "result": {
    "format": "svg",
    "url": "https://api.megacode.example.com/results/job_123456789.svg",
    "data": "base64-encoded-data"
  }
}
```

**Example:**

```bash
curl -X POST \
  https://api.megacode.example.com/api/encode \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -F "file=@data.txt" \
  -F "format=svg" \
  -F "palette=8-color" \
  -F "eccLevel=M"
```

### Get Encoding Job Status

Check the status of an encoding job.

**Endpoint:** `GET /api/encode/{jobId}`

**Response:**

```json
{
  "success": true,
  "jobId": "job_123456789",
  "status": "completed",
  "progress": 100,
  "result": {
    "format": "svg",
    "url": "https://api.megacode.example.com/results/job_123456789.svg"
  }
}
```

## Decoding API

### Decode Image

Decode a MegaCode symbol from an image.

**Endpoint:** `POST /api/decode`

**Content-Type:** `multipart/form-data`

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | File | Yes | The image file to decode |
| palette | String | No | Color palette (auto, 2-color, 4-color, 8-color, 16-color). Default: auto |
| debug | Boolean | No | Enable debug mode. Default: false |

**Response:**

```json
{
  "success": true,
  "jobId": "job_987654321",
  "status": "completed",
  "result": {
    "data": "Hello, MegaCode!",
    "contentType": "text/plain",
    "metadata": {
      "compression": "brotli",
      "eccLevel": "M",
      "palette": "8-color",
      "errorsCorrected": 12
    }
  }
}
```

**Example:**

```bash
curl -X POST \
  https://api.megacode.example.com/api/decode \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -F "file=@code.png" \
  -F "palette=auto"
```

### Get Decoding Job Status

Check the status of a decoding job.

**Endpoint:** `GET /api/decode/{jobId}`

**Response:**

```json
{
  "success": true,
  "jobId": "job_987654321",
  "status": "completed",
  "progress": 100,
  "result": {
    "data": "Hello, MegaCode!",
    "contentType": "text/plain",
    "metadata": {
      "compression": "brotli",
      "eccLevel": "M",
      "palette": "8-color",
      "errorsCorrected": 12
    }
  }
}
```

## Job Status Values

The `status` field in job responses can have the following values:

- `queued`: The job is in the queue waiting to be processed.
- `processing`: The job is currently being processed.
- `completed`: The job has completed successfully.
- `failed`: The job has failed.

## Error Handling

The API returns standard HTTP status codes:

- `200 OK`: The request was successful.
- `400 Bad Request`: The request was invalid.
- `401 Unauthorized`: Authentication failed.
- `404 Not Found`: The requested resource was not found.
- `500 Internal Server Error`: An error occurred on the server.

Error responses include a JSON object with details:

```json
{
  "success": false,
  "error": "Invalid input file",
  "code": "INVALID_INPUT"
}
```

## Rate Limiting

The API is rate-limited to prevent abuse. The current limits are:

- 100 requests per minute
- 1000 requests per day

Rate limit information is included in the response headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1619712000
```

## SDK Integration

We provide SDKs for easy integration with the MegaCode API:

- [JavaScript SDK](https://github.com/megacode/megacode-js)
- [Python SDK](https://github.com/megacode/megacode-python)
- [Java SDK](https://github.com/megacode/megacode-java)

### JavaScript Example

```javascript
const MegaCode = require('megacode-js');

const client = new MegaCode.Client('YOUR_API_KEY');

// Encode data
client.encode({
  file: fs.createReadStream('data.txt'),
  format: 'svg',
  palette: '8-color',
  eccLevel: 'M'
})
.then(result => {
  console.log('Encoding successful:', result);
})
.catch(error => {
  console.error('Encoding failed:', error);
});

// Decode image
client.decode({
  file: fs.createReadStream('code.png'),
  palette: 'auto'
})
.then(result => {
  console.log('Decoding successful:', result);
})
.catch(error => {
  console.error('Decoding failed:', error);
});
```

### Python Example

```python
from megacode import MegaCodeClient

client = MegaCodeClient('YOUR_API_KEY')

# Encode data
with open('data.txt', 'rb') as f:
    result = client.encode(
        file=f,
        format='svg',
        palette='8-color',
        ecc_level='M'
    )
    print('Encoding successful:', result)

# Decode image
with open('code.png', 'rb') as f:
    result = client.decode(
        file=f,
        palette='auto'
    )
    print('Decoding successful:', result)
```

## Webhook Notifications

For long-running jobs, you can register a webhook URL to receive notifications when the job completes:

**Endpoint:** `POST /api/encode` or `POST /api/decode`

**Additional Parameter:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| webhookUrl | String | No | URL to receive webhook notifications |

When the job completes, a POST request will be sent to the webhook URL with the job result.

## Support

If you have any questions or need assistance with the API, please contact <NAME_EMAIL>.
