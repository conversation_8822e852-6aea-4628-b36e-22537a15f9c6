# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.env
.env.test
.cache/
coverage/
.nyc_output/
.DS_Store

# Rust
/target/
**/*.rs.bk
Cargo.lock

# Flutter
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
*.lock
*.iml
*.log
.idea/
.vscode/

# Project specific
*.svg
*.png
*.stl
*.pdf
*.jpg
*.jpeg
test_output.*
