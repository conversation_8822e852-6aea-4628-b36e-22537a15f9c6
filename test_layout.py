#!/usr/bin/env python3
"""
Test script for MegaCode layout module.
"""

import logging
import numpy as np
from typing import List, Tuple
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants for finder patterns
FINDER_PATTERN_SIZE = 7  # Size of QR-like finder pattern
QUIET_ZONE_SIZE = 4      # Size of quiet zone around the code


@dataclass
class GridLayout:
    """2D grid layout for MegaCode."""
    
    width: int
    height: int
    grid: np.ndarray  # 3D array of RGB values
    has_finder_patterns: bool = False
    has_calibration_patterns: bool = False


def _calculate_grid_dimensions(
    num_modules: int,
    aspect_ratio: float = 1.0,
) -> Tuple[int, int]:
    """
    Calculate grid dimensions based on number of modules.
    
    Args:
        num_modules: Number of modules to fit in the grid
        aspect_ratio: Width/height ratio
    
    Returns:
        Tuple of (width, height) in modules
    """
    # Calculate base dimensions
    base_width = int(np.ceil(np.sqrt(num_modules * aspect_ratio)))
    base_height = int(np.ceil(num_modules / base_width))
    
    # Ensure width is at least height
    if base_width < base_height:
        base_width, base_height = base_height, base_width
    
    # Add space for finder patterns and quiet zone
    width = base_width + 2 * (FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE)
    height = base_height + 2 * (FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE)
    
    return width, height


def generate_grid(
    colors: List[Tuple[int, int, int]],
    finder_patterns: bool = True,
    calibration_patterns: bool = True,
    aspect_ratio: float = 1.0,
) -> GridLayout:
    """
    Generate a 2D grid layout for the given colors.
    
    Args:
        colors: List of RGB color tuples
        finder_patterns: Whether to add finder patterns
        calibration_patterns: Whether to add calibration patterns
        aspect_ratio: Width/height ratio
    
    Returns:
        GridLayout object
    """
    # Calculate grid dimensions
    num_modules = len(colors)
    width, height = _calculate_grid_dimensions(num_modules, aspect_ratio)
    
    logger.info(f"Generating grid of size {width}x{height} for {num_modules} modules")
    
    # Create empty grid (white background)
    grid = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # Calculate data area dimensions
    data_width = width - 2 * (FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE)
    data_height = height - 2 * (FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE)
    
    # Fill grid with colors
    color_idx = 0
    for y in range(data_height):
        for x in range(data_width):
            if color_idx < len(colors):
                # Calculate position in full grid
                grid_x = x + FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE
                grid_y = y + FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE
                
                # Set color
                grid[grid_y, grid_x] = colors[color_idx]
                color_idx += 1
    
    # Create layout
    layout = GridLayout(width=width, height=height, grid=grid)
    
    # Add finder patterns if requested
    if finder_patterns:
        layout = add_finder_patterns(layout)
    
    # Add calibration patterns if requested
    if calibration_patterns:
        layout = add_calibration_patterns(layout)
    
    return layout


def add_finder_patterns(layout: GridLayout) -> GridLayout:
    """
    Add QR-like finder patterns to the grid.
    
    Args:
        layout: GridLayout object
    
    Returns:
        Updated GridLayout
    """
    grid = layout.grid.copy()
    width, height = layout.width, layout.height
    
    # Define finder pattern (similar to QR code)
    # 7x7 pattern: outer border, inner square, and center dot
    finder = np.ones((FINDER_PATTERN_SIZE, FINDER_PATTERN_SIZE, 3), dtype=np.uint8) * 255
    
    # Outer border (black)
    finder[0, :] = [0, 0, 0]  # Top
    finder[-1, :] = [0, 0, 0]  # Bottom
    finder[:, 0] = [0, 0, 0]  # Left
    finder[:, -1] = [0, 0, 0]  # Right
    
    # Inner square (black)
    finder[2:5, 2:5] = [0, 0, 0]
    
    # Add finder patterns at corners
    # Top-left
    grid[QUIET_ZONE_SIZE:QUIET_ZONE_SIZE+FINDER_PATTERN_SIZE, 
         QUIET_ZONE_SIZE:QUIET_ZONE_SIZE+FINDER_PATTERN_SIZE] = finder
    
    # Top-right
    grid[QUIET_ZONE_SIZE:QUIET_ZONE_SIZE+FINDER_PATTERN_SIZE, 
         width-QUIET_ZONE_SIZE-FINDER_PATTERN_SIZE:width-QUIET_ZONE_SIZE] = finder
    
    # Bottom-left
    grid[height-QUIET_ZONE_SIZE-FINDER_PATTERN_SIZE:height-QUIET_ZONE_SIZE, 
         QUIET_ZONE_SIZE:QUIET_ZONE_SIZE+FINDER_PATTERN_SIZE] = finder
    
    # Create updated layout
    updated_layout = GridLayout(
        width=width,
        height=height,
        grid=grid,
        has_finder_patterns=True,
        has_calibration_patterns=layout.has_calibration_patterns,
    )
    
    logger.info("Added finder patterns to grid")
    
    return updated_layout


def add_calibration_patterns(layout: GridLayout) -> GridLayout:
    """
    Add color calibration patterns to the grid.
    
    Args:
        layout: GridLayout object
    
    Returns:
        Updated GridLayout
    """
    grid = layout.grid.copy()
    width, height = layout.width, layout.height
    
    # Define standard colors for calibration
    calibration_colors = [
        (0, 0, 0),       # Black
        (255, 255, 255), # White
        (255, 0, 0),     # Red
        (0, 255, 0),     # Green
        (0, 0, 255),     # Blue
        (255, 255, 0),   # Yellow
        (255, 0, 255),   # Magenta
        (0, 255, 255),   # Cyan
    ]
    
    # Size of each calibration patch
    patch_size = 5
    
    # Add calibration pattern in bottom-right corner
    for i, color in enumerate(calibration_colors):
        # Calculate position
        x = width - QUIET_ZONE_SIZE - (i + 1) * patch_size
        y = height - QUIET_ZONE_SIZE - patch_size
        
        # Add color patch
        grid[y:y+patch_size, x:x+patch_size] = color
    
    # Create updated layout
    updated_layout = GridLayout(
        width=width,
        height=height,
        grid=grid,
        has_finder_patterns=layout.has_finder_patterns,
        has_calibration_patterns=True,
    )
    
    logger.info("Added calibration patterns to grid")
    
    return updated_layout


def test_layout():
    """Test layout generation."""
    # Generate some test colors
    colors = [
        (0, 0, 0),       # Black
        (255, 255, 255), # White
        (255, 0, 0),     # Red
        (0, 255, 0),     # Green
        (0, 0, 255),     # Blue
        (255, 255, 0),   # Yellow
        (255, 0, 255),   # Magenta
        (0, 255, 255),   # Cyan
    ] * 10  # Repeat to get more colors
    
    logger.info(f"Testing layout with {len(colors)} colors")
    
    # Generate grid without patterns
    layout_basic = generate_grid(
        colors,
        finder_patterns=False,
        calibration_patterns=False,
    )
    logger.info(f"Generated basic grid of size {layout_basic.width}x{layout_basic.height}")
    
    # Generate grid with finder patterns
    layout_with_finders = generate_grid(
        colors,
        finder_patterns=True,
        calibration_patterns=False,
    )
    logger.info(f"Generated grid with finders of size {layout_with_finders.width}x{layout_with_finders.height}")
    
    # Generate grid with calibration patterns
    layout_with_calibration = generate_grid(
        colors,
        finder_patterns=False,
        calibration_patterns=True,
    )
    logger.info(f"Generated grid with calibration of size {layout_with_calibration.width}x{layout_with_calibration.height}")
    
    # Generate grid with both patterns
    layout_full = generate_grid(
        colors,
        finder_patterns=True,
        calibration_patterns=True,
    )
    logger.info(f"Generated full grid of size {layout_full.width}x{layout_full.height}")
    
    # Verify that patterns were added
    assert not layout_basic.has_finder_patterns
    assert not layout_basic.has_calibration_patterns
    
    assert layout_with_finders.has_finder_patterns
    assert not layout_with_finders.has_calibration_patterns
    
    assert not layout_with_calibration.has_finder_patterns
    assert layout_with_calibration.has_calibration_patterns
    
    assert layout_full.has_finder_patterns
    assert layout_full.has_calibration_patterns
    
    logger.info("Layout tests passed")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode layout module")
    
    test_layout()
    
    logger.info("All tests completed")


if __name__ == "__main__":
    main()
