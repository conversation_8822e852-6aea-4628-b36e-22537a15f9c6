#!/usr/bin/env python3
"""
Test script for MegaCode error correction module.
"""

import os
import sys
import logging
import random
from typing import List, Dict, Union, Tuple, Optional

import reedsolo

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class ECCLevel:
    """Error correction levels, similar to QR code."""

    L = 7  # ~7% error correction
    M = 15  # ~15% error correction
    Q = 25  # ~25% error correction
    H = 30  # ~30% error correction


class RSBlock:
    """Reed-Solomon encoded block with metadata."""

    def __init__(
        self,
        data: bytes,
        ecc: bytes,
        ecc_level: int,
        block_id: int,
        total_blocks: int,
    ):
        self.data = data
        self.ecc = ecc
        self.ecc_level = ecc_level
        self.block_id = block_id
        self.total_blocks = total_blocks

    def to_bytes(self) -> bytes:
        """Convert the block to bytes for storage/transmission."""
        # Header: Magic bytes (2 bytes), ECC level (1 byte), block ID (1 byte), total blocks (1 byte)
        header = bytes([
            0x4D, 0x43,  # Magic bytes 'MC' for MegaCode
            self.ecc_level,
            self.block_id,
            self.total_blocks,
        ])

        # Data length (1 byte) and ECC length (1 byte)
        lengths = bytes([
            len(self.data),
            len(self.ecc),
        ])

        # Combine all parts
        return header + lengths + self.data + self.ecc

    @classmethod
    def from_bytes(cls, data: bytes) -> "RSBlock":
        """Create a block from its byte representation."""
        # Check magic bytes
        if data[0] != 0x4D or data[1] != 0x43:
            logger.warning(f"Invalid magic bytes: {data[0]:02x}{data[1]:02x}")

        # Parse header
        ecc_level = data[2]
        block_id = data[3]
        total_blocks = data[4]

        # Parse lengths
        data_length = data[5]
        ecc_length = data[6]

        # Extract data and ECC
        header_size = 7
        block_data = data[header_size:header_size + data_length]
        ecc_data = data[header_size + data_length:header_size + data_length + ecc_length]

        return cls(
            data=block_data,
            ecc=ecc_data,
            ecc_level=ecc_level,
            block_id=block_id,
            total_blocks=total_blocks,
        )


def apply_reed_solomon(
    data: bytes,
    level: int = ECCLevel.M,
    block_size: int = 255,
    nsym: Optional[int] = None,
) -> List[RSBlock]:
    """
    Apply Reed-Solomon error correction to data.

    Args:
        data: Input data bytes
        level: Error correction level (L, M, Q, H) or ECCLevel enum
        block_size: Maximum size of each RS block (typically 255 for RS(255,k))
        nsym: Number of error correction symbols (calculated from level if None)

    Returns:
        List of RSBlock objects containing data and ECC
    """
    # Calculate number of ECC symbols if not specified
    if nsym is None:
        # Maximum data symbols in a block
        max_data_symbols = block_size - 1  # Reserve 1 byte for metadata

        # Calculate ECC symbols based on level percentage
        nsym = int(max_data_symbols * (level / 100))

        # Ensure minimum number of ECC symbols
        nsym = max(nsym, 2)

        logger.info(f"Using {nsym} ECC symbols for level {level}")

    # Ensure block_size and nsym are valid
    if block_size > 255:
        logger.warning(f"Block size {block_size} exceeds maximum of 255, truncating")
        block_size = 255

    if nsym >= block_size:
        logger.warning(f"ECC symbols {nsym} must be less than block size {block_size}, adjusting")
        nsym = block_size // 2

    # Maximum data symbols per block
    max_data_symbols = block_size - nsym

    # Initialize Reed-Solomon codec
    rs = reedsolo.RSCodec(nsym)

    # Split data into blocks
    blocks = []
    total_blocks = (len(data) + max_data_symbols - 1) // max_data_symbols

    # Ensure total_blocks fits in a byte
    if total_blocks > 255:
        logger.warning(f"Total blocks {total_blocks} exceeds maximum of 255, data too large")
        raise ValueError(f"Data too large: requires {total_blocks} blocks")

    logger.info(f"Encoding {len(data)} bytes into {total_blocks} RS blocks")

    for i in range(total_blocks):
        # Extract block data
        start = i * max_data_symbols
        end = min(start + max_data_symbols, len(data))
        block_data = data[start:end]

        # Pad if necessary
        if len(block_data) < max_data_symbols:
            block_data = block_data + bytes([0] * (max_data_symbols - len(block_data)))

        # Encode with Reed-Solomon
        encoded = rs.encode(block_data)

        # Extract ECC portion (last nsym bytes)
        ecc = encoded[-nsym:]

        # Create block
        block = RSBlock(
            data=block_data,
            ecc=ecc,
            ecc_level=level,
            block_id=i,
            total_blocks=total_blocks,
        )

        blocks.append(block)

    return blocks


def decode_reed_solomon(blocks: List[RSBlock]) -> bytes:
    """
    Decode Reed-Solomon encoded blocks, correcting errors if possible.

    Args:
        blocks: List of RSBlock objects

    Returns:
        Decoded and corrected data
    """
    # Sort blocks by ID
    sorted_blocks = sorted(blocks, key=lambda b: b.block_id)

    # Check if we have all blocks
    block_ids = [b.block_id for b in sorted_blocks]
    total_blocks = sorted_blocks[0].total_blocks
    expected_ids = set(range(total_blocks))
    missing_ids = expected_ids - set(block_ids)

    if missing_ids:
        logger.warning(f"Missing blocks: {missing_ids}")
        raise ValueError(f"Cannot decode: missing {len(missing_ids)} blocks")

    # Decode each block
    decoded_data = bytearray()

    for block in sorted_blocks:
        # Initialize RS codec based on block's ECC
        nsym = len(block.ecc)
        rs = reedsolo.RSCodec(nsym)

        # Combine data and ECC for decoding
        encoded = block.data + block.ecc

        try:
            # Attempt to decode and correct errors
            decoded = rs.decode(encoded)

            # Convert to bytes if it's not already
            if not isinstance(decoded, (bytes, bytearray)):
                logger.warning(f"Unexpected decoded type: {type(decoded)}")
                # Try to extract the message part if it's a tuple
                if isinstance(decoded, tuple) and len(decoded) > 0:
                    decoded = decoded[0]
                else:
                    # Fall back to the original data
                    decoded = block.data

            logger.info(f"Decoded block {block.block_id}")

            # Remove padding from the last block
            if block.block_id == total_blocks - 1:
                # Find the first zero byte from the end
                i = len(decoded) - 1
                while i >= 0 and decoded[i] == 0:
                    i -= 1

                # Keep data up to that point (inclusive)
                decoded_data.extend(decoded[:i+1])
            else:
                decoded_data.extend(decoded)

        except reedsolo.ReedSolomonError as e:
            logger.error(f"Failed to decode block {block.block_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error decoding block {block.block_id}: {e}")
            raise

    return bytes(decoded_data)


def introduce_errors(data: bytes, error_rate: float = 0.1) -> bytes:
    """
    Introduce random errors into data for testing error correction.

    Args:
        data: Input data bytes
        error_rate: Percentage of bytes to corrupt (0.0 to 1.0)

    Returns:
        Data with errors introduced
    """
    # Convert to bytearray for modification
    corrupted = bytearray(data)

    # Calculate number of bytes to corrupt
    num_errors = int(len(data) * error_rate)

    # Introduce random errors
    for _ in range(num_errors):
        # Select random position
        pos = random.randint(0, len(data) - 1)

        # Corrupt byte (XOR with random value)
        corrupted[pos] ^= random.randint(1, 255)

    logger.info(f"Introduced {num_errors} errors ({error_rate:.1%} of data)")

    return bytes(corrupted)


def test_reed_solomon():
    """Test Reed-Solomon error correction."""
    # Test data
    test_data = b"Hello, MegaCode! This is a test of the Reed-Solomon error correction module."
    logger.info(f"Original data: {len(test_data)} bytes")

    # Use a smaller block size and fixed nsym for testing
    block_size = 50
    nsym = 10

    # Apply Reed-Solomon error correction
    blocks = apply_reed_solomon(
        test_data,
        level=ECCLevel.H,
        block_size=block_size,
        nsym=nsym
    )
    logger.info(f"Encoded into {len(blocks)} blocks")

    # Print block details
    for i, block in enumerate(blocks):
        logger.info(f"Block {i}: data={len(block.data)} bytes, ecc={len(block.ecc)} bytes")

    # Convert blocks to bytes for transmission
    encoded_blocks = [block.to_bytes() for block in blocks]
    logger.info(f"Encoded blocks: {sum(len(b) for b in encoded_blocks)} bytes")

    # Introduce errors in some blocks
    corrupted_blocks = []
    for i, block_bytes in enumerate(encoded_blocks):
        # Corrupt every other block
        if i % 2 == 0:
            corrupted = introduce_errors(block_bytes, error_rate=0.05)
        else:
            corrupted = block_bytes

        corrupted_blocks.append(corrupted)

    # Parse blocks back from bytes
    parsed_blocks = [RSBlock.from_bytes(block) for block in corrupted_blocks]

    # Verify block IDs and total blocks
    for i, block in enumerate(parsed_blocks):
        logger.info(f"Parsed block {i}: id={block.block_id}, total={block.total_blocks}")

    # Decode with error correction
    try:
        decoded = decode_reed_solomon(parsed_blocks)
        logger.info(f"Decoded data: {len(decoded)} bytes")

        # Verify data integrity
        if decoded == test_data:
            logger.info("Data integrity check passed")
        else:
            logger.error("Data integrity check failed")
            logger.error(f"Original: {test_data}")
            logger.error(f"Decoded: {decoded}")
    except Exception as e:
        logger.error(f"Decoding failed: {e}")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode error correction module")

    # Set random seed for reproducibility
    random.seed(42)

    test_reed_solomon()

    logger.info("All tests completed")


if __name__ == "__main__":
    main()
