#!/usr/bin/env python3
"""
Test script for MegaCode symbol mapping module.
"""

import logging
from typing import List, Tuple
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@dataclass
class ColorPalette:
    """Color palette for MegaCode."""
    
    name: str
    colors: List[Tuple[int, int, int]]  # RGB values
    bits_per_symbol: int
    
    def __post_init__(self):
        """Validate the palette after initialization."""
        if len(self.colors) != 2 ** self.bits_per_symbol:
            raise ValueError(
                f"Color palette must have 2^{self.bits_per_symbol} = "
                f"{2 ** self.bits_per_symbol} colors, but has {len(self.colors)}"
            )


# Predefined color palettes
_COLOR_PALETTES = {
    # 1-bit (2 colors): Black and white
    "bw": ColorPalette(
        name="bw",
        colors=[(0, 0, 0), (255, 255, 255)],
        bits_per_symbol=1,
    ),
    
    # 2-bit (4 colors): Black, white, red, blue
    "4-color": ColorPalette(
        name="4-color",
        colors=[
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 0, 255),     # Blue
        ],
        bits_per_symbol=2,
    ),
    
    # 3-bit (8 colors): CMYK + RGB + Black + White
    "8-color": ColorPalette(
        name="8-color",
        colors=[
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 255, 0),     # Green
            (0, 0, 255),     # Blue
            (0, 255, 255),   # Cyan
            (255, 0, 255),   # Magenta
            (255, 255, 0),   # Yellow
        ],
        bits_per_symbol=3,
    ),
}


def get_color_palette(name: str) -> ColorPalette:
    """
    Get a predefined color palette by name.
    
    Args:
        name: Palette name ('bw', '4-color', '8-color')
    
    Returns:
        ColorPalette object
    """
    if name not in _COLOR_PALETTES:
        raise ValueError(
            f"Unknown color palette: {name}. "
            f"Available palettes: {list(_COLOR_PALETTES.keys())}"
        )
    
    return _COLOR_PALETTES[name]


def map_bits_to_colors(
    data: bytes,
    palette_name: str = "8-color",
) -> List[Tuple[int, int, int]]:
    """
    Map binary data to colors using the specified palette.
    
    Args:
        data: Binary data to encode
        palette_name: Color palette name
    
    Returns:
        List of RGB color tuples
    """
    # Get palette
    palette = get_color_palette(palette_name)
    
    # Calculate number of bits per symbol
    bits_per_symbol = palette.bits_per_symbol
    
    # Convert bytes to bits
    bits = []
    for byte in data:
        for i in range(7, -1, -1):  # MSB first
            bits.append((byte >> i) & 1)
    
    # Pad bits if necessary
    if len(bits) % bits_per_symbol != 0:
        padding = bits_per_symbol - (len(bits) % bits_per_symbol)
        bits.extend([0] * padding)
    
    # Group bits into symbols
    symbols = []
    for i in range(0, len(bits), bits_per_symbol):
        symbol_bits = bits[i:i + bits_per_symbol]
        symbol_value = 0
        for bit in symbol_bits:
            symbol_value = (symbol_value << 1) | bit
        symbols.append(symbol_value)
    
    # Map symbols to colors
    colors = [palette.colors[symbol] for symbol in symbols]
    
    logger.info(
        f"Mapped {len(data)} bytes to {len(colors)} colors "
        f"using {palette.name} palette"
    )
    
    return colors


def map_colors_to_bits(
    colors: List[Tuple[int, int, int]],
    palette_name: str = "8-color",
) -> bytes:
    """
    Map colors back to binary data.
    
    Args:
        colors: List of RGB color tuples
        palette_name: Color palette name
    
    Returns:
        Binary data
    """
    # Get palette
    palette = get_color_palette(palette_name)
    
    # Calculate number of bits per symbol
    bits_per_symbol = palette.bits_per_symbol
    
    # Map colors to symbols
    symbols = []
    for color in colors:
        # Find closest color in palette
        min_distance = float('inf')
        closest_symbol = 0
        
        for i, palette_color in enumerate(palette.colors):
            # Euclidean distance in RGB space
            distance = sum((a - b) ** 2 for a, b in zip(color, palette_color))
            
            if distance < min_distance:
                min_distance = distance
                closest_symbol = i
        
        symbols.append(closest_symbol)
    
    # Convert symbols to bits
    bits = []
    for symbol in symbols:
        for i in range(bits_per_symbol - 1, -1, -1):  # MSB first
            bits.append((symbol >> i) & 1)
    
    # Convert bits to bytes
    bytes_data = bytearray()
    for i in range(0, len(bits), 8):
        if i + 8 <= len(bits):
            byte = 0
            for j in range(8):
                byte = (byte << 1) | bits[i + j]
            bytes_data.append(byte)
    
    logger.info(
        f"Mapped {len(colors)} colors to {len(bytes_data)} bytes "
        f"using {palette.name} palette"
    )
    
    return bytes(bytes_data)


def test_symbol_mapping():
    """Test symbol mapping."""
    # Test data
    test_data = b"Hello, MegaCode!"
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Test with different palettes
    for palette_name in ["bw", "4-color", "8-color"]:
        logger.info(f"Testing {palette_name} palette")
        
        # Map bits to colors
        colors = map_bits_to_colors(test_data, palette_name=palette_name)
        
        # Map colors back to bits
        decoded = map_colors_to_bits(colors, palette_name=palette_name)
        
        # Verify data integrity
        if decoded == test_data:
            logger.info(f"Data integrity check passed for {palette_name}")
        else:
            logger.error(f"Data integrity check failed for {palette_name}")
            logger.error(f"Original: {test_data}")
            logger.error(f"Decoded: {decoded}")


def main():
    """Run all tests."""
    logger.info("Testing MegaCode symbol mapping module")
    
    test_symbol_mapping()
    
    logger.info("All tests completed")


if __name__ == "__main__":
    main()
