#!/usr/bin/env python3
"""
Integration test for MegaCode.

This test combines all the modules to create a complete encoding pipeline.
"""

import os
import logging
import random
import numpy as np
from typing import List, Tuple, Optional

# Import from test modules
from test_ecc import ECCLevel, RS<PERSON><PERSON>, apply_reed_solomon, decode_reed_solomon
from test_fountain import FountainPacket, generate_fountain_packets, decode_fountain_packets
from test_symbol import ColorPalette, get_color_palette, map_bits_to_colors, map_colors_to_bits
from test_layout import GridLayout, generate_grid, add_finder_patterns, add_calibration_patterns
from test_renderer import render_svg, render_png

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def compress_data(data: bytes) -> bytes:
    """
    Simple compression function for testing.
    
    Args:
        data: Input data
    
    Returns:
        Compressed data
    """
    # For testing, just add a header with the original size
    header = len(data).to_bytes(4, byteorder='big')
    return header + data


def decompress_data(data: bytes) -> bytes:
    """
    Simple decompression function for testing.
    
    Args:
        data: Compressed data
    
    Returns:
        Decompressed data
    """
    # Extract header
    original_size = int.from_bytes(data[:4], byteorder='big')
    # Return data without header
    return data[4:]


def encode_data(
    data: bytes,
    ecc_level: int = ECCLevel.M,
    palette_name: str = "8-color",
    output_file: Optional[str] = None,
) -> GridLayout:
    """
    Encode data into a MegaCode grid.
    
    Args:
        data: Input data
        ecc_level: Error correction level
        palette_name: Color palette name
        output_file: Output file path (if None, no file is created)
    
    Returns:
        GridLayout object
    """
    # Step 1: Compress data
    logger.info(f"Compressing {len(data)} bytes")
    compressed_data = compress_data(data)
    logger.info(f"Compressed to {len(compressed_data)} bytes")
    
    # Step 2: Apply Reed-Solomon error correction
    logger.info(f"Applying Reed-Solomon ECC (level {ecc_level})")
    rs_blocks = apply_reed_solomon(compressed_data, level=ecc_level, block_size=50, nsym=10)
    logger.info(f"Created {len(rs_blocks)} RS blocks")
    
    # Step 3: Generate fountain packets
    logger.info("Generating fountain packets")
    rs_data = [block.to_bytes() for block in rs_blocks]
    packets = generate_fountain_packets(rs_data, overhead=0.5)
    logger.info(f"Generated {len(packets)} fountain packets")
    
    # Step 4: Convert packets to bytes
    packet_data = [packet.to_bytes() for packet in packets]
    combined_data = b"".join(packet_data)
    logger.info(f"Combined packet data: {len(combined_data)} bytes")
    
    # Step 5: Map bits to colors
    logger.info(f"Mapping bits to colors using {palette_name} palette")
    colors = map_bits_to_colors(combined_data, palette_name=palette_name)
    logger.info(f"Mapped to {len(colors)} colors")
    
    # Step 6: Generate grid layout
    logger.info("Generating grid layout")
    layout = generate_grid(colors, finder_patterns=True, calibration_patterns=True)
    logger.info(f"Generated grid of size {layout.width}x{layout.height}")
    
    # Step 7: Render output if requested
    if output_file:
        if output_file.endswith(".svg"):
            render_svg(layout, module_size=5, output_file=output_file)
        elif output_file.endswith(".png"):
            render_png(layout, module_size=5, output_file=output_file)
        else:
            logger.warning(f"Unsupported output format: {output_file}")
    
    return layout


def decode_grid(
    layout: GridLayout,
    palette_name: str = "8-color",
) -> bytes:
    """
    Decode a MegaCode grid back to the original data.
    
    Args:
        layout: GridLayout object
        palette_name: Color palette name
    
    Returns:
        Decoded data
    """
    # Step 1: Extract colors from grid
    logger.info(f"Extracting colors from grid of size {layout.width}x{layout.height}")
    colors = []
    
    # Skip finder patterns and quiet zone
    start_x = 11  # FINDER_PATTERN_SIZE + QUIET_ZONE_SIZE
    start_y = 11
    end_x = layout.width - 11
    end_y = layout.height - 11
    
    for y in range(start_y, end_y):
        for x in range(start_x, end_x):
            colors.append(tuple(layout.grid[y, x]))
    
    logger.info(f"Extracted {len(colors)} colors")
    
    # Step 2: Map colors back to bits
    logger.info(f"Mapping colors to bits using {palette_name} palette")
    combined_data = map_colors_to_bits(colors, palette_name=palette_name)
    logger.info(f"Mapped to {len(combined_data)} bytes")
    
    # Step 3: Parse fountain packets
    logger.info("Parsing fountain packets")
    packets = []
    offset = 0
    
    while offset < len(combined_data):
        try:
            packet = FountainPacket.from_bytes(combined_data[offset:])
            packets.append(packet)
            # Move to next packet
            offset += len(packet.to_bytes())
        except Exception as e:
            logger.error(f"Error parsing packet at offset {offset}: {e}")
            # Skip to next potential packet
            offset += 1
    
    logger.info(f"Parsed {len(packets)} fountain packets")
    
    # Step 4: Decode fountain packets
    logger.info("Decoding fountain packets")
    rs_data = decode_fountain_packets(packets)
    
    if rs_data is None:
        logger.error("Failed to decode fountain packets")
        return b""
    
    logger.info(f"Decoded {len(rs_data)} bytes of RS data")
    
    # Step 5: Parse RS blocks
    logger.info("Parsing RS blocks")
    rs_blocks = []
    offset = 0
    
    while offset < len(rs_data):
        try:
            block = RSBlock.from_bytes(rs_data[offset:])
            rs_blocks.append(block)
            # Move to next block
            offset += len(block.to_bytes())
        except Exception as e:
            logger.error(f"Error parsing RS block at offset {offset}: {e}")
            # Skip to next potential block
            offset += 1
    
    logger.info(f"Parsed {len(rs_blocks)} RS blocks")
    
    # Step 6: Decode RS blocks
    logger.info("Decoding RS blocks")
    try:
        compressed_data = decode_reed_solomon(rs_blocks)
        logger.info(f"Decoded {len(compressed_data)} bytes of compressed data")
    except Exception as e:
        logger.error(f"Failed to decode RS blocks: {e}")
        return b""
    
    # Step 7: Decompress data
    logger.info("Decompressing data")
    try:
        data = decompress_data(compressed_data)
        logger.info(f"Decompressed to {len(data)} bytes")
    except Exception as e:
        logger.error(f"Failed to decompress data: {e}")
        return b""
    
    return data


def test_integration():
    """Test the complete encoding and decoding pipeline."""
    # Test data
    test_data = b"Hello, MegaCode! This is an integration test of the complete encoding and decoding pipeline."
    logger.info(f"Original data: {len(test_data)} bytes")
    
    # Encode data
    output_file = "integration_test.svg"
    layout = encode_data(
        test_data,
        ecc_level=ECCLevel.H,
        palette_name="8-color",
        output_file=output_file,
    )
    
    # Verify output file was created
    if os.path.exists(output_file):
        logger.info(f"Output file created: {os.path.getsize(output_file)} bytes")
    else:
        logger.error(f"Output file not created: {output_file}")
    
    # Decode grid
    decoded_data = decode_grid(layout, palette_name="8-color")
    
    # Verify data integrity
    if decoded_data == test_data:
        logger.info("Data integrity check passed")
    else:
        logger.error("Data integrity check failed")
        logger.error(f"Original: {test_data}")
        logger.error(f"Decoded: {decoded_data}")


def main():
    """Run all tests."""
    logger.info("Running MegaCode integration test")
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    test_integration()
    
    logger.info("Integration test completed")


if __name__ == "__main__":
    main()
