# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Submit a new Beta Build to Firebase App Distribution"
  lane :beta do
    gradle(
      task: "clean assembleRelease"
    )
    firebase_app_distribution(
      app: ENV["FIREBASE_ANDROID_APP_ID"],
      groups: "testers",
      release_notes: "Beta build for testing"
    )
  end

  desc "Deploy to staging"
  lane :deploy_staging do
    gradle(
      task: "clean bundleRelease"
    )
    upload_to_play_store(
      track: 'internal',
      release_status: 'draft',
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy to production"
  lane :deploy_production do
    gradle(
      task: "clean bundleRelease"
    )
    upload_to_play_store(
      track: 'production',
      release_status: 'completed',
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end
end

platform :ios do
  desc "Submit a new Beta Build to TestFlight"
  lane :beta do
    build_app(
      scheme: "Runner",
      workspace: "Runner.xcworkspace",
      export_method: "app-store"
    )
    upload_to_testflight(
      skip_waiting_for_build_processing: true
    )
  end

  desc "Deploy to staging"
  lane :deploy_staging do
    build_app(
      scheme: "Runner",
      workspace: "Runner.xcworkspace",
      export_method: "app-store"
    )
    upload_to_testflight(
      skip_waiting_for_build_processing: true
    )
  end

  desc "Deploy to production"
  lane :deploy_production do
    build_app(
      scheme: "Runner",
      workspace: "Runner.xcworkspace",
      export_method: "app-store"
    )
    upload_to_app_store(
      submit_for_review: true,
      automatic_release: true,
      force: true,
      skip_metadata: false,
      skip_screenshots: false,
      skip_binary_upload: false
    )
  end
end
