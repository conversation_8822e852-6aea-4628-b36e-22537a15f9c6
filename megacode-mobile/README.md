# MegaCode Mobile App

The MegaCode Mobile App is a Flutter application for scanning and decoding high-capacity 2D and 3D codes generated by the MegaCode Encoder.

## Features

- Real-time camera scanning
- AR overlay for guidance
- Color calibration and correction
- Decoding of MegaCode symbols
- History of scanned codes
- Integration with backend API

## Installation

```bash
flutter pub get
```

## Usage

### Running the App

```bash
flutter run
```

## Architecture

The app consists of several components:

- `screens`: UI screens for scanning, results, and settings
- `services`: Camera integration and API communication
- `utils`: Helper functions and utilities

## License

MIT
