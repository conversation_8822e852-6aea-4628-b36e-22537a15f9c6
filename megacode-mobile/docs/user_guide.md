# MegaCode Mobile User Guide

## Introduction

MegaCode Mobile is a powerful mobile application for scanning and decoding high-capacity 2D and 3D codes. This guide will help you get started with the application and explain its features in detail.

## Installation

### Android

1. Download the app from the [Google Play Store](https://play.google.com/store/apps/details?id=com.megacode.mobile).
2. Open the app and grant the required permissions (camera and storage).

### iOS

1. Download the app from the [App Store](https://apps.apple.com/app/megacode-scanner/id1234567890).
2. Open the app and grant the required permissions (camera and photo library).

## Getting Started

When you first launch MegaCode Mobile, you'll see the main scanning screen. The app has three main tabs:

1. **Scan**: The main screen for scanning MegaCode symbols.
2. **History**: View your scan history.
3. **Settings**: Configure app settings.

### Scanning a MegaCode Symbol

To scan a MegaCode symbol:

1. Navigate to the "Scan" tab.
2. Position the MegaCode symbol within the scanning frame.
3. The app will automatically detect and decode the symbol.
4. Once decoded, the app will display the result.

#### Auto-Scan Mode

The app features an Auto-Scan mode that continuously scans for MegaCode symbols:

1. Toggle the "Auto-Scan" switch in the top-right corner of the scan screen.
2. Hold your device steady and point it at the MegaCode symbol.
3. The app will automatically scan and decode the symbol when detected.

#### Manual Scanning

If you prefer manual control:

1. Ensure Auto-Scan is turned off.
2. Position the MegaCode symbol within the scanning frame.
3. Tap the scan button (large circular button) to capture and decode the image.

#### Scanning from Gallery

You can also scan MegaCode symbols from existing images:

1. Tap "Choose from Gallery" at the bottom of the scan screen.
2. Select an image containing a MegaCode symbol.
3. The app will process the image and display the result if a valid symbol is found.

### Viewing Scan Results

After successfully scanning a MegaCode symbol, the app will display the decoded content:

- **Text**: Plain text will be displayed in a scrollable view.
- **URL**: URLs will be displayed with an option to open them in a browser.
- **JSON**: JSON data will be formatted and displayed in a structured view.

You can:

- **Copy** the content to the clipboard.
- **Share** the content with other apps.
- **Save** the scanned image to your device.

### Managing Scan History

The "History" tab shows a list of your previous scans:

1. Tap on any item to view the full details.
2. Swipe left on an item to delete it.
3. Tap the filter icon to filter by content type.
4. Tap the search icon to search within your scan history.

## Advanced Features

### Settings

The "Settings" tab allows you to configure various aspects of the app:

#### Scanning Options

- **Default Palette**: Choose the color palette for decoding (Auto, 2-color, 4-color, 8-color, or 16-color).
- **Auto-Scan**: Enable or disable auto-scan mode by default.
- **Camera Flash**: Set the default flash mode (Off, On, or Auto).
- **Save History**: Enable or disable saving scan history.

#### Appearance

- **Theme**: Choose between Light, Dark, or System (follows system theme).
- **Scan Frame Style**: Customize the appearance of the scanning frame.

#### Advanced

- **Debug Mode**: Enable or disable debug mode for troubleshooting.
- **API URL**: Configure the URL for the MegaCode API (if using cloud-based decoding).

### Calibration Markers

The scanning screen includes calibration markers that help the app accurately detect colors in different lighting conditions. For best results:

1. Ensure the calibration markers in the top-right corner of the scanning frame are visible.
2. Avoid extreme lighting conditions (very bright or very dark).
3. Hold the device steady to prevent motion blur.

## Troubleshooting

### Common Issues

- **Scanning fails**: Ensure the MegaCode symbol is well-lit and positioned within the scanning frame. Try adjusting the distance or angle.
- **Blurry scans**: Hold the device steady or place it on a stable surface during scanning.
- **Color detection issues**: Check that the calibration markers are visible and not obscured. Try different lighting conditions.

### Getting Help

If you encounter any issues not covered in this guide:

1. Check the in-app help section under Settings > Help.
2. Visit our [support website](https://megacode.example.com/support).
3. Contact <NAME_EMAIL>.

## Privacy

MegaCode Mobile respects your privacy:

- All scanning and decoding is performed locally on your device by default.
- Scan history is stored only on your device and can be cleared at any time.
- If cloud-based decoding is enabled, data is transmitted securely and not stored on our servers.

For more information, see our [Privacy Policy](https://megacode.example.com/privacy).

## License

MegaCode Mobile is licensed under the MIT License. See the LICENSE file for details.
