import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';

// Storage keys
const HISTORY_KEY = '@MegaCode:history';
const SETTINGS_KEY = '@MegaCode:settings';

// Default settings
const DEFAULT_SETTINGS = {
  theme: 'light',
  saveHistory: true,
  autoOpenUrls: false,
  defaultPalette: 'auto',
  cameraFlashMode: 'off',
  maxHistoryItems: 50,
};

/**
 * Save scan to history
 * @param {Object} scan - Scan object
 * @returns {Promise<void>}
 */
export async function saveToHistory(scan) {
  try {
    // Get settings
    const settings = await getSettings();
    
    // Check if history is enabled
    if (!settings.saveHistory) {
      return;
    }
    
    // Get current history
    const history = await getHistory();
    
    // Add scan to history
    const updatedHistory = [scan, ...history];
    
    // Limit history size
    if (updatedHistory.length > settings.maxHistoryItems) {
      updatedHistory.splice(settings.maxHistoryItems);
    }
    
    // Save history
    await AsyncStorage.setItem(HISTORY_KEY, JSON.stringify(updatedHistory));
    
    // Save image to app directory
    if (scan.imageUri) {
      const fileName = scan.id + '.jpg';
      const destinationUri = `${FileSystem.documentDirectory}images/${fileName}`;
      
      // Create directory if it doesn't exist
      await FileSystem.makeDirectoryAsync(`${FileSystem.documentDirectory}images`, {
        intermediates: true,
      });
      
      // Copy image
      await FileSystem.copyAsync({
        from: scan.imageUri,
        to: destinationUri,
      });
      
      // Update image URI in history
      scan.imageUri = destinationUri;
      updatedHistory[0] = scan;
      
      // Save updated history
      await AsyncStorage.setItem(HISTORY_KEY, JSON.stringify(updatedHistory));
    }
  } catch (error) {
    console.error('Error saving to history:', error);
    throw error;
  }
}

/**
 * Get scan history
 * @returns {Promise<Array>} - Scan history
 */
export async function getHistory() {
  try {
    const historyJson = await AsyncStorage.getItem(HISTORY_KEY);
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting history:', error);
    return [];
  }
}

/**
 * Clear scan history
 * @returns {Promise<void>}
 */
export async function clearHistory() {
  try {
    // Remove history from storage
    await AsyncStorage.removeItem(HISTORY_KEY);
    
    // Remove image directory
    try {
      await FileSystem.deleteAsync(`${FileSystem.documentDirectory}images`, {
        idempotent: true,
      });
    } catch (e) {
      // Directory might not exist
    }
  } catch (error) {
    console.error('Error clearing history:', error);
    throw error;
  }
}

/**
 * Delete scan from history
 * @param {string} scanId - Scan ID
 * @returns {Promise<void>}
 */
export async function deleteScan(scanId) {
  try {
    // Get current history
    const history = await getHistory();
    
    // Find scan
    const scanIndex = history.findIndex(scan => scan.id === scanId);
    
    if (scanIndex === -1) {
      return;
    }
    
    // Get scan
    const scan = history[scanIndex];
    
    // Remove scan from history
    history.splice(scanIndex, 1);
    
    // Save updated history
    await AsyncStorage.setItem(HISTORY_KEY, JSON.stringify(history));
    
    // Remove image if it exists
    if (scan.imageUri && scan.imageUri.startsWith(FileSystem.documentDirectory)) {
      try {
        await FileSystem.deleteAsync(scan.imageUri, { idempotent: true });
      } catch (e) {
        // File might not exist
      }
    }
  } catch (error) {
    console.error('Error deleting scan:', error);
    throw error;
  }
}

/**
 * Get app settings
 * @returns {Promise<Object>} - App settings
 */
export async function getSettings() {
  try {
    const settingsJson = await AsyncStorage.getItem(SETTINGS_KEY);
    const settings = settingsJson ? JSON.parse(settingsJson) : {};
    
    // Merge with default settings
    return { ...DEFAULT_SETTINGS, ...settings };
  } catch (error) {
    console.error('Error getting settings:', error);
    return { ...DEFAULT_SETTINGS };
  }
}

/**
 * Save app settings
 * @param {Object} settings - App settings
 * @returns {Promise<void>}
 */
export async function saveSettings(settings) {
  try {
    // Get current settings
    const currentSettings = await getSettings();
    
    // Merge settings
    const updatedSettings = { ...currentSettings, ...settings };
    
    // Save settings
    await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(updatedSettings));
  } catch (error) {
    console.error('Error saving settings:', error);
    throw error;
  }
}

/**
 * Reset app settings to defaults
 * @returns {Promise<void>}
 */
export async function resetSettings() {
  try {
    await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(DEFAULT_SETTINGS));
  } catch (error) {
    console.error('Error resetting settings:', error);
    throw error;
  }
}
