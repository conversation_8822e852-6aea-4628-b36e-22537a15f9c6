import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

// API configuration
const API_CONFIG = {
  // Use environment variables or Constants for API URL
  baseUrl: Constants.expoConfig?.extra?.apiUrl || 'https://api.megacode.example.com',
  timeout: 30000, // 30 seconds
};

/**
 * Make API request with fetch
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - API response
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_CONFIG.baseUrl}${endpoint}`;
  
  // Set default options
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    timeout: API_CONFIG.timeout,
  };
  
  // Merge options
  const fetchOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };
  
  try {
    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);
    fetchOptions.signal = controller.signal;
    
    // Make request
    const response = await fetch(url, fetchOptions);
    clearTimeout(timeoutId);
    
    // Parse response
    const contentType = response.headers.get('content-type');
    let data;
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    // Check if response is successful
    if (!response.ok) {
      throw new Error(data.error || `API request failed with status ${response.status}`);
    }
    
    return data;
  } catch (error) {
    // Handle network errors
    if (error.name === 'AbortError') {
      throw new Error('Request timed out');
    }
    
    throw error;
  }
}

/**
 * Upload file to API
 * @param {string} endpoint - API endpoint
 * @param {string} fileUri - File URI
 * @param {Object} formData - Additional form data
 * @param {Function} progressCallback - Progress callback
 * @returns {Promise<Object>} - API response
 */
async function uploadFile(endpoint, fileUri, formData = {}, progressCallback = null) {
  const url = `${API_CONFIG.baseUrl}${endpoint}`;
  
  // Create form data
  const form = new FormData();
  
  // Add file
  const fileInfo = await FileSystem.getInfoAsync(fileUri);
  
  if (!fileInfo.exists) {
    throw new Error('File does not exist');
  }
  
  // Get file name and type
  const fileName = fileUri.split('/').pop();
  const fileType = getFileType(fileName);
  
  form.append('file', {
    uri: Platform.OS === 'android' ? fileUri : fileUri.replace('file://', ''),
    name: fileName,
    type: fileType,
  });
  
  // Add additional form data
  Object.keys(formData).forEach(key => {
    form.append(key, formData[key]);
  });
  
  try {
    // Upload file
    const response = await FileSystem.uploadAsync(url, fileUri, {
      httpMethod: 'POST',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      fieldName: 'file',
      parameters: formData,
      headers: {
        'Accept': 'application/json',
      },
      sessionType: FileSystem.FileSystemSessionType.BACKGROUND,
      uploadProgress: progressCallback,
    });
    
    // Parse response
    let data;
    
    try {
      data = JSON.parse(response.body);
    } catch (error) {
      data = response.body;
    }
    
    // Check if response is successful
    if (response.status < 200 || response.status >= 300) {
      throw new Error(data.error || `API request failed with status ${response.status}`);
    }
    
    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Get file type from file name
 * @param {string} fileName - File name
 * @returns {string} - File type
 */
function getFileType(fileName) {
  const extension = fileName.split('.').pop().toLowerCase();
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'pdf':
      return 'application/pdf';
    case 'json':
      return 'application/json';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Decode image
 * @param {string} imageUri - Image URI
 * @param {Object} options - Decoding options
 * @returns {Promise<Object>} - Decoding result
 */
export async function decodeImage(imageUri, options = {}) {
  try {
    // Upload image for decoding
    const response = await uploadFile('/api/decode', imageUri, {
      format: options.format || 'auto',
      palette: options.palette || 'auto',
      debug: options.debug || false,
    });
    
    // Check if job was created
    if (response.success && response.jobId) {
      // Poll for job completion
      return await pollJobStatus('/api/decode', response.jobId);
    }
    
    return response;
  } catch (error) {
    console.error('Error decoding image:', error);
    return {
      success: false,
      error: error.message || 'Failed to decode image',
    };
  }
}

/**
 * Poll job status
 * @param {string} endpoint - API endpoint
 * @param {string} jobId - Job ID
 * @param {number} maxAttempts - Maximum number of polling attempts
 * @param {number} interval - Polling interval in milliseconds
 * @returns {Promise<Object>} - Job result
 */
async function pollJobStatus(endpoint, jobId, maxAttempts = 30, interval = 1000) {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      // Get job status
      const response = await apiRequest(`${endpoint}/${jobId}`);
      
      // Check if job is completed
      if (response.job.status === 'completed') {
        return {
          success: true,
          jobId,
          data: response.job.result.content || null,
          contentType: response.job.result.contentType || null,
          downloadUrl: response.job.result.downloadUrl || null,
        };
      }
      
      // Check if job failed
      if (response.job.status === 'failed') {
        return {
          success: false,
          jobId,
          error: response.job.error || 'Job failed',
        };
      }
      
      // Wait for next attempt
      await new Promise(resolve => setTimeout(resolve, interval));
      attempts++;
    } catch (error) {
      console.error('Error polling job status:', error);
      
      // Wait for next attempt
      await new Promise(resolve => setTimeout(resolve, interval));
      attempts++;
    }
  }
  
  // Max attempts reached
  return {
    success: false,
    jobId,
    error: 'Timed out waiting for job completion',
  };
}
