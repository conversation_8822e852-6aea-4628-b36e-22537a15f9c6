import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Share,
  Linking,
  Alert,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as WebBrowser from 'expo-web-browser';

export default function ResultScreen({ route, navigation }) {
  const { result, contentType, imageUri } = route.params;
  const [isLoading, setIsLoading] = useState(false);
  const [isUrl, setIsUrl] = useState(false);
  const [isJson, setIsJson] = useState(false);
  const [parsedJson, setParsedJson] = useState(null);

  useEffect(() => {
    // Check if result is a URL
    if (contentType === 'text/url' || (typeof result === 'string' && isValidUrl(result))) {
      setIsUrl(true);
    }

    // Check if result is JSON
    if (contentType === 'application/json' || (typeof result === 'string' && isValidJson(result))) {
      setIsJson(true);
      try {
        setParsedJson(JSON.parse(result));
      } catch (error) {
        console.error('Error parsing JSON:', error);
      }
    }
  }, [result, contentType]);

  const isValidUrl = (text) => {
    try {
      new URL(text);
      return true;
    } catch (e) {
      return false;
    }
  };

  const isValidJson = (text) => {
    try {
      JSON.parse(text);
      return true;
    } catch (e) {
      return false;
    }
  };

  const copyToClipboard = async () => {
    try {
      await Clipboard.setStringAsync(result);
      Alert.alert('Copied', 'Content copied to clipboard');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      Alert.alert('Error', 'Failed to copy to clipboard');
    }
  };

  const shareResult = async () => {
    try {
      const shareOptions = {
        message: result,
      };

      // If it's a URL, add it as a URL
      if (isUrl) {
        shareOptions.url = result;
      }

      await Share.share(shareOptions);
    } catch (error) {
      console.error('Error sharing result:', error);
      Alert.alert('Error', 'Failed to share result');
    }
  };

  const openUrl = async () => {
    if (isUrl) {
      try {
        await WebBrowser.openBrowserAsync(result);
      } catch (error) {
        console.error('Error opening URL:', error);
        Alert.alert('Error', 'Failed to open URL');
      }
    }
  };

  const saveImage = async () => {
    if (!imageUri) {
      Alert.alert('Error', 'No image to save');
      return;
    }

    try {
      setIsLoading(true);
      
      // Request permission if not already granted
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Media library access is required to save images');
        setIsLoading(false);
        return;
      }
      
      // Save image to media library
      const asset = await MediaLibrary.createAssetAsync(imageUri);
      await MediaLibrary.createAlbumAsync('MegaCode', asset, false);
      
      Alert.alert('Success', 'Image saved to gallery');
    } catch (error) {
      console.error('Error saving image:', error);
      Alert.alert('Error', 'Failed to save image');
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    if (isJson && parsedJson) {
      // Render JSON content
      return (
        <View style={styles.jsonContainer}>
          <Text style={styles.jsonTitle}>JSON Content:</Text>
          <ScrollView style={styles.jsonScroll}>
            <Text style={styles.jsonText}>
              {JSON.stringify(parsedJson, null, 2)}
            </Text>
          </ScrollView>
        </View>
      );
    } else if (isUrl) {
      // Render URL content
      return (
        <View style={styles.urlContainer}>
          <Text style={styles.urlTitle}>URL Detected:</Text>
          <Text style={styles.urlText}>{result}</Text>
          <TouchableOpacity style={styles.urlButton} onPress={openUrl}>
            <Ionicons name="open-outline" size={20} color="#fff" />
            <Text style={styles.urlButtonText}>Open URL</Text>
          </TouchableOpacity>
        </View>
      );
    } else {
      // Render text content
      return (
        <ScrollView style={styles.textContainer}>
          <Text style={styles.resultText}>{result}</Text>
        </ScrollView>
      );
    }
  };

  return (
    <View style={styles.container}>
      {imageUri && (
        <View style={styles.imageContainer}>
          <Image source={{ uri: imageUri }} style={styles.image} resizeMode="contain" />
        </View>
      )}
      
      <View style={styles.contentContainer}>
        <View style={styles.contentTypeContainer}>
          <Ionicons
            name={
              isUrl ? 'link' : 
              isJson ? 'code' : 
              'document-text'
            }
            size={24}
            color="#007AFF"
          />
          <Text style={styles.contentTypeText}>
            {isUrl ? 'URL' : 
             isJson ? 'JSON' : 
             contentType === 'text/plain' ? 'Text' : 
             'Data'}
          </Text>
        </View>
        
        {renderContent()}
      </View>
      
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={copyToClipboard}>
          <Ionicons name="copy-outline" size={24} color="#007AFF" />
          <Text style={styles.actionText}>Copy</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={shareResult}>
          <Ionicons name="share-outline" size={24} color="#007AFF" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={saveImage}>
          {isLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <>
              <Ionicons name="save-outline" size={24} color="#007AFF" />
              <Text style={styles.actionText}>Save Image</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  imageContainer: {
    height: 200,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  contentContainer: {
    flex: 1,
    padding: 15,
  },
  contentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  contentTypeText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#007AFF',
  },
  textContainer: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
  },
  resultText: {
    fontSize: 16,
    color: '#333',
  },
  urlContainer: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
  },
  urlTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  urlText: {
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 15,
  },
  urlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 8,
    justifyContent: 'center',
  },
  urlButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 'bold',
  },
  jsonContainer: {
    flex: 1,
  },
  jsonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  jsonScroll: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
  },
  jsonText: {
    fontSize: 14,
    color: '#333',
    fontFamily: 'monospace',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  actionButton: {
    alignItems: 'center',
    padding: 10,
  },
  actionText: {
    marginTop: 5,
    color: '#007AFF',
  },
});
