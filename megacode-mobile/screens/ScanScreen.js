import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
  Image,
  Animated,
  Easing,
  Platform,
  Switch,
} from 'react-native';
import { Camera } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { useIsFocused } from '@react-navigation/native';
import Svg, { Rect, Path, Circle } from 'react-native-svg';

// Import API service
import { decodeImage } from '../services/api';

// Import storage service
import { saveToHistory, getSettings } from '../services/storage';

const { width, height } = Dimensions.get('window');
const PREVIEW_SIZE = width * 0.8;

export default function ScanScreen({ navigation }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [isScanning, setIsScanning] = useState(false);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [capturedImage, setCapturedImage] = useState(null);
  const [autoScan, setAutoScan] = useState(false);
  const [scanSettings, setScanSettings] = useState({
    palette: 'auto',
    debug: false,
    saveHistory: true,
  });
  const [scanAttempts, setScanAttempts] = useState(0);

  // Animation values
  const scanLineAnim = useRef(new Animated.Value(0)).current;
  const scanCornerAnim = useRef(new Animated.Value(0)).current;

  const cameraRef = useRef(null);
  const isFocused = useIsFocused();
  const scanTimerRef = useRef(null);

  useEffect(() => {
    (async () => {
      // Request camera permission
      const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();

      // Request media library permission
      const { status: mediaStatus } = await MediaLibrary.requestPermissionsAsync();

      setHasPermission(cameraStatus === 'granted' && mediaStatus === 'granted');

      if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Camera and media library access are required to use this app.',
          [{ text: 'OK' }]
        );
      }

      // Load settings
      const settings = await getSettings();
      if (settings) {
        setScanSettings({
          palette: settings.defaultPalette || 'auto',
          debug: settings.debugMode || false,
          saveHistory: settings.saveHistory !== false,
        });
        setAutoScan(settings.autoScan || false);
      }
    })();
  }, []);

  // Reset captured image when screen is focused
  useEffect(() => {
    if (isFocused) {
      setCapturedImage(null);

      // Start scan animation if screen is focused
      startScanAnimation();

      // Start auto-scan timer if enabled
      if (autoScan) {
        startAutoScanTimer();
      }
    } else {
      // Clear auto-scan timer when screen loses focus
      if (scanTimerRef.current) {
        clearInterval(scanTimerRef.current);
        scanTimerRef.current = null;
      }
    }

    return () => {
      // Clean up timer on unmount
      if (scanTimerRef.current) {
        clearInterval(scanTimerRef.current);
        scanTimerRef.current = null;
      }
    };
  }, [isFocused, autoScan]);

  // Animation functions
  const startScanAnimation = () => {
    // Reset animation values
    scanLineAnim.setValue(0);
    scanCornerAnim.setValue(0);

    // Start scan line animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(scanLineAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(scanLineAnim, {
          toValue: 0,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Start corner animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(scanCornerAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(scanCornerAnim, {
          toValue: 0,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startAutoScanTimer = () => {
    // Clear existing timer
    if (scanTimerRef.current) {
      clearInterval(scanTimerRef.current);
    }

    // Set new timer to take picture every 3 seconds
    scanTimerRef.current = setInterval(() => {
      if (!isScanning && cameraRef.current && isFocused) {
        takePicture();
      }
    }, 3000);
  };

  const takePicture = async () => {
    if (cameraRef.current && !isScanning) {
      try {
        setIsScanning(true);

        // Take picture
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          skipProcessing: false,
        });

        // Set captured image
        setCapturedImage(photo);

        // Process image
        await processImage(photo.uri);
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture. Please try again.');
        setIsScanning(false);
      }
    }
  };

  const pickImage = async () => {
    if (!isScanning) {
      try {
        setIsScanning(true);

        // Launch image picker
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: false,
          quality: 0.8,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          // Set captured image
          setCapturedImage(result.assets[0]);

          // Process image
          await processImage(result.assets[0].uri);
        } else {
          setIsScanning(false);
        }
      } catch (error) {
        console.error('Error picking image:', error);
        Alert.alert('Error', 'Failed to pick image. Please try again.');
        setIsScanning(false);
      }
    }
  };

  const processImage = async (imageUri) => {
    try {
      // Increment scan attempts
      setScanAttempts(prev => prev + 1);

      // Decode image with settings
      const result = await decodeImage(imageUri, {
        palette: scanSettings.palette,
        debug: scanSettings.debug,
      });

      if (result.success) {
        // Save to history if enabled
        if (scanSettings.saveHistory) {
          await saveToHistory({
            id: result.jobId || Date.now().toString(),
            timestamp: new Date().toISOString(),
            imageUri,
            result: result.data,
            type: result.contentType || 'unknown',
          });
        }

        // Reset scan attempts
        setScanAttempts(0);

        // Navigate to result screen
        navigation.navigate('Result', {
          result: result.data,
          contentType: result.contentType,
          imageUri,
          metadata: result.metadata || {},
        });
      } else {
        // If auto-scan is enabled, only show alert after multiple failures
        if (!autoScan || scanAttempts >= 3) {
          Alert.alert(
            'Decoding Failed',
            result.error || 'Failed to decode MegaCode. Please try again with a clearer image.',
            [{ text: 'OK' }],
            { cancelable: true }
          );

          // Reset scan attempts
          setScanAttempts(0);
        }
      }
    } catch (error) {
      console.error('Error processing image:', error);

      // Only show alert if not in auto-scan mode or after multiple failures
      if (!autoScan || scanAttempts >= 3) {
        Alert.alert('Error', 'Failed to process image. Please try again.');
        setScanAttempts(0);
      }
    } finally {
      setIsScanning(false);
    }
  };

  const toggleCameraType = () => {
    setCameraType(
      cameraType === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  const toggleFlashMode = () => {
    setFlashMode(
      flashMode === Camera.Constants.FlashMode.off
        ? Camera.Constants.FlashMode.torch
        : Camera.Constants.FlashMode.off
    );
  };

  if (hasPermission === null) {
    return <View style={styles.container}><ActivityIndicator size="large" color="#007AFF" /></View>;
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Camera permission not granted</Text>
        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate('Settings')}
        >
          <Text style={styles.buttonText}>Go to Settings</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Toggle auto-scan mode
  const toggleAutoScan = () => {
    const newValue = !autoScan;
    setAutoScan(newValue);

    if (newValue) {
      startAutoScanTimer();
    } else if (scanTimerRef.current) {
      clearInterval(scanTimerRef.current);
      scanTimerRef.current = null;
    }
  };

  // Render AR overlay with corner indicators and scan line
  const renderAROverlay = () => {
    const cornerSize = 20;
    const cornerThickness = 3;
    const cornerColor = scanCornerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(0, 122, 255, 0.5)', 'rgba(0, 122, 255, 1)']
    });

    const scanLineTranslateY = scanLineAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, PREVIEW_SIZE]
    });

    return (
      <View style={styles.arOverlay}>
        {/* Scan frame */}
        <View style={styles.scanFrame}>
          {/* Top-left corner */}
          <Animated.View style={[styles.corner, styles.topLeft, { borderColor: cornerColor }]}>
            <View style={[styles.cornerHorizontal, { width: cornerSize, height: cornerThickness }]} />
            <View style={[styles.cornerVertical, { width: cornerThickness, height: cornerSize }]} />
          </Animated.View>

          {/* Top-right corner */}
          <Animated.View style={[styles.corner, styles.topRight, { borderColor: cornerColor }]}>
            <View style={[styles.cornerHorizontal, { width: cornerSize, height: cornerThickness }]} />
            <View style={[styles.cornerVertical, { width: cornerThickness, height: cornerSize }]} />
          </Animated.View>

          {/* Bottom-left corner */}
          <Animated.View style={[styles.corner, styles.bottomLeft, { borderColor: cornerColor }]}>
            <View style={[styles.cornerHorizontal, { width: cornerSize, height: cornerThickness }]} />
            <View style={[styles.cornerVertical, { width: cornerThickness, height: cornerSize }]} />
          </Animated.View>

          {/* Bottom-right corner */}
          <Animated.View style={[styles.corner, styles.bottomRight, { borderColor: cornerColor }]}>
            <View style={[styles.cornerHorizontal, { width: cornerSize, height: cornerThickness }]} />
            <View style={[styles.cornerVertical, { width: cornerThickness, height: cornerSize }]} />
          </Animated.View>

          {/* Scan line */}
          <Animated.View
            style={[
              styles.scanLine,
              { transform: [{ translateY: scanLineTranslateY }] }
            ]}
          />
        </View>

        {/* Calibration markers */}
        <View style={styles.calibrationMarkers}>
          <View style={styles.calibrationMarker}>
            <View style={[styles.colorMarker, { backgroundColor: '#000000' }]} />
            <View style={[styles.colorMarker, { backgroundColor: '#FFFFFF' }]} />
          </View>
          <View style={styles.calibrationMarker}>
            <View style={[styles.colorMarker, { backgroundColor: '#FF0000' }]} />
            <View style={[styles.colorMarker, { backgroundColor: '#00FF00' }]} />
            <View style={[styles.colorMarker, { backgroundColor: '#0000FF' }]} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scan MegaCode</Text>
        <View style={styles.autoScanContainer}>
          <Text style={styles.autoScanText}>Auto-Scan</Text>
          <Switch
            value={autoScan}
            onValueChange={toggleAutoScan}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={autoScan ? '#007AFF' : '#f4f3f4'}
            ios_backgroundColor="#3e3e3e"
          />
        </View>
      </View>

      <View style={styles.cameraContainer}>
        {isFocused && !capturedImage ? (
          <Camera
            ref={cameraRef}
            style={styles.camera}
            type={cameraType}
            flashMode={flashMode}
            ratio="16:9"
            autoFocus={Camera.Constants.AutoFocus.on}
          >
            {renderAROverlay()}
          </Camera>
        ) : capturedImage ? (
          <Image
            source={{ uri: capturedImage.uri }}
            style={styles.camera}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.camera, { backgroundColor: '#000' }]} />
        )}
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={toggleCameraType}
          disabled={isScanning}
        >
          <Ionicons
            name="camera-reverse-outline"
            size={28}
            color={isScanning ? '#999' : '#fff'}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.captureButton}
          onPress={takePicture}
          disabled={isScanning}
        >
          {isScanning ? (
            <ActivityIndicator size="large" color="#fff" />
          ) : (
            <Ionicons name="scan" size={36} color="#fff" />
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={toggleFlashMode}
          disabled={isScanning}
        >
          <Ionicons
            name={flashMode === Camera.Constants.FlashMode.torch ? "flash" : "flash-off"}
            size={28}
            color={isScanning ? '#999' : '#fff'}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.bottomControls}>
        <TouchableOpacity
          style={styles.galleryButton}
          onPress={pickImage}
          disabled={isScanning}
        >
          <Ionicons
            name="images-outline"
            size={24}
            color={isScanning ? '#999' : '#007AFF'}
          />
          <Text style={[styles.galleryText, isScanning && { color: '#999' }]}>
            Choose from Gallery
          </Text>
        </TouchableOpacity>

        <Text style={styles.instructionText}>
          {autoScan
            ? "Auto-scanning enabled. Hold steady..."
            : "Position the MegaCode within the frame and tap to scan"}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 10,
    backgroundColor: '#000',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  autoScanContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  autoScanText: {
    color: '#fff',
    marginRight: 8,
    fontSize: 14,
  },
  cameraContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  arOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: PREVIEW_SIZE,
    height: PREVIEW_SIZE,
    position: 'relative',
    backgroundColor: 'transparent',
  },
  corner: {
    position: 'absolute',
    width: 20,
    height: 20,
  },
  topLeft: {
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    transform: [{ rotate: '90deg' }],
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    transform: [{ rotate: '-90deg' }],
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    transform: [{ rotate: '180deg' }],
  },
  cornerHorizontal: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: '#007AFF',
  },
  cornerVertical: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: '#007AFF',
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    width: '100%',
    height: 2,
    backgroundColor: 'rgba(0, 122, 255, 0.5)',
  },
  calibrationMarkers: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'column',
  },
  calibrationMarker: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  colorMarker: {
    width: 15,
    height: 15,
    marginRight: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: '#000',
  },
  controlButton: {
    padding: 15,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    backgroundColor: '#000',
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  galleryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    backgroundColor: '#000',
  },
  galleryText: {
    color: '#007AFF',
    marginLeft: 8,
    fontSize: 16,
  },
  instructionText: {
    color: '#fff',
    textAlign: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
    fontSize: 14,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
});
