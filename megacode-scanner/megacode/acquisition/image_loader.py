"""
Image acquisition module for MegaCode scanner.

This module provides functions for loading and preprocessing images.
"""

import os
import logging
import numpy as np
from typing import Union, Tuple, Optional, List, Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

# Try to import OpenCV and PIL
try:
    import cv2
    HAVE_CV2 = True
except ImportError:
    logger.warning("OpenCV not available, some features will be limited")
    HAVE_CV2 = False

try:
    from PIL import Image
    HAVE_PIL = True
except ImportError:
    logger.warning("PIL not available, some features will be limited")
    HAVE_PIL = False


class ImageLoader:
    """Image loader for MegaCode scanner."""
    
    def __init__(self):
        """Initialize the image loader."""
        self.supported_formats = []
        
        if HAVE_CV2:
            self.supported_formats.extend([".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"])
        
        if HAVE_PIL:
            self.supported_formats.extend([".webp", ".gif"])
        
        # Remove duplicates
        self.supported_formats = list(set(self.supported_formats))
        
        logger.info(f"Supported image formats: {', '.join(self.supported_formats)}")
    
    def load_image(
        self,
        image_path: str,
        grayscale: bool = False,
        resize: Optional[Tuple[int, int]] = None,
    ) -> np.ndarray:
        """
        Load an image from a file.
        
        Args:
            image_path: Path to the image file
            grayscale: Whether to convert the image to grayscale
            resize: Optional tuple of (width, height) to resize the image
            
        Returns:
            Image as a NumPy array (H, W, C) with RGB channels
            
        Raises:
            ValueError: If the image format is not supported
            IOError: If the image cannot be loaded
        """
        # Check if file exists
        if not os.path.isfile(image_path):
            raise IOError(f"Image file not found: {image_path}")
        
        # Check if format is supported
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(
                f"Unsupported image format: {file_ext}. "
                f"Supported formats: {', '.join(self.supported_formats)}"
            )
        
        # Load image
        try:
            if HAVE_CV2:
                # Load with OpenCV
                if grayscale:
                    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
                    # Convert to 3-channel grayscale
                    img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
                else:
                    img = cv2.imread(image_path, cv2.IMREAD_COLOR)
                    # Convert BGR to RGB
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                
                if img is None:
                    raise IOError(f"Failed to load image: {image_path}")
                
                # Resize if requested
                if resize is not None:
                    img = cv2.resize(img, resize)
                
                return img
            
            elif HAVE_PIL:
                # Load with PIL
                img = Image.open(image_path)
                
                if grayscale:
                    img = img.convert("L")
                    # Convert to 3-channel grayscale
                    img = np.array(img)
                    img = np.stack([img, img, img], axis=2)
                else:
                    img = img.convert("RGB")
                    img = np.array(img)
                
                # Resize if requested
                if resize is not None:
                    img_pil = Image.fromarray(img)
                    img_pil = img_pil.resize(resize)
                    img = np.array(img_pil)
                
                return img
            
            else:
                raise ImportError("Neither OpenCV nor PIL is available")
        
        except Exception as e:
            logger.error(f"Failed to load image {image_path}: {e}")
            raise IOError(f"Failed to load image {image_path}: {e}") from e
    
    def preprocess_image(
        self,
        image: np.ndarray,
        denoise: bool = True,
        equalize: bool = True,
        sharpen: bool = True,
    ) -> np.ndarray:
        """
        Preprocess an image for better code detection.
        
        Args:
            image: Input image as a NumPy array
            denoise: Whether to apply denoising
            equalize: Whether to apply histogram equalization
            sharpen: Whether to apply sharpening
            
        Returns:
            Preprocessed image as a NumPy array
        """
        if not HAVE_CV2:
            logger.warning("OpenCV not available, skipping preprocessing")
            return image
        
        # Make a copy to avoid modifying the original
        img = image.copy()
        
        # Convert to grayscale if needed
        if len(img.shape) == 3 and img.shape[2] == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        else:
            gray = img
        
        # Apply denoising
        if denoise:
            gray = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply histogram equalization
        if equalize:
            gray = cv2.equalizeHist(gray)
        
        # Apply sharpening
        if sharpen:
            kernel = np.array([[-1, -1, -1],
                              [-1, 9, -1],
                              [-1, -1, -1]])
            gray = cv2.filter2D(gray, -1, kernel)
        
        return gray
    
    def capture_from_camera(
        self,
        camera_id: int = 0,
        resolution: Tuple[int, int] = (1280, 720),
        num_frames: int = 1,
    ) -> np.ndarray:
        """
        Capture an image from a camera.
        
        Args:
            camera_id: Camera ID
            resolution: Desired resolution (width, height)
            num_frames: Number of frames to capture and average
            
        Returns:
            Captured image as a NumPy array
            
        Raises:
            RuntimeError: If camera capture fails
        """
        if not HAVE_CV2:
            raise RuntimeError("OpenCV not available, camera capture not supported")
        
        try:
            # Open camera
            cap = cv2.VideoCapture(camera_id)
            
            # Set resolution
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, resolution[0])
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, resolution[1])
            
            # Check if camera opened successfully
            if not cap.isOpened():
                raise RuntimeError(f"Failed to open camera {camera_id}")
            
            # Capture frames
            frames = []
            for _ in range(num_frames):
                ret, frame = cap.read()
                if not ret:
                    raise RuntimeError("Failed to capture frame")
                frames.append(frame)
            
            # Release camera
            cap.release()
            
            # Average frames
            if num_frames > 1:
                avg_frame = np.mean(frames, axis=0).astype(np.uint8)
            else:
                avg_frame = frames[0]
            
            # Convert BGR to RGB
            avg_frame = cv2.cvtColor(avg_frame, cv2.COLOR_BGR2RGB)
            
            return avg_frame
        
        except Exception as e:
            logger.error(f"Camera capture failed: {e}")
            raise RuntimeError(f"Camera capture failed: {e}") from e
