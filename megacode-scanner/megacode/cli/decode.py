"""
Command-line interface for MegaCode scanner.
"""

import os
import sys
import logging
import click
import numpy as np
from typing import Optional, List, Dict, Union, Tuple

from ..acquisition.image_loader import ImageLoader
from ..detection.code_detector import CodeDetector
from ..detection.grid_extractor import GridExtractor
from ..classification.color_classifier import ColorClassifier
from ..decoding.decoder import Decoder


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--input",
    "-i",
    required=True,
    type=click.Path(exists=True, readable=True),
    help="Input image file",
)
@click.option(
    "--output",
    "-o",
    type=click.Path(writable=True),
    help="Output file path (if not provided, prints to stdout)",
)
@click.option(
    "--format",
    "-f",
    type=click.Choice(["auto", "text", "binary"]),
    default="auto",
    help="Output format",
)
@click.option(
    "--palette",
    type=click.Choice(["auto", "bw", "4-color", "8-color", "16-color"]),
    default="auto",
    help="Color palette",
)
@click.option(
    "--debug",
    is_flag=True,
    help="Enable debug mode (saves intermediate images)",
)
@click.option(
    "--verbose",
    "-v",
    is_flag=True,
    help="Enable verbose logging",
)
def main(
    input: str,
    output: Optional[str],
    format: str,
    palette: str,
    debug: bool,
    verbose: bool,
):
    """
    Decode MegaCode symbols from an image.
    """
    # Set logging level
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize components
    image_loader = ImageLoader()
    code_detector = CodeDetector()
    grid_extractor = GridExtractor()
    color_classifier = ColorClassifier()
    decoder = Decoder()
    
    try:
        # Load image
        logger.info(f"Loading image: {input}")
        image = image_loader.load_image(input)
        
        if debug:
            import cv2
            cv2.imwrite("debug_input.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
        
        # Detect code
        logger.info("Detecting code")
        detection_result = code_detector.detect_code(image)
        
        if not detection_result["found"]:
            logger.error("No code detected in the image")
            sys.exit(1)
        
        logger.info(f"Code detected with confidence: {detection_result['confidence']:.2f}")
        
        if debug:
            # Draw detection result
            debug_image = image.copy()
            corners = detection_result["corners"]
            
            import cv2
            for i in range(4):
                cv2.line(
                    debug_image,
                    corners[i],
                    corners[(i + 1) % 4],
                    (0, 255, 0),
                    2,
                )
            
            cv2.imwrite("debug_detection.png", cv2.cvtColor(debug_image, cv2.COLOR_RGB2BGR))
        
        # Extract grid
        logger.info("Extracting grid")
        extraction_result = grid_extractor.extract_grid(image, detection_result)
        
        if not extraction_result["success"]:
            logger.error("Failed to extract grid")
            sys.exit(1)
        
        logger.info(f"Grid extracted: {extraction_result['grid_size'][0]}x{extraction_result['grid_size'][1]} modules")
        
        if debug:
            import cv2
            # Save grid image
            grid_image = extraction_result["grid"].copy()
            # Resize for better visibility
            grid_image = cv2.resize(
                grid_image,
                (grid_image.shape[1] * 10, grid_image.shape[0] * 10),
                interpolation=cv2.INTER_NEAREST,
            )
            cv2.imwrite("debug_grid.png", cv2.cvtColor(grid_image, cv2.COLOR_RGB2BGR))
        
        # Classify colors
        logger.info("Classifying colors")
        palette_name = None if palette == "auto" else palette
        classification_result = color_classifier.classify_colors(
            extraction_result["grid"],
            extraction_result["calibration"],
            palette_name=palette_name,
        )
        
        if not classification_result["success"]:
            logger.error("Failed to classify colors")
            sys.exit(1)
        
        logger.info(f"Colors classified with palette: {classification_result['palette'].name}")
        
        if debug:
            # Save symbols image
            symbols = classification_result["symbols"]
            palette = classification_result["palette"]
            
            # Create colored image from symbols
            symbol_image = np.zeros(
                (symbols.shape[0], symbols.shape[1], 3),
                dtype=np.uint8,
            )
            
            for y in range(symbols.shape[0]):
                for x in range(symbols.shape[1]):
                    symbol = symbols[y, x]
                    if symbol < len(palette.colors):
                        symbol_image[y, x] = palette.colors[symbol]
            
            # Resize for better visibility
            import cv2
            symbol_image = cv2.resize(
                symbol_image,
                (symbol_image.shape[1] * 10, symbol_image.shape[0] * 10),
                interpolation=cv2.INTER_NEAREST,
            )
            cv2.imwrite("debug_symbols.png", cv2.cvtColor(symbol_image, cv2.COLOR_RGB2BGR))
        
        # Convert symbols to bits
        logger.info("Converting symbols to bits")
        bits = color_classifier.symbols_to_bits(
            classification_result["symbols"],
            classification_result["palette"],
        )
        
        # Convert bits to bytes
        logger.info("Converting bits to bytes")
        raw_data = color_classifier.bits_to_bytes(bits)
        
        # Decode data
        logger.info("Decoding data")
        decoding_result = decoder.decode_data(raw_data)
        
        if not decoding_result["success"]:
            logger.error("Failed to decode data")
            sys.exit(1)
        
        logger.info(f"Data decoded: {len(decoding_result['data'])} bytes")
        
        if decoding_result["errors_corrected"] > 0:
            logger.info(f"Corrected {decoding_result['errors_corrected']} errors")
        
        # Determine if data is text or binary
        if format == "auto":
            # Try to decode as UTF-8
            try:
                decoding_result["data"].decode("utf-8")
                is_text = True
            except UnicodeDecodeError:
                is_text = False
        else:
            is_text = format == "text"
        
        # Output data
        if output:
            # Write to file
            mode = "w" if is_text else "wb"
            with open(output, mode) as f:
                if is_text:
                    f.write(decoding_result["data"].decode("utf-8"))
                else:
                    f.write(decoding_result["data"])
            
            logger.info(f"Data written to {output}")
        else:
            # Print to stdout
            if is_text:
                print(decoding_result["data"].decode("utf-8"))
            else:
                sys.stdout.buffer.write(decoding_result["data"])
    
    except Exception as e:
        logger.error(f"Error: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
