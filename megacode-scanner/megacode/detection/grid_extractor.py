"""
Grid extraction module for MegaCode scanner.

This module provides functions for extracting the grid from detected MegaCode symbols.
"""

import logging
import numpy as np
from typing import List, Tuple, Dict, Any, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Try to import OpenCV
try:
    import cv2
    HAVE_CV2 = True
except ImportError:
    logger.warning("OpenCV not available, grid extraction will be limited")
    HAVE_CV2 = False


class GridExtractor:
    """Grid extractor for MegaCode scanner."""
    
    def __init__(self):
        """Initialize the grid extractor."""
        self.finder_pattern_size = 7  # Size of QR-like finder pattern
        self.quiet_zone_size = 4      # Size of quiet zone around the code
    
    def extract_grid(
        self,
        image: np.ndarray,
        detection_result: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Extract the grid from a detected MegaCode symbol.
        
        Args:
            image: Input image as a NumPy array
            detection_result: Detection result from CodeDetector.detect_code()
            
        Returns:
            Dictionary with extraction results:
            - 'success': Whether extraction was successful
            - 'grid': Extracted grid as a NumPy array
            - 'module_size': Estimated module size in pixels
            - 'grid_size': Grid dimensions (width, height)
            - 'calibration': Calibration data
            
        Raises:
            RuntimeError: If grid extraction fails
        """
        if not HAVE_CV2:
            raise RuntimeError("OpenCV not available, grid extraction not supported")
        
        # Check if code was found
        if not detection_result["found"]:
            return {
                "success": False,
                "grid": None,
                "module_size": 0,
                "grid_size": (0, 0),
                "calibration": None,
            }
        
        # Get perspective transformation
        perspective = detection_result["perspective"]
        if perspective is None:
            return {
                "success": False,
                "grid": None,
                "module_size": 0,
                "grid_size": (0, 0),
                "calibration": None,
            }
        
        # Get corners
        corners = detection_result["corners"]
        
        # Calculate width and height
        width = max(
            np.sqrt((corners[1][0] - corners[0][0])**2 + (corners[1][1] - corners[0][1])**2),
            np.sqrt((corners[2][0] - corners[3][0])**2 + (corners[2][1] - corners[3][1])**2)
        )
        height = max(
            np.sqrt((corners[3][0] - corners[0][0])**2 + (corners[3][1] - corners[0][1])**2),
            np.sqrt((corners[2][0] - corners[1][0])**2 + (corners[2][1] - corners[1][1])**2)
        )
        
        # Round to integers
        width = int(width)
        height = int(height)
        
        # Create top-down view
        warped = cv2.warpPerspective(image, perspective, (width, height))
        
        # Estimate module size
        module_size = self._estimate_module_size(warped)
        
        # Estimate grid size
        grid_width = (width - 2 * (self.finder_pattern_size + self.quiet_zone_size) * module_size) // module_size
        grid_height = (height - 2 * (self.finder_pattern_size + self.quiet_zone_size) * module_size) // module_size
        
        # Ensure grid size is positive
        grid_width = max(1, grid_width)
        grid_height = max(1, grid_height)
        
        # Extract grid
        grid = self._extract_modules(warped, module_size, (grid_width, grid_height))
        
        # Extract calibration data
        calibration = self._extract_calibration(warped, module_size)
        
        return {
            "success": True,
            "grid": grid,
            "module_size": module_size,
            "grid_size": (grid_width, grid_height),
            "calibration": calibration,
        }
    
    def _estimate_module_size(self, warped: np.ndarray) -> int:
        """
        Estimate the module size in pixels.
        
        Args:
            warped: Warped image (top-down view)
            
        Returns:
            Estimated module size in pixels
        """
        # Convert to grayscale if needed
        if len(warped.shape) == 3 and warped.shape[2] == 3:
            gray = cv2.cvtColor(warped, cv2.COLOR_RGB2GRAY)
        else:
            gray = warped
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # Extract top-left finder pattern
        finder_region = thresh[:self.finder_pattern_size * 5, :self.finder_pattern_size * 5]
        
        # Find contours
        contours, _ = cv2.findContours(finder_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find the largest contour
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Get bounding box
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Estimate module size
            module_size = max(w, h) // self.finder_pattern_size
            
            # Ensure module size is at least 1 pixel
            module_size = max(1, module_size)
            
            return module_size
        
        # Fallback: estimate based on image size
        return max(1, min(warped.shape) // 50)
    
    def _extract_modules(
        self,
        warped: np.ndarray,
        module_size: int,
        grid_size: Tuple[int, int],
    ) -> np.ndarray:
        """
        Extract modules from the warped image.
        
        Args:
            warped: Warped image (top-down view)
            module_size: Module size in pixels
            grid_size: Grid dimensions (width, height)
            
        Returns:
            Grid of modules as a NumPy array (height, width, 3)
        """
        # Calculate offset
        offset_x = (self.finder_pattern_size + self.quiet_zone_size) * module_size
        offset_y = (self.finder_pattern_size + self.quiet_zone_size) * module_size
        
        # Create grid
        grid_width, grid_height = grid_size
        grid = np.zeros((grid_height, grid_width, 3), dtype=np.uint8)
        
        # Extract modules
        for y in range(grid_height):
            for x in range(grid_width):
                # Calculate module position
                module_x = offset_x + x * module_size
                module_y = offset_y + y * module_size
                
                # Ensure within bounds
                if (
                    module_x + module_size <= warped.shape[1] and
                    module_y + module_size <= warped.shape[0]
                ):
                    # Extract module
                    module = warped[
                        module_y:module_y + module_size,
                        module_x:module_x + module_size
                    ]
                    
                    # Calculate average color
                    if len(module.shape) == 3:
                        avg_color = np.mean(module, axis=(0, 1)).astype(np.uint8)
                    else:
                        avg_color = np.array([np.mean(module)] * 3, dtype=np.uint8)
                    
                    # Set grid value
                    grid[y, x] = avg_color
        
        return grid
    
    def _extract_calibration(
        self,
        warped: np.ndarray,
        module_size: int,
    ) -> Dict[str, Any]:
        """
        Extract calibration data from the warped image.
        
        Args:
            warped: Warped image (top-down view)
            module_size: Module size in pixels
            
        Returns:
            Dictionary with calibration data:
            - 'colors': List of calibration colors
            - 'positions': List of calibration positions
        """
        # Define standard colors for calibration
        standard_colors = [
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 255, 0),     # Green
            (0, 0, 255),     # Blue
            (255, 255, 0),   # Yellow
            (255, 0, 255),   # Magenta
            (0, 255, 255),   # Cyan
        ]
        
        # Size of each calibration patch
        patch_size = 5 * module_size
        
        # Calculate position of calibration pattern (bottom-right corner)
        x = warped.shape[1] - (self.quiet_zone_size + len(standard_colors) * 5) * module_size
        y = warped.shape[0] - (self.quiet_zone_size + 5) * module_size
        
        # Extract calibration colors
        calibration_colors = []
        calibration_positions = []
        
        for i in range(len(standard_colors)):
            # Calculate patch position
            patch_x = x + i * patch_size
            patch_y = y
            
            # Ensure within bounds
            if (
                patch_x + patch_size <= warped.shape[1] and
                patch_y + patch_size <= warped.shape[0] and
                patch_x >= 0 and patch_y >= 0
            ):
                # Extract patch
                patch = warped[
                    patch_y:patch_y + patch_size,
                    patch_x:patch_x + patch_size
                ]
                
                # Calculate average color
                if len(patch.shape) == 3:
                    avg_color = np.mean(patch, axis=(0, 1)).astype(np.uint8)
                else:
                    avg_color = np.array([np.mean(patch)] * 3, dtype=np.uint8)
                
                # Add to calibration data
                calibration_colors.append(tuple(avg_color))
                calibration_positions.append((patch_x, patch_y, patch_size))
        
        return {
            "colors": calibration_colors,
            "positions": calibration_positions,
            "standard_colors": standard_colors,
        }
