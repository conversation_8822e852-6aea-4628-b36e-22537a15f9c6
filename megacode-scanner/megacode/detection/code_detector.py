"""
Code detection module for MegaCode scanner.

This module provides functions for detecting MegaCode symbols in images.
"""

import logging
import numpy as np
from typing import List, Tuple, Dict, Any, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Try to import OpenCV
try:
    import cv2
    HAVE_CV2 = True
except ImportError:
    logger.warning("OpenCV not available, code detection will be limited")
    HAVE_CV2 = False


class CodeDetector:
    """Code detector for MegaCode scanner."""
    
    def __init__(self):
        """Initialize the code detector."""
        self.finder_pattern_size = 7  # Size of QR-like finder pattern
        self.quiet_zone_size = 4      # Size of quiet zone around the code
    
    def detect_code(
        self,
        image: np.ndarray,
        min_size: int = 100,
        max_size: int = 1000,
    ) -> Dict[str, Any]:
        """
        Detect MegaCode symbols in an image.
        
        Args:
            image: Input image as a NumPy array
            min_size: Minimum size of the code in pixels
            max_size: Maximum size of the code in pixels
            
        Returns:
            Dictionary with detection results:
            - 'found': Whether a code was found
            - 'corners': List of corner points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            - 'confidence': Detection confidence (0.0 to 1.0)
            - 'rotation': Estimated rotation angle in degrees
            - 'perspective': Perspective transformation matrix
            
        Raises:
            RuntimeError: If code detection fails
        """
        if not HAVE_CV2:
            raise RuntimeError("OpenCV not available, code detection not supported")
        
        # Convert to grayscale if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Initialize result
        result = {
            "found": False,
            "corners": [],
            "confidence": 0.0,
            "rotation": 0.0,
            "perspective": None,
        }
        
        # Process contours
        for contour in contours:
            # Filter by size
            area = cv2.contourArea(contour)
            if area < min_size * min_size or area > max_size * max_size:
                continue
            
            # Approximate contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # Check if it's a quadrilateral
            if len(approx) == 4:
                # Check if it's a convex quadrilateral
                if cv2.isContourConvex(approx):
                    # Extract corners
                    corners = [tuple(point[0]) for point in approx]
                    
                    # Check for finder patterns
                    if self._check_finder_patterns(gray, corners):
                        # Calculate confidence
                        confidence = self._calculate_confidence(gray, corners)
                        
                        # Calculate rotation
                        rotation = self._calculate_rotation(corners)
                        
                        # Calculate perspective transformation
                        perspective = self._calculate_perspective(corners)
                        
                        # Update result
                        result["found"] = True
                        result["corners"] = corners
                        result["confidence"] = confidence
                        result["rotation"] = rotation
                        result["perspective"] = perspective
                        
                        # Stop after finding the first valid code
                        break
        
        return result
    
    def _check_finder_patterns(
        self,
        image: np.ndarray,
        corners: List[Tuple[int, int]],
    ) -> bool:
        """
        Check if the corners contain valid finder patterns.
        
        Args:
            image: Grayscale image
            corners: List of corner points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            
        Returns:
            True if valid finder patterns are found, False otherwise
        """
        # Sort corners (top-left, top-right, bottom-right, bottom-left)
        corners = self._sort_corners(corners)
        
        # Check each corner for finder pattern
        finder_size = self.finder_pattern_size
        
        # Define regions to check
        regions = [
            (0, 0, finder_size, finder_size),  # Top-left
            (0, -finder_size, finder_size, finder_size),  # Top-right
            (-finder_size, 0, finder_size, finder_size),  # Bottom-left
        ]
        
        # Apply perspective transformation to get a top-down view
        perspective = self._calculate_perspective(corners)
        if perspective is None:
            return False
        
        # Get code size
        width = max(corners[1][0], corners[2][0]) - min(corners[0][0], corners[3][0])
        height = max(corners[2][1], corners[3][1]) - min(corners[0][1], corners[1][1])
        
        # Create top-down view
        warped = cv2.warpPerspective(image, perspective, (width, height))
        
        # Check each region
        valid_patterns = 0
        for x, y, w, h in regions:
            # Adjust coordinates for negative indices
            x_adj = x if x >= 0 else width + x
            y_adj = y if y >= 0 else height + y
            
            # Extract region
            if (
                x_adj >= 0 and y_adj >= 0 and
                x_adj + w <= width and y_adj + h <= height
            ):
                region = warped[y_adj:y_adj+h, x_adj:x_adj+w]
                
                # Check if it matches finder pattern
                if self._is_finder_pattern(region):
                    valid_patterns += 1
        
        # Require at least 2 valid patterns
        return valid_patterns >= 2
    
    def _is_finder_pattern(self, region: np.ndarray) -> bool:
        """
        Check if a region contains a valid finder pattern.
        
        Args:
            region: Image region to check
            
        Returns:
            True if a valid finder pattern is found, False otherwise
        """
        # Resize to standard size if needed
        if region.shape[0] != self.finder_pattern_size or region.shape[1] != self.finder_pattern_size:
            region = cv2.resize(region, (self.finder_pattern_size, self.finder_pattern_size))
        
        # Apply threshold
        _, thresh = cv2.threshold(region, 128, 255, cv2.THRESH_BINARY)
        
        # Define ideal finder pattern (1 = black, 0 = white)
        ideal = np.zeros((self.finder_pattern_size, self.finder_pattern_size), dtype=np.uint8)
        
        # Outer border
        ideal[0, :] = 1  # Top
        ideal[-1, :] = 1  # Bottom
        ideal[:, 0] = 1  # Left
        ideal[:, -1] = 1  # Right
        
        # Inner square
        center = self.finder_pattern_size // 2
        size = self.finder_pattern_size // 3
        ideal[center-size:center+size+1, center-size:center+size+1] = 1
        
        # Convert threshold to binary (1 = black, 0 = white)
        binary = (thresh == 0).astype(np.uint8)
        
        # Calculate match percentage
        match = np.sum(binary == ideal) / (self.finder_pattern_size * self.finder_pattern_size)
        
        # Require at least 80% match
        return match >= 0.8
    
    def _calculate_confidence(
        self,
        image: np.ndarray,
        corners: List[Tuple[int, int]],
    ) -> float:
        """
        Calculate detection confidence.
        
        Args:
            image: Grayscale image
            corners: List of corner points
            
        Returns:
            Confidence value (0.0 to 1.0)
        """
        # Simple confidence based on finder pattern match
        valid_patterns = 0
        total_patterns = 3
        
        # Sort corners
        corners = self._sort_corners(corners)
        
        # Apply perspective transformation
        perspective = self._calculate_perspective(corners)
        if perspective is None:
            return 0.0
        
        # Get code size
        width = max(corners[1][0], corners[2][0]) - min(corners[0][0], corners[3][0])
        height = max(corners[2][1], corners[3][1]) - min(corners[0][1], corners[1][1])
        
        # Create top-down view
        warped = cv2.warpPerspective(image, perspective, (width, height))
        
        # Check finder patterns
        finder_size = self.finder_pattern_size
        
        # Define regions to check
        regions = [
            (0, 0, finder_size, finder_size),  # Top-left
            (0, -finder_size, finder_size, finder_size),  # Top-right
            (-finder_size, 0, finder_size, finder_size),  # Bottom-left
        ]
        
        # Check each region
        for x, y, w, h in regions:
            # Adjust coordinates for negative indices
            x_adj = x if x >= 0 else width + x
            y_adj = y if y >= 0 else height + y
            
            # Extract region
            if (
                x_adj >= 0 and y_adj >= 0 and
                x_adj + w <= width and y_adj + h <= height
            ):
                region = warped[y_adj:y_adj+h, x_adj:x_adj+w]
                
                # Check if it matches finder pattern
                if self._is_finder_pattern(region):
                    valid_patterns += 1
        
        # Calculate confidence
        confidence = valid_patterns / total_patterns
        
        return confidence
    
    def _calculate_rotation(
        self,
        corners: List[Tuple[int, int]],
    ) -> float:
        """
        Calculate rotation angle.
        
        Args:
            corners: List of corner points
            
        Returns:
            Rotation angle in degrees
        """
        # Sort corners
        corners = self._sort_corners(corners)
        
        # Calculate angle between top-left and top-right corners
        x1, y1 = corners[0]
        x2, y2 = corners[1]
        
        angle = np.degrees(np.arctan2(y2 - y1, x2 - x1))
        
        return angle
    
    def _calculate_perspective(
        self,
        corners: List[Tuple[int, int]],
    ) -> Optional[np.ndarray]:
        """
        Calculate perspective transformation matrix.
        
        Args:
            corners: List of corner points
            
        Returns:
            Perspective transformation matrix or None if invalid
        """
        # Sort corners
        corners = self._sort_corners(corners)
        
        # Calculate width and height
        width = max(
            np.sqrt((corners[1][0] - corners[0][0])**2 + (corners[1][1] - corners[0][1])**2),
            np.sqrt((corners[2][0] - corners[3][0])**2 + (corners[2][1] - corners[3][1])**2)
        )
        height = max(
            np.sqrt((corners[3][0] - corners[0][0])**2 + (corners[3][1] - corners[0][1])**2),
            np.sqrt((corners[2][0] - corners[1][0])**2 + (corners[2][1] - corners[1][1])**2)
        )
        
        # Round to integers
        width = int(width)
        height = int(height)
        
        # Check if valid
        if width <= 0 or height <= 0:
            return None
        
        # Define destination points
        dst_points = np.array([
            [0, 0],
            [width, 0],
            [width, height],
            [0, height]
        ], dtype=np.float32)
        
        # Convert corners to numpy array
        src_points = np.array(corners, dtype=np.float32)
        
        # Calculate perspective transformation
        perspective = cv2.getPerspectiveTransform(src_points, dst_points)
        
        return perspective
    
    def _sort_corners(
        self,
        corners: List[Tuple[int, int]],
    ) -> List[Tuple[int, int]]:
        """
        Sort corners in order: top-left, top-right, bottom-right, bottom-left.
        
        Args:
            corners: List of corner points
            
        Returns:
            Sorted corners
        """
        # Convert to numpy array
        corners_array = np.array(corners)
        
        # Calculate center
        center = np.mean(corners_array, axis=0)
        
        # Sort corners based on their position relative to the center
        sorted_corners = []
        
        # Top-left: both x and y less than center
        tl = corners_array[np.logical_and(corners_array[:, 0] < center[0], corners_array[:, 1] < center[1])]
        if len(tl) > 0:
            sorted_corners.append(tuple(tl[0]))
        else:
            # If no exact match, find the point with minimum distance to top-left
            distances = np.sqrt((corners_array[:, 0] - 0)**2 + (corners_array[:, 1] - 0)**2)
            sorted_corners.append(tuple(corners_array[np.argmin(distances)]))
        
        # Top-right: x greater than center, y less than center
        tr = corners_array[np.logical_and(corners_array[:, 0] > center[0], corners_array[:, 1] < center[1])]
        if len(tr) > 0:
            sorted_corners.append(tuple(tr[0]))
        else:
            # If no exact match, find the point with minimum distance to top-right
            distances = np.sqrt((corners_array[:, 0] - center[0]*2)**2 + (corners_array[:, 1] - 0)**2)
            sorted_corners.append(tuple(corners_array[np.argmin(distances)]))
        
        # Bottom-right: both x and y greater than center
        br = corners_array[np.logical_and(corners_array[:, 0] > center[0], corners_array[:, 1] > center[1])]
        if len(br) > 0:
            sorted_corners.append(tuple(br[0]))
        else:
            # If no exact match, find the point with minimum distance to bottom-right
            distances = np.sqrt((corners_array[:, 0] - center[0]*2)**2 + (corners_array[:, 1] - center[1]*2)**2)
            sorted_corners.append(tuple(corners_array[np.argmin(distances)]))
        
        # Bottom-left: x less than center, y greater than center
        bl = corners_array[np.logical_and(corners_array[:, 0] < center[0], corners_array[:, 1] > center[1])]
        if len(bl) > 0:
            sorted_corners.append(tuple(bl[0]))
        else:
            # If no exact match, find the point with minimum distance to bottom-left
            distances = np.sqrt((corners_array[:, 0] - 0)**2 + (corners_array[:, 1] - center[1]*2)**2)
            sorted_corners.append(tuple(corners_array[np.argmin(distances)]))
        
        return sorted_corners
