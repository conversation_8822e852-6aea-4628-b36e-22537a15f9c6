"""
Decoding module for MegaCode scanner.

This module provides functions for decoding data from classified symbols.
"""

import logging
import struct
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)

# Try to import required modules
try:
    import reedsolo
    HAVE_REEDSOLO = True
except ImportError:
    logger.warning("reedsolo not available, error correction will be limited")
    HAVE_REEDSOLO = False


class Decoder:
    """Decoder for MegaCode scanner."""
    
    def __init__(self):
        """Initialize the decoder."""
        pass
    
    def decode_data(
        self,
        raw_data: bytes,
    ) -> Dict[str, Any]:
        """
        Decode data from raw bytes.
        
        Args:
            raw_data: Raw data bytes
            
        Returns:
            Dictionary with decoding results:
            - 'success': Whether decoding was successful
            - 'data': Decoded data
            - 'metadata': Metadata from the code
            - 'errors_corrected': Number of errors corrected
            
        Raises:
            ValueError: If the data is invalid
        """
        # Check if data is valid
        if not raw_data:
            return {
                "success": False,
                "data": None,
                "metadata": None,
                "errors_corrected": 0,
            }
        
        try:
            # Parse fountain packets
            packets = self._parse_fountain_packets(raw_data)
            
            if not packets:
                logger.warning("No valid fountain packets found")
                return {
                    "success": False,
                    "data": None,
                    "metadata": None,
                    "errors_corrected": 0,
                }
            
            # Decode fountain packets
            fountain_data = self._decode_fountain_packets(packets)
            
            if fountain_data is None:
                logger.warning("Failed to decode fountain packets")
                return {
                    "success": False,
                    "data": None,
                    "metadata": None,
                    "errors_corrected": 0,
                }
            
            # Parse RS blocks
            rs_blocks = self._parse_rs_blocks(fountain_data)
            
            if not rs_blocks:
                logger.warning("No valid RS blocks found")
                return {
                    "success": False,
                    "data": None,
                    "metadata": None,
                    "errors_corrected": 0,
                }
            
            # Decode RS blocks
            rs_data, errors_corrected = self._decode_rs_blocks(rs_blocks)
            
            if rs_data is None:
                logger.warning("Failed to decode RS blocks")
                return {
                    "success": False,
                    "data": None,
                    "metadata": None,
                    "errors_corrected": errors_corrected,
                }
            
            # Decompress data
            decompressed_data, metadata = self._decompress_data(rs_data)
            
            if decompressed_data is None:
                logger.warning("Failed to decompress data")
                return {
                    "success": False,
                    "data": None,
                    "metadata": metadata,
                    "errors_corrected": errors_corrected,
                }
            
            return {
                "success": True,
                "data": decompressed_data,
                "metadata": metadata,
                "errors_corrected": errors_corrected,
            }
        
        except Exception as e:
            logger.error(f"Decoding failed: {e}")
            return {
                "success": False,
                "data": None,
                "metadata": None,
                "errors_corrected": 0,
            }
    
    def _parse_fountain_packets(self, data: bytes) -> List[Dict[str, Any]]:
        """
        Parse fountain packets from raw data.
        
        Args:
            data: Raw data bytes
            
        Returns:
            List of fountain packets
        """
        packets = []
        offset = 0
        
        while offset < len(data):
            try:
                # Check if we have enough data for a header
                if offset + 13 > len(data):
                    break
                
                # Parse header
                magic, seed, num_indices, total_blocks, original_size = struct.unpack(
                    "!HIHHI", data[offset:offset+13]
                )
                
                # Check magic bytes
                if magic != 0x4D46:  # 'MF'
                    offset += 1
                    continue
                
                # Check if we have enough data for indices
                indices_size = num_indices * 2  # 2 bytes per index
                if offset + 13 + indices_size > len(data):
                    offset += 1
                    continue
                
                # Parse indices
                indices_format = f"!{num_indices}H"
                indices = struct.unpack(
                    indices_format, data[offset+13:offset+13+indices_size]
                )
                
                # Calculate packet size
                packet_size = 13 + indices_size + (total_blocks > 0 and original_size // total_blocks or 0)
                
                # Check if we have enough data for the packet
                if offset + packet_size > len(data):
                    # Use remaining data
                    packet_data = data[offset+13+indices_size:]
                else:
                    packet_data = data[offset+13+indices_size:offset+packet_size]
                
                # Create packet
                packet = {
                    "seed": seed,
                    "block_indices": list(indices),
                    "total_blocks": total_blocks,
                    "original_size": original_size,
                    "data": packet_data,
                }
                
                packets.append(packet)
                
                # Move to next packet
                offset += packet_size
            
            except Exception as e:
                logger.warning(f"Failed to parse fountain packet at offset {offset}: {e}")
                offset += 1
        
        logger.info(f"Parsed {len(packets)} fountain packets")
        return packets
    
    def _decode_fountain_packets(self, packets: List[Dict[str, Any]]) -> Optional[bytes]:
        """
        Decode fountain packets.
        
        Args:
            packets: List of fountain packets
            
        Returns:
            Decoded data or None if decoding fails
        """
        if not packets:
            return None
        
        # Extract parameters from first packet
        total_blocks = packets[0]["total_blocks"]
        original_size = packets[0]["original_size"]
        block_size = len(packets[0]["data"])
        
        logger.info(
            f"Decoding {len(packets)} packets to recover {total_blocks} blocks "
            f"({original_size} bytes)"
        )
        
        # Initialize decoded blocks
        decoded_blocks = [None] * total_blocks
        decoded_count = 0
        
        # Initialize ripple (packets that can decode exactly one block)
        ripple = []
        
        # Process all packets
        remaining_packets = list(packets)
        
        # Continue until all blocks are decoded or no more progress
        while decoded_count < total_blocks and remaining_packets:
            # Find packets that can decode exactly one block
            for i, packet in enumerate(remaining_packets):
                # Skip if already in ripple
                if packet in ripple:
                    continue
                
                # Check if this packet can decode exactly one block
                unknown_blocks = [
                    idx for idx in packet["block_indices"] if decoded_blocks[idx] is None
                ]
                
                if len(unknown_blocks) == 1:
                    ripple.append(packet)
            
            # If ripple is empty, we can't make progress
            if not ripple:
                logger.warning(
                    f"Decoding stalled: {decoded_count}/{total_blocks} blocks decoded"
                )
                return None
            
            # Process a packet from the ripple
            packet = ripple.pop(0)
            remaining_packets.remove(packet)
            
            # Find the unknown block
            unknown_blocks = [
                idx for idx in packet["block_indices"] if decoded_blocks[idx] is None
            ]
            
            if not unknown_blocks:
                # All blocks in this packet are already decoded
                continue
            
            unknown_idx = unknown_blocks[0]
            
            # XOR with all known blocks to recover the unknown block
            block_data = bytearray(packet["data"])
            
            for idx in packet["block_indices"]:
                if idx != unknown_idx and decoded_blocks[idx] is not None:
                    for j in range(min(len(block_data), len(decoded_blocks[idx]))):
                        block_data[j] ^= decoded_blocks[idx][j]
            
            # Store the decoded block
            decoded_blocks[unknown_idx] = bytes(block_data)
            decoded_count += 1
            
            logger.debug(f"Decoded block {unknown_idx} ({decoded_count}/{total_blocks})")
        
        # Check if all blocks were decoded
        if decoded_count < total_blocks:
            logger.warning(
                f"Failed to decode all blocks: {decoded_count}/{total_blocks}"
            )
            return None
        
        # Combine blocks to form the original data
        decoded_data = b"".join(decoded_blocks)
        
        # Truncate to original size
        decoded_data = decoded_data[:original_size]
        
        logger.info(f"Successfully decoded {len(decoded_data)} bytes")
        
        return decoded_data
    
    def _parse_rs_blocks(self, data: bytes) -> List[Dict[str, Any]]:
        """
        Parse Reed-Solomon blocks from data.
        
        Args:
            data: Data bytes
            
        Returns:
            List of RS blocks
        """
        blocks = []
        offset = 0
        
        while offset < len(data):
            try:
                # Check if we have enough data for a header
                if offset + 7 > len(data):
                    break
                
                # Parse header
                magic1, magic2, ecc_level, block_id, total_blocks = struct.unpack(
                    "!BBBBB", data[offset:offset+5]
                )
                
                # Check magic bytes
                if magic1 != 0x4D or magic2 != 0x43:  # 'MC'
                    offset += 1
                    continue
                
                # Parse lengths
                data_length, ecc_length = struct.unpack(
                    "!BB", data[offset+5:offset+7]
                )
                
                # Check if we have enough data for the block
                if offset + 7 + data_length + ecc_length > len(data):
                    offset += 1
                    continue
                
                # Extract data and ECC
                block_data = data[offset+7:offset+7+data_length]
                ecc_data = data[offset+7+data_length:offset+7+data_length+ecc_length]
                
                # Create block
                block = {
                    "ecc_level": ecc_level,
                    "block_id": block_id,
                    "total_blocks": total_blocks,
                    "data": block_data,
                    "ecc": ecc_data,
                }
                
                blocks.append(block)
                
                # Move to next block
                offset += 7 + data_length + ecc_length
            
            except Exception as e:
                logger.warning(f"Failed to parse RS block at offset {offset}: {e}")
                offset += 1
        
        logger.info(f"Parsed {len(blocks)} RS blocks")
        return blocks
    
    def _decode_rs_blocks(self, blocks: List[Dict[str, Any]]) -> Tuple[Optional[bytes], int]:
        """
        Decode Reed-Solomon blocks.
        
        Args:
            blocks: List of RS blocks
            
        Returns:
            Tuple of (decoded_data, errors_corrected)
        """
        if not HAVE_REEDSOLO:
            logger.warning("reedsolo not available, skipping error correction")
            # Concatenate block data without error correction
            return b"".join(block["data"] for block in blocks), 0
        
        # Sort blocks by ID
        sorted_blocks = sorted(blocks, key=lambda b: b["block_id"])
        
        # Check if we have all blocks
        block_ids = [b["block_id"] for b in sorted_blocks]
        total_blocks = sorted_blocks[0]["total_blocks"]
        expected_ids = set(range(total_blocks))
        missing_ids = expected_ids - set(block_ids)
        
        if missing_ids:
            logger.warning(f"Missing blocks: {missing_ids}")
            return None, 0
        
        # Decode each block
        decoded_data = bytearray()
        total_errors = 0
        
        for block in sorted_blocks:
            # Initialize RS codec based on block's ECC
            nsym = len(block["ecc"])
            rs = reedsolo.RSCodec(nsym)
            
            # Combine data and ECC for decoding
            encoded = block["data"] + block["ecc"]
            
            try:
                # Attempt to decode and correct errors
                decoded, _, errata_pos = rs.decode(encoded, return_errata_positions=True)
                
                if errata_pos:
                    logger.info(f"Corrected {len(errata_pos)} errors in block {block['block_id']}")
                    total_errors += len(errata_pos)
                
                # Remove padding from the last block
                if block["block_id"] == total_blocks - 1:
                    # Find the first zero byte from the end
                    i = len(decoded) - 1
                    while i >= 0 and decoded[i] == 0:
                        i -= 1
                    
                    # Keep data up to that point (inclusive)
                    decoded_data.extend(decoded[:i+1])
                else:
                    decoded_data.extend(decoded)
                    
            except reedsolo.ReedSolomonError as e:
                logger.error(f"Failed to decode block {block['block_id']}: {e}")
                return None, total_errors
        
        return bytes(decoded_data), total_errors
    
    def _decompress_data(self, data: bytes) -> Tuple[Optional[bytes], Dict[str, Any]]:
        """
        Decompress data.
        
        Args:
            data: Compressed data
            
        Returns:
            Tuple of (decompressed_data, metadata)
        """
        try:
            # Extract header
            header_size = struct.calcsize("!IB")
            if len(data) < header_size:
                logger.warning("Compressed data is too small to contain a valid header")
                return None, {}
            
            original_size, compression_method = struct.unpack(
                "!IB", data[:header_size]
            )
            
            # Get compressed data without header
            compressed = data[header_size:]
            
            # Apply decompression
            if compression_method == 1:  # gzip
                import gzip
                logger.info(f"Decompressing {len(compressed)} bytes with gzip")
                decompressed = gzip.decompress(compressed)
            elif compression_method == 2:  # brotli
                try:
                    import brotli
                    logger.info(f"Decompressing {len(compressed)} bytes with brotli")
                    decompressed = brotli.decompress(compressed)
                except ImportError:
                    logger.warning("brotli not available, cannot decompress")
                    return None, {"compression_method": "brotli"}
            elif compression_method == 0:  # none
                logger.info("No decompression needed")
                decompressed = compressed
            else:
                logger.warning(f"Unsupported compression method: {compression_method}")
                return None, {"compression_method": compression_method}
            
            # Verify size
            if len(decompressed) != original_size:
                logger.warning(
                    f"Decompressed size ({len(decompressed)}) doesn't match "
                    f"original size ({original_size})"
                )
            
            return decompressed, {
                "original_size": original_size,
                "compression_method": compression_method,
            }
        
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return None, {}
