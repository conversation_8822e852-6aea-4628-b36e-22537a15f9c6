"""
Color classification module for MegaCode scanner.

This module provides functions for classifying colors in the extracted grid.
"""

import logging
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass

# Configure logging
logger = logging.getLogger(__name__)

# Try to import scikit-learn
try:
    from sklearn.cluster import KMeans
    HAVE_SKLEARN = True
except ImportError:
    logger.warning("scikit-learn not available, using fallback color classification")
    HAVE_SKLEARN = False


@dataclass
class ColorPalette:
    """Color palette for MegaCode."""
    
    name: str
    colors: List[Tuple[int, int, int]]  # RGB values
    bits_per_symbol: int
    
    def __post_init__(self):
        """Validate the palette after initialization."""
        if len(self.colors) != 2 ** self.bits_per_symbol:
            raise ValueError(
                f"Color palette must have 2^{self.bits_per_symbol} = "
                f"{2 ** self.bits_per_symbol} colors, but has {len(self.colors)}"
            )


# Predefined color palettes
_COLOR_PALETTES = {
    # 1-bit (2 colors): Black and white
    "bw": ColorPalette(
        name="bw",
        colors=[(0, 0, 0), (255, 255, 255)],
        bits_per_symbol=1,
    ),
    
    # 2-bit (4 colors): Black, white, red, blue
    "4-color": ColorPalette(
        name="4-color",
        colors=[
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 0, 255),     # Blue
        ],
        bits_per_symbol=2,
    ),
    
    # 3-bit (8 colors): CMYK + RGB + Black + White
    "8-color": ColorPalette(
        name="8-color",
        colors=[
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 255, 0),     # Green
            (0, 0, 255),     # Blue
            (0, 255, 255),   # Cyan
            (255, 0, 255),   # Magenta
            (255, 255, 0),   # Yellow
        ],
        bits_per_symbol=3,
    ),
    
    # 4-bit (16 colors): Extended palette
    "16-color": ColorPalette(
        name="16-color",
        colors=[
            (0, 0, 0),       # Black
            (255, 255, 255), # White
            (255, 0, 0),     # Red
            (0, 255, 0),     # Green
            (0, 0, 255),     # Blue
            (0, 255, 255),   # Cyan
            (255, 0, 255),   # Magenta
            (255, 255, 0),   # Yellow
            (128, 0, 0),     # Maroon
            (0, 128, 0),     # Dark Green
            (0, 0, 128),     # Navy
            (128, 128, 0),   # Olive
            (128, 0, 128),   # Purple
            (0, 128, 128),   # Teal
            (128, 128, 128), # Gray
            (255, 128, 0),   # Orange
        ],
        bits_per_symbol=4,
    ),
}


class ColorClassifier:
    """Color classifier for MegaCode scanner."""
    
    def __init__(self):
        """Initialize the color classifier."""
        self.palettes = _COLOR_PALETTES
    
    def classify_colors(
        self,
        grid: np.ndarray,
        calibration: Dict[str, Any],
        palette_name: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Classify colors in the grid.
        
        Args:
            grid: Grid of modules as a NumPy array (height, width, 3)
            calibration: Calibration data from GridExtractor
            palette_name: Name of the palette to use (if None, auto-detect)
            
        Returns:
            Dictionary with classification results:
            - 'success': Whether classification was successful
            - 'symbols': Grid of symbol indices
            - 'palette': Detected or used palette
            - 'confidence': Classification confidence (0.0 to 1.0)
            
        Raises:
            ValueError: If the palette is invalid
        """
        # Check if grid is valid
        if grid is None or grid.size == 0:
            return {
                "success": False,
                "symbols": None,
                "palette": None,
                "confidence": 0.0,
            }
        
        # Detect palette if not specified
        if palette_name is None:
            palette_name, confidence = self._detect_palette(grid, calibration)
            logger.info(f"Detected palette: {palette_name} (confidence: {confidence:.2f})")
        else:
            # Validate palette
            if palette_name not in self.palettes:
                raise ValueError(
                    f"Invalid palette: {palette_name}. "
                    f"Available palettes: {list(self.palettes.keys())}"
                )
            confidence = 1.0
        
        # Get palette
        palette = self.palettes[palette_name]
        
        # Calibrate colors if calibration data is available
        if calibration and calibration["colors"]:
            calibrated_palette = self._calibrate_palette(palette, calibration)
        else:
            calibrated_palette = palette
        
        # Classify colors
        symbols = self._classify_grid(grid, calibrated_palette)
        
        return {
            "success": True,
            "symbols": symbols,
            "palette": calibrated_palette,
            "confidence": confidence,
        }
    
    def _detect_palette(
        self,
        grid: np.ndarray,
        calibration: Dict[str, Any],
    ) -> Tuple[str, float]:
        """
        Detect the palette used in the grid.
        
        Args:
            grid: Grid of modules
            calibration: Calibration data
            
        Returns:
            Tuple of (palette_name, confidence)
        """
        # Flatten grid to list of colors
        colors = grid.reshape(-1, 3)
        
        # Count unique colors
        if HAVE_SKLEARN:
            # Use k-means clustering to estimate number of colors
            max_clusters = 16
            inertias = []
            
            for k in range(1, max_clusters + 1):
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                kmeans.fit(colors)
                inertias.append(kmeans.inertia_)
            
            # Find elbow point
            diffs = np.diff(inertias)
            elbow = np.argmax(diffs) + 1
            
            # Ensure at least 2 clusters
            num_colors = max(2, elbow)
        else:
            # Fallback: count unique colors with tolerance
            unique_colors = set()
            for color in colors:
                # Check if color is close to any existing color
                found = False
                for existing in unique_colors:
                    distance = np.sqrt(np.sum((color - existing) ** 2))
                    if distance < 50:  # Tolerance
                        found = True
                        break
                
                if not found:
                    unique_colors.add(tuple(color))
            
            num_colors = len(unique_colors)
        
        # Determine palette based on number of colors
        if num_colors <= 2:
            palette_name = "bw"
            confidence = 0.9
        elif num_colors <= 4:
            palette_name = "4-color"
            confidence = 0.8
        elif num_colors <= 8:
            palette_name = "8-color"
            confidence = 0.7
        else:
            palette_name = "16-color"
            confidence = 0.6
        
        return palette_name, confidence
    
    def _calibrate_palette(
        self,
        palette: ColorPalette,
        calibration: Dict[str, Any],
    ) -> ColorPalette:
        """
        Calibrate palette based on calibration data.
        
        Args:
            palette: Original palette
            calibration: Calibration data
            
        Returns:
            Calibrated palette
        """
        # Get calibration colors
        cal_colors = calibration["colors"]
        std_colors = calibration["standard_colors"]
        
        # Check if we have enough calibration colors
        if len(cal_colors) < 2:
            logger.warning("Not enough calibration colors, using original palette")
            return palette
        
        # Create color mapping
        color_map = {}
        for i, std_color in enumerate(std_colors):
            if i < len(cal_colors):
                color_map[std_color] = cal_colors[i]
        
        # Map palette colors to calibrated colors
        calibrated_colors = []
        for color in palette.colors:
            # Find closest standard color
            closest_std = min(std_colors, key=lambda c: np.sum((np.array(color) - np.array(c)) ** 2))
            
            # Map to calibrated color
            if closest_std in color_map:
                calibrated_colors.append(color_map[closest_std])
            else:
                # If no mapping, use original color
                calibrated_colors.append(color)
        
        # Create calibrated palette
        calibrated_palette = ColorPalette(
            name=palette.name + "_calibrated",
            colors=calibrated_colors,
            bits_per_symbol=palette.bits_per_symbol,
        )
        
        return calibrated_palette
    
    def _classify_grid(
        self,
        grid: np.ndarray,
        palette: ColorPalette,
    ) -> np.ndarray:
        """
        Classify colors in the grid.
        
        Args:
            grid: Grid of modules
            palette: Color palette
            
        Returns:
            Grid of symbol indices
        """
        # Create output grid
        symbols = np.zeros(grid.shape[:2], dtype=np.uint8)
        
        # Classify each module
        for y in range(grid.shape[0]):
            for x in range(grid.shape[1]):
                color = grid[y, x]
                
                # Find closest color in palette
                min_distance = float('inf')
                closest_symbol = 0
                
                for i, palette_color in enumerate(palette.colors):
                    # Euclidean distance in RGB space
                    distance = np.sqrt(np.sum((color - np.array(palette_color)) ** 2))
                    
                    if distance < min_distance:
                        min_distance = distance
                        closest_symbol = i
                
                symbols[y, x] = closest_symbol
        
        return symbols
    
    def symbols_to_bits(
        self,
        symbols: np.ndarray,
        palette: ColorPalette,
    ) -> np.ndarray:
        """
        Convert symbols to bits.
        
        Args:
            symbols: Grid of symbol indices
            palette: Color palette
            
        Returns:
            Array of bits
        """
        # Calculate total number of bits
        total_bits = symbols.size * palette.bits_per_symbol
        
        # Create output array
        bits = np.zeros(total_bits, dtype=np.uint8)
        
        # Convert symbols to bits
        bit_idx = 0
        for y in range(symbols.shape[0]):
            for x in range(symbols.shape[1]):
                symbol = symbols[y, x]
                
                # Convert symbol to bits
                for i in range(palette.bits_per_symbol - 1, -1, -1):
                    bits[bit_idx] = (symbol >> i) & 1
                    bit_idx += 1
        
        return bits
    
    def bits_to_bytes(self, bits: np.ndarray) -> bytes:
        """
        Convert bits to bytes.
        
        Args:
            bits: Array of bits
            
        Returns:
            Bytes
        """
        # Pad bits if necessary
        if len(bits) % 8 != 0:
            padding = 8 - (len(bits) % 8)
            bits = np.concatenate([bits, np.zeros(padding, dtype=np.uint8)])
        
        # Convert bits to bytes
        bytes_data = bytearray()
        for i in range(0, len(bits), 8):
            byte = 0
            for j in range(8):
                byte = (byte << 1) | bits[i + j]
            bytes_data.append(byte)
        
        return bytes(bytes_data)
