# MegaCode Scanner

The MegaCode Scanner is a Python library and command-line tool for decoding high-capacity 2D and 3D codes generated by the MegaCode Encoder.

## Features

- Process images from files or camera input
- Detect and align MegaCode symbols
- Calibrate colors using reference patterns
- Classify modules and extract data
- Apply error correction and fountain code reconstruction
- Command-line interface for easy integration

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Command Line

```bash
python -m megacode.cli.decode --input code.png --output data.txt
```

### As a Library

```python
from megacode.acquisition import load_image
from megacode.detection import detect_code, extract_grid
from megacode.classification import classify_colors
from megacode.decoding import decode_data

# Load and process image
image = load_image("code.png")

# Detect code and extract grid
code_info = detect_code(image)
grid = extract_grid(image, code_info)

# Classify colors to symbols
symbols = classify_colors(grid, code_info.calibration)

# Decode data
data = decode_data(symbols)

# Save or use the decoded data
with open("output.txt", "wb") as f:
    f.write(data)
```

## Architecture

The scanner consists of several modules:

- `acquisition`: Image loading and preprocessing
- `detection`: Code detection and grid extraction
- `classification`: Color classification and symbol mapping
- `decoding`: Data reconstruction and error correction
- `cli`: Command-line interface

## License

MIT
